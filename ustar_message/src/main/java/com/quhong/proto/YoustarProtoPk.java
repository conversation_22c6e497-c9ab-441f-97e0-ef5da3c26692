// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: pk.proto

package com.quhong.proto;

public final class YoustarProtoPk {
  private YoustarProtoPk() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface PKInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:PKInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.PKUserInfo user_info = 1;</code>
     */
    boolean hasUserInfo();
    /**
     * <code>.PKUserInfo user_info = 1;</code>
     */
    com.quhong.proto.YoustarProtoPk.PKUserInfo getUserInfo();
    /**
     * <code>.PKUserInfo user_info = 1;</code>
     */
    com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder getUserInfoOrBuilder();

    /**
     * <code>repeated .PKUserInfo top_list_info = 2;</code>
     */
    java.util.List<com.quhong.proto.YoustarProtoPk.PKUserInfo> 
        getTopListInfoList();
    /**
     * <code>repeated .PKUserInfo top_list_info = 2;</code>
     */
    com.quhong.proto.YoustarProtoPk.PKUserInfo getTopListInfo(int index);
    /**
     * <code>repeated .PKUserInfo top_list_info = 2;</code>
     */
    int getTopListInfoCount();
    /**
     * <code>repeated .PKUserInfo top_list_info = 2;</code>
     */
    java.util.List<? extends com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder> 
        getTopListInfoOrBuilderList();
    /**
     * <code>repeated .PKUserInfo top_list_info = 2;</code>
     */
    com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder getTopListInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code PKInfo}
   */
  public  static final class PKInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:PKInfo)
      PKInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PKInfo.newBuilder() to construct.
    private PKInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PKInfo() {
      topListInfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PKInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder subBuilder = null;
              if (userInfo_ != null) {
                subBuilder = userInfo_.toBuilder();
              }
              userInfo_ = input.readMessage(com.quhong.proto.YoustarProtoPk.PKUserInfo.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(userInfo_);
                userInfo_ = subBuilder.buildPartial();
              }

              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                topListInfo_ = new java.util.ArrayList<com.quhong.proto.YoustarProtoPk.PKUserInfo>();
                mutable_bitField0_ |= 0x00000002;
              }
              topListInfo_.add(
                  input.readMessage(com.quhong.proto.YoustarProtoPk.PKUserInfo.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          topListInfo_ = java.util.Collections.unmodifiableList(topListInfo_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoPk.internal_static_PKInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoPk.internal_static_PKInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoPk.PKInfo.class, com.quhong.proto.YoustarProtoPk.PKInfo.Builder.class);
    }

    private int bitField0_;
    public static final int USER_INFO_FIELD_NUMBER = 1;
    private com.quhong.proto.YoustarProtoPk.PKUserInfo userInfo_;
    /**
     * <code>.PKUserInfo user_info = 1;</code>
     */
    public boolean hasUserInfo() {
      return userInfo_ != null;
    }
    /**
     * <code>.PKUserInfo user_info = 1;</code>
     */
    public com.quhong.proto.YoustarProtoPk.PKUserInfo getUserInfo() {
      return userInfo_ == null ? com.quhong.proto.YoustarProtoPk.PKUserInfo.getDefaultInstance() : userInfo_;
    }
    /**
     * <code>.PKUserInfo user_info = 1;</code>
     */
    public com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder getUserInfoOrBuilder() {
      return getUserInfo();
    }

    public static final int TOP_LIST_INFO_FIELD_NUMBER = 2;
    private java.util.List<com.quhong.proto.YoustarProtoPk.PKUserInfo> topListInfo_;
    /**
     * <code>repeated .PKUserInfo top_list_info = 2;</code>
     */
    public java.util.List<com.quhong.proto.YoustarProtoPk.PKUserInfo> getTopListInfoList() {
      return topListInfo_;
    }
    /**
     * <code>repeated .PKUserInfo top_list_info = 2;</code>
     */
    public java.util.List<? extends com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder> 
        getTopListInfoOrBuilderList() {
      return topListInfo_;
    }
    /**
     * <code>repeated .PKUserInfo top_list_info = 2;</code>
     */
    public int getTopListInfoCount() {
      return topListInfo_.size();
    }
    /**
     * <code>repeated .PKUserInfo top_list_info = 2;</code>
     */
    public com.quhong.proto.YoustarProtoPk.PKUserInfo getTopListInfo(int index) {
      return topListInfo_.get(index);
    }
    /**
     * <code>repeated .PKUserInfo top_list_info = 2;</code>
     */
    public com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder getTopListInfoOrBuilder(
        int index) {
      return topListInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (userInfo_ != null) {
        output.writeMessage(1, getUserInfo());
      }
      for (int i = 0; i < topListInfo_.size(); i++) {
        output.writeMessage(2, topListInfo_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (userInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getUserInfo());
      }
      for (int i = 0; i < topListInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, topListInfo_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoPk.PKInfo)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoPk.PKInfo other = (com.quhong.proto.YoustarProtoPk.PKInfo) obj;

      boolean result = true;
      result = result && (hasUserInfo() == other.hasUserInfo());
      if (hasUserInfo()) {
        result = result && getUserInfo()
            .equals(other.getUserInfo());
      }
      result = result && getTopListInfoList()
          .equals(other.getTopListInfoList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasUserInfo()) {
        hash = (37 * hash) + USER_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getUserInfo().hashCode();
      }
      if (getTopListInfoCount() > 0) {
        hash = (37 * hash) + TOP_LIST_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getTopListInfoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoPk.PKInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoPk.PKInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoPk.PKInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoPk.PKInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoPk.PKInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoPk.PKInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoPk.PKInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoPk.PKInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code PKInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:PKInfo)
        com.quhong.proto.YoustarProtoPk.PKInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoPk.internal_static_PKInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoPk.internal_static_PKInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoPk.PKInfo.class, com.quhong.proto.YoustarProtoPk.PKInfo.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoPk.PKInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTopListInfoFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (userInfoBuilder_ == null) {
          userInfo_ = null;
        } else {
          userInfo_ = null;
          userInfoBuilder_ = null;
        }
        if (topListInfoBuilder_ == null) {
          topListInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          topListInfoBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoPk.internal_static_PKInfo_descriptor;
      }

      public com.quhong.proto.YoustarProtoPk.PKInfo getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoPk.PKInfo.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoPk.PKInfo build() {
        com.quhong.proto.YoustarProtoPk.PKInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoPk.PKInfo buildPartial() {
        com.quhong.proto.YoustarProtoPk.PKInfo result = new com.quhong.proto.YoustarProtoPk.PKInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (userInfoBuilder_ == null) {
          result.userInfo_ = userInfo_;
        } else {
          result.userInfo_ = userInfoBuilder_.build();
        }
        if (topListInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            topListInfo_ = java.util.Collections.unmodifiableList(topListInfo_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.topListInfo_ = topListInfo_;
        } else {
          result.topListInfo_ = topListInfoBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoPk.PKInfo) {
          return mergeFrom((com.quhong.proto.YoustarProtoPk.PKInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoPk.PKInfo other) {
        if (other == com.quhong.proto.YoustarProtoPk.PKInfo.getDefaultInstance()) return this;
        if (other.hasUserInfo()) {
          mergeUserInfo(other.getUserInfo());
        }
        if (topListInfoBuilder_ == null) {
          if (!other.topListInfo_.isEmpty()) {
            if (topListInfo_.isEmpty()) {
              topListInfo_ = other.topListInfo_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureTopListInfoIsMutable();
              topListInfo_.addAll(other.topListInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.topListInfo_.isEmpty()) {
            if (topListInfoBuilder_.isEmpty()) {
              topListInfoBuilder_.dispose();
              topListInfoBuilder_ = null;
              topListInfo_ = other.topListInfo_;
              bitField0_ = (bitField0_ & ~0x00000002);
              topListInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTopListInfoFieldBuilder() : null;
            } else {
              topListInfoBuilder_.addAllMessages(other.topListInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoPk.PKInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoPk.PKInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.quhong.proto.YoustarProtoPk.PKUserInfo userInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoPk.PKUserInfo, com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder, com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder> userInfoBuilder_;
      /**
       * <code>.PKUserInfo user_info = 1;</code>
       */
      public boolean hasUserInfo() {
        return userInfoBuilder_ != null || userInfo_ != null;
      }
      /**
       * <code>.PKUserInfo user_info = 1;</code>
       */
      public com.quhong.proto.YoustarProtoPk.PKUserInfo getUserInfo() {
        if (userInfoBuilder_ == null) {
          return userInfo_ == null ? com.quhong.proto.YoustarProtoPk.PKUserInfo.getDefaultInstance() : userInfo_;
        } else {
          return userInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>.PKUserInfo user_info = 1;</code>
       */
      public Builder setUserInfo(com.quhong.proto.YoustarProtoPk.PKUserInfo value) {
        if (userInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          userInfo_ = value;
          onChanged();
        } else {
          userInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.PKUserInfo user_info = 1;</code>
       */
      public Builder setUserInfo(
          com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder builderForValue) {
        if (userInfoBuilder_ == null) {
          userInfo_ = builderForValue.build();
          onChanged();
        } else {
          userInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.PKUserInfo user_info = 1;</code>
       */
      public Builder mergeUserInfo(com.quhong.proto.YoustarProtoPk.PKUserInfo value) {
        if (userInfoBuilder_ == null) {
          if (userInfo_ != null) {
            userInfo_ =
              com.quhong.proto.YoustarProtoPk.PKUserInfo.newBuilder(userInfo_).mergeFrom(value).buildPartial();
          } else {
            userInfo_ = value;
          }
          onChanged();
        } else {
          userInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.PKUserInfo user_info = 1;</code>
       */
      public Builder clearUserInfo() {
        if (userInfoBuilder_ == null) {
          userInfo_ = null;
          onChanged();
        } else {
          userInfo_ = null;
          userInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.PKUserInfo user_info = 1;</code>
       */
      public com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder getUserInfoBuilder() {
        
        onChanged();
        return getUserInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>.PKUserInfo user_info = 1;</code>
       */
      public com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder getUserInfoOrBuilder() {
        if (userInfoBuilder_ != null) {
          return userInfoBuilder_.getMessageOrBuilder();
        } else {
          return userInfo_ == null ?
              com.quhong.proto.YoustarProtoPk.PKUserInfo.getDefaultInstance() : userInfo_;
        }
      }
      /**
       * <code>.PKUserInfo user_info = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoPk.PKUserInfo, com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder, com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder> 
          getUserInfoFieldBuilder() {
        if (userInfoBuilder_ == null) {
          userInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoPk.PKUserInfo, com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder, com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder>(
                  getUserInfo(),
                  getParentForChildren(),
                  isClean());
          userInfo_ = null;
        }
        return userInfoBuilder_;
      }

      private java.util.List<com.quhong.proto.YoustarProtoPk.PKUserInfo> topListInfo_ =
        java.util.Collections.emptyList();
      private void ensureTopListInfoIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          topListInfo_ = new java.util.ArrayList<com.quhong.proto.YoustarProtoPk.PKUserInfo>(topListInfo_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.quhong.proto.YoustarProtoPk.PKUserInfo, com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder, com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder> topListInfoBuilder_;

      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public java.util.List<com.quhong.proto.YoustarProtoPk.PKUserInfo> getTopListInfoList() {
        if (topListInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(topListInfo_);
        } else {
          return topListInfoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public int getTopListInfoCount() {
        if (topListInfoBuilder_ == null) {
          return topListInfo_.size();
        } else {
          return topListInfoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public com.quhong.proto.YoustarProtoPk.PKUserInfo getTopListInfo(int index) {
        if (topListInfoBuilder_ == null) {
          return topListInfo_.get(index);
        } else {
          return topListInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public Builder setTopListInfo(
          int index, com.quhong.proto.YoustarProtoPk.PKUserInfo value) {
        if (topListInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTopListInfoIsMutable();
          topListInfo_.set(index, value);
          onChanged();
        } else {
          topListInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public Builder setTopListInfo(
          int index, com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder builderForValue) {
        if (topListInfoBuilder_ == null) {
          ensureTopListInfoIsMutable();
          topListInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          topListInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public Builder addTopListInfo(com.quhong.proto.YoustarProtoPk.PKUserInfo value) {
        if (topListInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTopListInfoIsMutable();
          topListInfo_.add(value);
          onChanged();
        } else {
          topListInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public Builder addTopListInfo(
          int index, com.quhong.proto.YoustarProtoPk.PKUserInfo value) {
        if (topListInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTopListInfoIsMutable();
          topListInfo_.add(index, value);
          onChanged();
        } else {
          topListInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public Builder addTopListInfo(
          com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder builderForValue) {
        if (topListInfoBuilder_ == null) {
          ensureTopListInfoIsMutable();
          topListInfo_.add(builderForValue.build());
          onChanged();
        } else {
          topListInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public Builder addTopListInfo(
          int index, com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder builderForValue) {
        if (topListInfoBuilder_ == null) {
          ensureTopListInfoIsMutable();
          topListInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          topListInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public Builder addAllTopListInfo(
          java.lang.Iterable<? extends com.quhong.proto.YoustarProtoPk.PKUserInfo> values) {
        if (topListInfoBuilder_ == null) {
          ensureTopListInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, topListInfo_);
          onChanged();
        } else {
          topListInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public Builder clearTopListInfo() {
        if (topListInfoBuilder_ == null) {
          topListInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          topListInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public Builder removeTopListInfo(int index) {
        if (topListInfoBuilder_ == null) {
          ensureTopListInfoIsMutable();
          topListInfo_.remove(index);
          onChanged();
        } else {
          topListInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder getTopListInfoBuilder(
          int index) {
        return getTopListInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder getTopListInfoOrBuilder(
          int index) {
        if (topListInfoBuilder_ == null) {
          return topListInfo_.get(index);  } else {
          return topListInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public java.util.List<? extends com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder> 
           getTopListInfoOrBuilderList() {
        if (topListInfoBuilder_ != null) {
          return topListInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(topListInfo_);
        }
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder addTopListInfoBuilder() {
        return getTopListInfoFieldBuilder().addBuilder(
            com.quhong.proto.YoustarProtoPk.PKUserInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder addTopListInfoBuilder(
          int index) {
        return getTopListInfoFieldBuilder().addBuilder(
            index, com.quhong.proto.YoustarProtoPk.PKUserInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .PKUserInfo top_list_info = 2;</code>
       */
      public java.util.List<com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder> 
           getTopListInfoBuilderList() {
        return getTopListInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.quhong.proto.YoustarProtoPk.PKUserInfo, com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder, com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder> 
          getTopListInfoFieldBuilder() {
        if (topListInfoBuilder_ == null) {
          topListInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.quhong.proto.YoustarProtoPk.PKUserInfo, com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder, com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder>(
                  topListInfo_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          topListInfo_ = null;
        }
        return topListInfoBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:PKInfo)
    }

    // @@protoc_insertion_point(class_scope:PKInfo)
    private static final com.quhong.proto.YoustarProtoPk.PKInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoPk.PKInfo();
    }

    public static com.quhong.proto.YoustarProtoPk.PKInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PKInfo>
        PARSER = new com.google.protobuf.AbstractParser<PKInfo>() {
      public PKInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new PKInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PKInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PKInfo> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoPk.PKInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PKUserInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:PKUserInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string name = 1;</code>
     */
    java.lang.String getName();
    /**
     * <code>string name = 1;</code>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>string rid = 2;</code>
     */
    java.lang.String getRid();
    /**
     * <code>string rid = 2;</code>
     */
    com.google.protobuf.ByteString
        getRidBytes();

    /**
     * <code>string uid = 3;</code>
     */
    java.lang.String getUid();
    /**
     * <code>string uid = 3;</code>
     */
    com.google.protobuf.ByteString
        getUidBytes();

    /**
     * <code>string head = 4;</code>
     */
    java.lang.String getHead();
    /**
     * <code>string head = 4;</code>
     */
    com.google.protobuf.ByteString
        getHeadBytes();

    /**
     * <code>int32 vip = 5;</code>
     */
    int getVip();

    /**
     * <code>int32 newVipLevel = 6;</code>
     */
    int getNewVipLevel();

    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 7;</code>
     */
    boolean hasRidInfo();
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 7;</code>
     */
    com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo();
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 7;</code>
     */
    com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder();
  }
  /**
   * Protobuf type {@code PKUserInfo}
   */
  public  static final class PKUserInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:PKUserInfo)
      PKUserInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PKUserInfo.newBuilder() to construct.
    private PKUserInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PKUserInfo() {
      name_ = "";
      rid_ = "";
      uid_ = "";
      head_ = "";
      vip_ = 0;
      newVipLevel_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PKUserInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              rid_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              uid_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              head_ = s;
              break;
            }
            case 40: {

              vip_ = input.readInt32();
              break;
            }
            case 48: {

              newVipLevel_ = input.readInt32();
              break;
            }
            case 58: {
              com.quhong.proto.YoustarProtoUname.RidInfo.Builder subBuilder = null;
              if (ridInfo_ != null) {
                subBuilder = ridInfo_.toBuilder();
              }
              ridInfo_ = input.readMessage(com.quhong.proto.YoustarProtoUname.RidInfo.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(ridInfo_);
                ridInfo_ = subBuilder.buildPartial();
              }

              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoPk.internal_static_PKUserInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoPk.internal_static_PKUserInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoPk.PKUserInfo.class, com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder.class);
    }

    public static final int NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 1;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 1;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int RID_FIELD_NUMBER = 2;
    private volatile java.lang.Object rid_;
    /**
     * <code>string rid = 2;</code>
     */
    public java.lang.String getRid() {
      java.lang.Object ref = rid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        rid_ = s;
        return s;
      }
    }
    /**
     * <code>string rid = 2;</code>
     */
    public com.google.protobuf.ByteString
        getRidBytes() {
      java.lang.Object ref = rid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int UID_FIELD_NUMBER = 3;
    private volatile java.lang.Object uid_;
    /**
     * <code>string uid = 3;</code>
     */
    public java.lang.String getUid() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        uid_ = s;
        return s;
      }
    }
    /**
     * <code>string uid = 3;</code>
     */
    public com.google.protobuf.ByteString
        getUidBytes() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int HEAD_FIELD_NUMBER = 4;
    private volatile java.lang.Object head_;
    /**
     * <code>string head = 4;</code>
     */
    public java.lang.String getHead() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        head_ = s;
        return s;
      }
    }
    /**
     * <code>string head = 4;</code>
     */
    public com.google.protobuf.ByteString
        getHeadBytes() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        head_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VIP_FIELD_NUMBER = 5;
    private int vip_;
    /**
     * <code>int32 vip = 5;</code>
     */
    public int getVip() {
      return vip_;
    }

    public static final int NEWVIPLEVEL_FIELD_NUMBER = 6;
    private int newVipLevel_;
    /**
     * <code>int32 newVipLevel = 6;</code>
     */
    public int getNewVipLevel() {
      return newVipLevel_;
    }

    public static final int RIDINFO_FIELD_NUMBER = 7;
    private com.quhong.proto.YoustarProtoUname.RidInfo ridInfo_;
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 7;</code>
     */
    public boolean hasRidInfo() {
      return ridInfo_ != null;
    }
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 7;</code>
     */
    public com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo() {
      return ridInfo_ == null ? com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
    }
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 7;</code>
     */
    public com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder() {
      return getRidInfo();
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
      }
      if (!getRidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, rid_);
      }
      if (!getUidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, uid_);
      }
      if (!getHeadBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, head_);
      }
      if (vip_ != 0) {
        output.writeInt32(5, vip_);
      }
      if (newVipLevel_ != 0) {
        output.writeInt32(6, newVipLevel_);
      }
      if (ridInfo_ != null) {
        output.writeMessage(7, getRidInfo());
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
      }
      if (!getRidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, rid_);
      }
      if (!getUidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, uid_);
      }
      if (!getHeadBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, head_);
      }
      if (vip_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, vip_);
      }
      if (newVipLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, newVipLevel_);
      }
      if (ridInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getRidInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoPk.PKUserInfo)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoPk.PKUserInfo other = (com.quhong.proto.YoustarProtoPk.PKUserInfo) obj;

      boolean result = true;
      result = result && getName()
          .equals(other.getName());
      result = result && getRid()
          .equals(other.getRid());
      result = result && getUid()
          .equals(other.getUid());
      result = result && getHead()
          .equals(other.getHead());
      result = result && (getVip()
          == other.getVip());
      result = result && (getNewVipLevel()
          == other.getNewVipLevel());
      result = result && (hasRidInfo() == other.hasRidInfo());
      if (hasRidInfo()) {
        result = result && getRidInfo()
            .equals(other.getRidInfo());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + RID_FIELD_NUMBER;
      hash = (53 * hash) + getRid().hashCode();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + getUid().hashCode();
      hash = (37 * hash) + HEAD_FIELD_NUMBER;
      hash = (53 * hash) + getHead().hashCode();
      hash = (37 * hash) + VIP_FIELD_NUMBER;
      hash = (53 * hash) + getVip();
      hash = (37 * hash) + NEWVIPLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getNewVipLevel();
      if (hasRidInfo()) {
        hash = (37 * hash) + RIDINFO_FIELD_NUMBER;
        hash = (53 * hash) + getRidInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoPk.PKUserInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoPk.PKUserInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKUserInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoPk.PKUserInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKUserInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoPk.PKUserInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKUserInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoPk.PKUserInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKUserInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoPk.PKUserInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKUserInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoPk.PKUserInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoPk.PKUserInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code PKUserInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:PKUserInfo)
        com.quhong.proto.YoustarProtoPk.PKUserInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoPk.internal_static_PKUserInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoPk.internal_static_PKUserInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoPk.PKUserInfo.class, com.quhong.proto.YoustarProtoPk.PKUserInfo.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoPk.PKUserInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        name_ = "";

        rid_ = "";

        uid_ = "";

        head_ = "";

        vip_ = 0;

        newVipLevel_ = 0;

        if (ridInfoBuilder_ == null) {
          ridInfo_ = null;
        } else {
          ridInfo_ = null;
          ridInfoBuilder_ = null;
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoPk.internal_static_PKUserInfo_descriptor;
      }

      public com.quhong.proto.YoustarProtoPk.PKUserInfo getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoPk.PKUserInfo.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoPk.PKUserInfo build() {
        com.quhong.proto.YoustarProtoPk.PKUserInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoPk.PKUserInfo buildPartial() {
        com.quhong.proto.YoustarProtoPk.PKUserInfo result = new com.quhong.proto.YoustarProtoPk.PKUserInfo(this);
        result.name_ = name_;
        result.rid_ = rid_;
        result.uid_ = uid_;
        result.head_ = head_;
        result.vip_ = vip_;
        result.newVipLevel_ = newVipLevel_;
        if (ridInfoBuilder_ == null) {
          result.ridInfo_ = ridInfo_;
        } else {
          result.ridInfo_ = ridInfoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoPk.PKUserInfo) {
          return mergeFrom((com.quhong.proto.YoustarProtoPk.PKUserInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoPk.PKUserInfo other) {
        if (other == com.quhong.proto.YoustarProtoPk.PKUserInfo.getDefaultInstance()) return this;
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (!other.getRid().isEmpty()) {
          rid_ = other.rid_;
          onChanged();
        }
        if (!other.getUid().isEmpty()) {
          uid_ = other.uid_;
          onChanged();
        }
        if (!other.getHead().isEmpty()) {
          head_ = other.head_;
          onChanged();
        }
        if (other.getVip() != 0) {
          setVip(other.getVip());
        }
        if (other.getNewVipLevel() != 0) {
          setNewVipLevel(other.getNewVipLevel());
        }
        if (other.hasRidInfo()) {
          mergeRidInfo(other.getRidInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoPk.PKUserInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoPk.PKUserInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 1;</code>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 1;</code>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 1;</code>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 1;</code>
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 1;</code>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object rid_ = "";
      /**
       * <code>string rid = 2;</code>
       */
      public java.lang.String getRid() {
        java.lang.Object ref = rid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          rid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string rid = 2;</code>
       */
      public com.google.protobuf.ByteString
          getRidBytes() {
        java.lang.Object ref = rid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          rid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string rid = 2;</code>
       */
      public Builder setRid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        rid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string rid = 2;</code>
       */
      public Builder clearRid() {
        
        rid_ = getDefaultInstance().getRid();
        onChanged();
        return this;
      }
      /**
       * <code>string rid = 2;</code>
       */
      public Builder setRidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        rid_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object uid_ = "";
      /**
       * <code>string uid = 3;</code>
       */
      public java.lang.String getUid() {
        java.lang.Object ref = uid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          uid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string uid = 3;</code>
       */
      public com.google.protobuf.ByteString
          getUidBytes() {
        java.lang.Object ref = uid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          uid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string uid = 3;</code>
       */
      public Builder setUid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string uid = 3;</code>
       */
      public Builder clearUid() {
        
        uid_ = getDefaultInstance().getUid();
        onChanged();
        return this;
      }
      /**
       * <code>string uid = 3;</code>
       */
      public Builder setUidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        uid_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object head_ = "";
      /**
       * <code>string head = 4;</code>
       */
      public java.lang.String getHead() {
        java.lang.Object ref = head_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          head_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string head = 4;</code>
       */
      public com.google.protobuf.ByteString
          getHeadBytes() {
        java.lang.Object ref = head_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          head_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string head = 4;</code>
       */
      public Builder setHead(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        head_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string head = 4;</code>
       */
      public Builder clearHead() {
        
        head_ = getDefaultInstance().getHead();
        onChanged();
        return this;
      }
      /**
       * <code>string head = 4;</code>
       */
      public Builder setHeadBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        head_ = value;
        onChanged();
        return this;
      }

      private int vip_ ;
      /**
       * <code>int32 vip = 5;</code>
       */
      public int getVip() {
        return vip_;
      }
      /**
       * <code>int32 vip = 5;</code>
       */
      public Builder setVip(int value) {
        
        vip_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 vip = 5;</code>
       */
      public Builder clearVip() {
        
        vip_ = 0;
        onChanged();
        return this;
      }

      private int newVipLevel_ ;
      /**
       * <code>int32 newVipLevel = 6;</code>
       */
      public int getNewVipLevel() {
        return newVipLevel_;
      }
      /**
       * <code>int32 newVipLevel = 6;</code>
       */
      public Builder setNewVipLevel(int value) {
        
        newVipLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 newVipLevel = 6;</code>
       */
      public Builder clearNewVipLevel() {
        
        newVipLevel_ = 0;
        onChanged();
        return this;
      }

      private com.quhong.proto.YoustarProtoUname.RidInfo ridInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder> ridInfoBuilder_;
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public boolean hasRidInfo() {
        return ridInfoBuilder_ != null || ridInfo_ != null;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo() {
        if (ridInfoBuilder_ == null) {
          return ridInfo_ == null ? com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
        } else {
          return ridInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public Builder setRidInfo(com.quhong.proto.YoustarProtoUname.RidInfo value) {
        if (ridInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ridInfo_ = value;
          onChanged();
        } else {
          ridInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public Builder setRidInfo(
          com.quhong.proto.YoustarProtoUname.RidInfo.Builder builderForValue) {
        if (ridInfoBuilder_ == null) {
          ridInfo_ = builderForValue.build();
          onChanged();
        } else {
          ridInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public Builder mergeRidInfo(com.quhong.proto.YoustarProtoUname.RidInfo value) {
        if (ridInfoBuilder_ == null) {
          if (ridInfo_ != null) {
            ridInfo_ =
              com.quhong.proto.YoustarProtoUname.RidInfo.newBuilder(ridInfo_).mergeFrom(value).buildPartial();
          } else {
            ridInfo_ = value;
          }
          onChanged();
        } else {
          ridInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public Builder clearRidInfo() {
        if (ridInfoBuilder_ == null) {
          ridInfo_ = null;
          onChanged();
        } else {
          ridInfo_ = null;
          ridInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfo.Builder getRidInfoBuilder() {
        
        onChanged();
        return getRidInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder() {
        if (ridInfoBuilder_ != null) {
          return ridInfoBuilder_.getMessageOrBuilder();
        } else {
          return ridInfo_ == null ?
              com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
        }
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder> 
          getRidInfoFieldBuilder() {
        if (ridInfoBuilder_ == null) {
          ridInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder>(
                  getRidInfo(),
                  getParentForChildren(),
                  isClean());
          ridInfo_ = null;
        }
        return ridInfoBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:PKUserInfo)
    }

    // @@protoc_insertion_point(class_scope:PKUserInfo)
    private static final com.quhong.proto.YoustarProtoPk.PKUserInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoPk.PKUserInfo();
    }

    public static com.quhong.proto.YoustarProtoPk.PKUserInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PKUserInfo>
        PARSER = new com.google.protobuf.AbstractParser<PKUserInfo>() {
      public PKUserInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new PKUserInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PKUserInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PKUserInfo> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoPk.PKUserInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PKExtraInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:PKExtraInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 cancel_type = 1;</code>
     */
    int getCancelType();

    /**
     * <code>string operator = 2;</code>
     */
    java.lang.String getOperator();
    /**
     * <code>string operator = 2;</code>
     */
    com.google.protobuf.ByteString
        getOperatorBytes();
  }
  /**
   * Protobuf type {@code PKExtraInfo}
   */
  public  static final class PKExtraInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:PKExtraInfo)
      PKExtraInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PKExtraInfo.newBuilder() to construct.
    private PKExtraInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PKExtraInfo() {
      cancelType_ = 0;
      operator_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PKExtraInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              cancelType_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              operator_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoPk.internal_static_PKExtraInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoPk.internal_static_PKExtraInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoPk.PKExtraInfo.class, com.quhong.proto.YoustarProtoPk.PKExtraInfo.Builder.class);
    }

    public static final int CANCEL_TYPE_FIELD_NUMBER = 1;
    private int cancelType_;
    /**
     * <code>int32 cancel_type = 1;</code>
     */
    public int getCancelType() {
      return cancelType_;
    }

    public static final int OPERATOR_FIELD_NUMBER = 2;
    private volatile java.lang.Object operator_;
    /**
     * <code>string operator = 2;</code>
     */
    public java.lang.String getOperator() {
      java.lang.Object ref = operator_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        operator_ = s;
        return s;
      }
    }
    /**
     * <code>string operator = 2;</code>
     */
    public com.google.protobuf.ByteString
        getOperatorBytes() {
      java.lang.Object ref = operator_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        operator_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (cancelType_ != 0) {
        output.writeInt32(1, cancelType_);
      }
      if (!getOperatorBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, operator_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (cancelType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, cancelType_);
      }
      if (!getOperatorBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, operator_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoPk.PKExtraInfo)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoPk.PKExtraInfo other = (com.quhong.proto.YoustarProtoPk.PKExtraInfo) obj;

      boolean result = true;
      result = result && (getCancelType()
          == other.getCancelType());
      result = result && getOperator()
          .equals(other.getOperator());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CANCEL_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getCancelType();
      hash = (37 * hash) + OPERATOR_FIELD_NUMBER;
      hash = (53 * hash) + getOperator().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoPk.PKExtraInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code PKExtraInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:PKExtraInfo)
        com.quhong.proto.YoustarProtoPk.PKExtraInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoPk.internal_static_PKExtraInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoPk.internal_static_PKExtraInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoPk.PKExtraInfo.class, com.quhong.proto.YoustarProtoPk.PKExtraInfo.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoPk.PKExtraInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        cancelType_ = 0;

        operator_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoPk.internal_static_PKExtraInfo_descriptor;
      }

      public com.quhong.proto.YoustarProtoPk.PKExtraInfo getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoPk.PKExtraInfo.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoPk.PKExtraInfo build() {
        com.quhong.proto.YoustarProtoPk.PKExtraInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoPk.PKExtraInfo buildPartial() {
        com.quhong.proto.YoustarProtoPk.PKExtraInfo result = new com.quhong.proto.YoustarProtoPk.PKExtraInfo(this);
        result.cancelType_ = cancelType_;
        result.operator_ = operator_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoPk.PKExtraInfo) {
          return mergeFrom((com.quhong.proto.YoustarProtoPk.PKExtraInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoPk.PKExtraInfo other) {
        if (other == com.quhong.proto.YoustarProtoPk.PKExtraInfo.getDefaultInstance()) return this;
        if (other.getCancelType() != 0) {
          setCancelType(other.getCancelType());
        }
        if (!other.getOperator().isEmpty()) {
          operator_ = other.operator_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoPk.PKExtraInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoPk.PKExtraInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int cancelType_ ;
      /**
       * <code>int32 cancel_type = 1;</code>
       */
      public int getCancelType() {
        return cancelType_;
      }
      /**
       * <code>int32 cancel_type = 1;</code>
       */
      public Builder setCancelType(int value) {
        
        cancelType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 cancel_type = 1;</code>
       */
      public Builder clearCancelType() {
        
        cancelType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object operator_ = "";
      /**
       * <code>string operator = 2;</code>
       */
      public java.lang.String getOperator() {
        java.lang.Object ref = operator_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          operator_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string operator = 2;</code>
       */
      public com.google.protobuf.ByteString
          getOperatorBytes() {
        java.lang.Object ref = operator_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          operator_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string operator = 2;</code>
       */
      public Builder setOperator(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        operator_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string operator = 2;</code>
       */
      public Builder clearOperator() {
        
        operator_ = getDefaultInstance().getOperator();
        onChanged();
        return this;
      }
      /**
       * <code>string operator = 2;</code>
       */
      public Builder setOperatorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        operator_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:PKExtraInfo)
    }

    // @@protoc_insertion_point(class_scope:PKExtraInfo)
    private static final com.quhong.proto.YoustarProtoPk.PKExtraInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoPk.PKExtraInfo();
    }

    public static com.quhong.proto.YoustarProtoPk.PKExtraInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PKExtraInfo>
        PARSER = new com.google.protobuf.AbstractParser<PKExtraInfo>() {
      public PKExtraInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new PKExtraInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PKExtraInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PKExtraInfo> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoPk.PKExtraInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_PKInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_PKInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_PKUserInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_PKUserInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_PKExtraInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_PKExtraInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\010pk.proto\032\013uname.proto\"L\n\006PKInfo\022\036\n\tuse" +
      "r_info\030\001 \001(\0132\013.PKUserInfo\022\"\n\rtop_list_in" +
      "fo\030\002 \003(\0132\013.PKUserInfo\"\177\n\nPKUserInfo\022\014\n\004n" +
      "ame\030\001 \001(\t\022\013\n\003rid\030\002 \001(\t\022\013\n\003uid\030\003 \001(\t\022\014\n\004h" +
      "ead\030\004 \001(\t\022\013\n\003vip\030\005 \001(\005\022\023\n\013newVipLevel\030\006 " +
      "\001(\005\022\031\n\007ridInfo\030\007 \001(\0132\010.RidInfo\"4\n\013PKExtr" +
      "aInfo\022\023\n\013cancel_type\030\001 \001(\005\022\020\n\010operator\030\002" +
      " \001(\tB\"\n\020com.quhong.protoB\016YoustarProtoPk" +
      "b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.quhong.proto.YoustarProtoUname.getDescriptor(),
        }, assigner);
    internal_static_PKInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_PKInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_PKInfo_descriptor,
        new java.lang.String[] { "UserInfo", "TopListInfo", });
    internal_static_PKUserInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_PKUserInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_PKUserInfo_descriptor,
        new java.lang.String[] { "Name", "Rid", "Uid", "Head", "Vip", "NewVipLevel", "RidInfo", });
    internal_static_PKExtraInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_PKExtraInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_PKExtraInfo_descriptor,
        new java.lang.String[] { "CancelType", "Operator", });
    com.quhong.proto.YoustarProtoUname.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
