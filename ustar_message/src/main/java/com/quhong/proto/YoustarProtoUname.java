// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: uname.proto

package com.quhong.proto;

public final class YoustarProtoUname {
  private YoustarProtoUname() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface UnameOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Uname)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string name = 1;</code>
     */
    java.lang.String getName();
    /**
     * <code>string name = 1;</code>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>int32 vip = 2;</code>
     */
    int getVip();

    /**
     * <code>int32 level = 3;</code>
     */
    int getLevel();

    /**
     * <code>repeated string badge = 4;</code>
     */
    java.util.List<java.lang.String>
        getBadgeList();
    /**
     * <code>repeated string badge = 4;</code>
     */
    int getBadgeCount();
    /**
     * <code>repeated string badge = 4;</code>
     */
    java.lang.String getBadge(int index);
    /**
     * <code>repeated string badge = 4;</code>
     */
    com.google.protobuf.ByteString
        getBadgeBytes(int index);

    /**
     * <code>string head = 5;</code>
     */
    java.lang.String getHead();
    /**
     * <code>string head = 5;</code>
     */
    com.google.protobuf.ByteString
        getHeadBytes();

    /**
     * <pre>
     *气泡id
     * </pre>
     *
     * <code>int32 bid = 6;</code>
     */
    int getBid();

    /**
     * <pre>
     * 0 无 1 vip 2 svip
     * </pre>
     *
     * <code>int32 identify = 7;</code>
     */
    int getIdentify();

    /**
     * <pre>
     * 用户rid
     * </pre>
     *
     * <code>int32 rid = 8;</code>
     */
    int getRid();

    /**
     * <pre>
     * </pre>
     *
     * <code>string uid = 9;</code>
     */
    java.lang.String getUid();
    /**
     * <pre>
     * </pre>
     *
     * <code>string uid = 9;</code>
     */
    com.google.protobuf.ByteString
        getUidBytes();

    /**
     * <pre>
     *当前房间的身份（0不显示，1房主2副房主3管理员4会员）
     * </pre>
     *
     * <code>int32 role = 10;</code>
     */
    int getRole();

    /**
     * <code>int32 newVipLevel = 11;</code>
     */
    int getNewVipLevel();

    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 12;</code>
     */
    boolean hasRidInfo();
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 12;</code>
     */
    com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo();
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 12;</code>
     */
    com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder();

    /**
     * <pre>
     *是否为新用户 0 不是 1 是
     * </pre>
     *
     * <code>int32 isNewUser = 13;</code>
     */
    int getIsNewUser();
  }
  /**
   * <pre>
   *礼物接收者信息
   * </pre>
   *
   * Protobuf type {@code Uname}
   */
  public  static final class Uname extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Uname)
      UnameOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Uname.newBuilder() to construct.
    private Uname(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Uname() {
      name_ = "";
      vip_ = 0;
      level_ = 0;
      badge_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      head_ = "";
      bid_ = 0;
      identify_ = 0;
      rid_ = 0;
      uid_ = "";
      role_ = 0;
      newVipLevel_ = 0;
      isNewUser_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Uname(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 16: {

              vip_ = input.readInt32();
              break;
            }
            case 24: {

              level_ = input.readInt32();
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                badge_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000008;
              }
              badge_.add(s);
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              head_ = s;
              break;
            }
            case 48: {

              bid_ = input.readInt32();
              break;
            }
            case 56: {

              identify_ = input.readInt32();
              break;
            }
            case 64: {

              rid_ = input.readInt32();
              break;
            }
            case 74: {
              java.lang.String s = input.readStringRequireUtf8();

              uid_ = s;
              break;
            }
            case 80: {

              role_ = input.readInt32();
              break;
            }
            case 88: {

              newVipLevel_ = input.readInt32();
              break;
            }
            case 98: {
              com.quhong.proto.YoustarProtoUname.RidInfo.Builder subBuilder = null;
              if (ridInfo_ != null) {
                subBuilder = ridInfo_.toBuilder();
              }
              ridInfo_ = input.readMessage(com.quhong.proto.YoustarProtoUname.RidInfo.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(ridInfo_);
                ridInfo_ = subBuilder.buildPartial();
              }

              break;
            }
            case 104: {

              isNewUser_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          badge_ = badge_.getUnmodifiableView();
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoUname.internal_static_Uname_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoUname.internal_static_Uname_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoUname.Uname.class, com.quhong.proto.YoustarProtoUname.Uname.Builder.class);
    }

    private int bitField0_;
    public static final int NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 1;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 1;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VIP_FIELD_NUMBER = 2;
    private int vip_;
    /**
     * <code>int32 vip = 2;</code>
     */
    public int getVip() {
      return vip_;
    }

    public static final int LEVEL_FIELD_NUMBER = 3;
    private int level_;
    /**
     * <code>int32 level = 3;</code>
     */
    public int getLevel() {
      return level_;
    }

    public static final int BADGE_FIELD_NUMBER = 4;
    private com.google.protobuf.LazyStringList badge_;
    /**
     * <code>repeated string badge = 4;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getBadgeList() {
      return badge_;
    }
    /**
     * <code>repeated string badge = 4;</code>
     */
    public int getBadgeCount() {
      return badge_.size();
    }
    /**
     * <code>repeated string badge = 4;</code>
     */
    public java.lang.String getBadge(int index) {
      return badge_.get(index);
    }
    /**
     * <code>repeated string badge = 4;</code>
     */
    public com.google.protobuf.ByteString
        getBadgeBytes(int index) {
      return badge_.getByteString(index);
    }

    public static final int HEAD_FIELD_NUMBER = 5;
    private volatile java.lang.Object head_;
    /**
     * <code>string head = 5;</code>
     */
    public java.lang.String getHead() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        head_ = s;
        return s;
      }
    }
    /**
     * <code>string head = 5;</code>
     */
    public com.google.protobuf.ByteString
        getHeadBytes() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        head_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BID_FIELD_NUMBER = 6;
    private int bid_;
    /**
     * <pre>
     *气泡id
     * </pre>
     *
     * <code>int32 bid = 6;</code>
     */
    public int getBid() {
      return bid_;
    }

    public static final int IDENTIFY_FIELD_NUMBER = 7;
    private int identify_;
    /**
     * <pre>
     * 0 无 1 vip 2 svip
     * </pre>
     *
     * <code>int32 identify = 7;</code>
     */
    public int getIdentify() {
      return identify_;
    }

    public static final int RID_FIELD_NUMBER = 8;
    private int rid_;
    /**
     * <pre>
     * 用户rid
     * </pre>
     *
     * <code>int32 rid = 8;</code>
     */
    public int getRid() {
      return rid_;
    }

    public static final int UID_FIELD_NUMBER = 9;
    private volatile java.lang.Object uid_;
    /**
     * <pre>
     * </pre>
     *
     * <code>string uid = 9;</code>
     */
    public java.lang.String getUid() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        uid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>string uid = 9;</code>
     */
    public com.google.protobuf.ByteString
        getUidBytes() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ROLE_FIELD_NUMBER = 10;
    private int role_;
    /**
     * <pre>
     *当前房间的身份（0不显示，1房主2副房主3管理员4会员）
     * </pre>
     *
     * <code>int32 role = 10;</code>
     */
    public int getRole() {
      return role_;
    }

    public static final int NEWVIPLEVEL_FIELD_NUMBER = 11;
    private int newVipLevel_;
    /**
     * <code>int32 newVipLevel = 11;</code>
     */
    public int getNewVipLevel() {
      return newVipLevel_;
    }

    public static final int RIDINFO_FIELD_NUMBER = 12;
    private com.quhong.proto.YoustarProtoUname.RidInfo ridInfo_;
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 12;</code>
     */
    public boolean hasRidInfo() {
      return ridInfo_ != null;
    }
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 12;</code>
     */
    public com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo() {
      return ridInfo_ == null ? com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
    }
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 12;</code>
     */
    public com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder() {
      return getRidInfo();
    }

    public static final int ISNEWUSER_FIELD_NUMBER = 13;
    private int isNewUser_;
    /**
     * <pre>
     *是否为新用户 0 不是 1 是
     * </pre>
     *
     * <code>int32 isNewUser = 13;</code>
     */
    public int getIsNewUser() {
      return isNewUser_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
      }
      if (vip_ != 0) {
        output.writeInt32(2, vip_);
      }
      if (level_ != 0) {
        output.writeInt32(3, level_);
      }
      for (int i = 0; i < badge_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, badge_.getRaw(i));
      }
      if (!getHeadBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, head_);
      }
      if (bid_ != 0) {
        output.writeInt32(6, bid_);
      }
      if (identify_ != 0) {
        output.writeInt32(7, identify_);
      }
      if (rid_ != 0) {
        output.writeInt32(8, rid_);
      }
      if (!getUidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, uid_);
      }
      if (role_ != 0) {
        output.writeInt32(10, role_);
      }
      if (newVipLevel_ != 0) {
        output.writeInt32(11, newVipLevel_);
      }
      if (ridInfo_ != null) {
        output.writeMessage(12, getRidInfo());
      }
      if (isNewUser_ != 0) {
        output.writeInt32(13, isNewUser_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
      }
      if (vip_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, vip_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, level_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < badge_.size(); i++) {
          dataSize += computeStringSizeNoTag(badge_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getBadgeList().size();
      }
      if (!getHeadBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, head_);
      }
      if (bid_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, bid_);
      }
      if (identify_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, identify_);
      }
      if (rid_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, rid_);
      }
      if (!getUidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, uid_);
      }
      if (role_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, role_);
      }
      if (newVipLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, newVipLevel_);
      }
      if (ridInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, getRidInfo());
      }
      if (isNewUser_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(13, isNewUser_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoUname.Uname)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoUname.Uname other = (com.quhong.proto.YoustarProtoUname.Uname) obj;

      boolean result = true;
      result = result && getName()
          .equals(other.getName());
      result = result && (getVip()
          == other.getVip());
      result = result && (getLevel()
          == other.getLevel());
      result = result && getBadgeList()
          .equals(other.getBadgeList());
      result = result && getHead()
          .equals(other.getHead());
      result = result && (getBid()
          == other.getBid());
      result = result && (getIdentify()
          == other.getIdentify());
      result = result && (getRid()
          == other.getRid());
      result = result && getUid()
          .equals(other.getUid());
      result = result && (getRole()
          == other.getRole());
      result = result && (getNewVipLevel()
          == other.getNewVipLevel());
      result = result && (hasRidInfo() == other.hasRidInfo());
      if (hasRidInfo()) {
        result = result && getRidInfo()
            .equals(other.getRidInfo());
      }
      result = result && (getIsNewUser()
          == other.getIsNewUser());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + VIP_FIELD_NUMBER;
      hash = (53 * hash) + getVip();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      if (getBadgeCount() > 0) {
        hash = (37 * hash) + BADGE_FIELD_NUMBER;
        hash = (53 * hash) + getBadgeList().hashCode();
      }
      hash = (37 * hash) + HEAD_FIELD_NUMBER;
      hash = (53 * hash) + getHead().hashCode();
      hash = (37 * hash) + BID_FIELD_NUMBER;
      hash = (53 * hash) + getBid();
      hash = (37 * hash) + IDENTIFY_FIELD_NUMBER;
      hash = (53 * hash) + getIdentify();
      hash = (37 * hash) + RID_FIELD_NUMBER;
      hash = (53 * hash) + getRid();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + getUid().hashCode();
      hash = (37 * hash) + ROLE_FIELD_NUMBER;
      hash = (53 * hash) + getRole();
      hash = (37 * hash) + NEWVIPLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getNewVipLevel();
      if (hasRidInfo()) {
        hash = (37 * hash) + RIDINFO_FIELD_NUMBER;
        hash = (53 * hash) + getRidInfo().hashCode();
      }
      hash = (37 * hash) + ISNEWUSER_FIELD_NUMBER;
      hash = (53 * hash) + getIsNewUser();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoUname.Uname parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.Uname parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.Uname parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.Uname parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.Uname parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.Uname parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.Uname parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.Uname parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.Uname parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.Uname parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.Uname parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.Uname parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoUname.Uname prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *礼物接收者信息
     * </pre>
     *
     * Protobuf type {@code Uname}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Uname)
        com.quhong.proto.YoustarProtoUname.UnameOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoUname.internal_static_Uname_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoUname.internal_static_Uname_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoUname.Uname.class, com.quhong.proto.YoustarProtoUname.Uname.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoUname.Uname.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        name_ = "";

        vip_ = 0;

        level_ = 0;

        badge_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        head_ = "";

        bid_ = 0;

        identify_ = 0;

        rid_ = 0;

        uid_ = "";

        role_ = 0;

        newVipLevel_ = 0;

        if (ridInfoBuilder_ == null) {
          ridInfo_ = null;
        } else {
          ridInfo_ = null;
          ridInfoBuilder_ = null;
        }
        isNewUser_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoUname.internal_static_Uname_descriptor;
      }

      public com.quhong.proto.YoustarProtoUname.Uname getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoUname.Uname.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoUname.Uname build() {
        com.quhong.proto.YoustarProtoUname.Uname result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoUname.Uname buildPartial() {
        com.quhong.proto.YoustarProtoUname.Uname result = new com.quhong.proto.YoustarProtoUname.Uname(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.name_ = name_;
        result.vip_ = vip_;
        result.level_ = level_;
        if (((bitField0_ & 0x00000008) == 0x00000008)) {
          badge_ = badge_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.badge_ = badge_;
        result.head_ = head_;
        result.bid_ = bid_;
        result.identify_ = identify_;
        result.rid_ = rid_;
        result.uid_ = uid_;
        result.role_ = role_;
        result.newVipLevel_ = newVipLevel_;
        if (ridInfoBuilder_ == null) {
          result.ridInfo_ = ridInfo_;
        } else {
          result.ridInfo_ = ridInfoBuilder_.build();
        }
        result.isNewUser_ = isNewUser_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoUname.Uname) {
          return mergeFrom((com.quhong.proto.YoustarProtoUname.Uname)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoUname.Uname other) {
        if (other == com.quhong.proto.YoustarProtoUname.Uname.getDefaultInstance()) return this;
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (other.getVip() != 0) {
          setVip(other.getVip());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        if (!other.badge_.isEmpty()) {
          if (badge_.isEmpty()) {
            badge_ = other.badge_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureBadgeIsMutable();
            badge_.addAll(other.badge_);
          }
          onChanged();
        }
        if (!other.getHead().isEmpty()) {
          head_ = other.head_;
          onChanged();
        }
        if (other.getBid() != 0) {
          setBid(other.getBid());
        }
        if (other.getIdentify() != 0) {
          setIdentify(other.getIdentify());
        }
        if (other.getRid() != 0) {
          setRid(other.getRid());
        }
        if (!other.getUid().isEmpty()) {
          uid_ = other.uid_;
          onChanged();
        }
        if (other.getRole() != 0) {
          setRole(other.getRole());
        }
        if (other.getNewVipLevel() != 0) {
          setNewVipLevel(other.getNewVipLevel());
        }
        if (other.hasRidInfo()) {
          mergeRidInfo(other.getRidInfo());
        }
        if (other.getIsNewUser() != 0) {
          setIsNewUser(other.getIsNewUser());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoUname.Uname parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoUname.Uname) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 1;</code>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 1;</code>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 1;</code>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 1;</code>
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 1;</code>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private int vip_ ;
      /**
       * <code>int32 vip = 2;</code>
       */
      public int getVip() {
        return vip_;
      }
      /**
       * <code>int32 vip = 2;</code>
       */
      public Builder setVip(int value) {
        
        vip_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 vip = 2;</code>
       */
      public Builder clearVip() {
        
        vip_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <code>int32 level = 3;</code>
       */
      public int getLevel() {
        return level_;
      }
      /**
       * <code>int32 level = 3;</code>
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 level = 3;</code>
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList badge_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureBadgeIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          badge_ = new com.google.protobuf.LazyStringArrayList(badge_);
          bitField0_ |= 0x00000008;
         }
      }
      /**
       * <code>repeated string badge = 4;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getBadgeList() {
        return badge_.getUnmodifiableView();
      }
      /**
       * <code>repeated string badge = 4;</code>
       */
      public int getBadgeCount() {
        return badge_.size();
      }
      /**
       * <code>repeated string badge = 4;</code>
       */
      public java.lang.String getBadge(int index) {
        return badge_.get(index);
      }
      /**
       * <code>repeated string badge = 4;</code>
       */
      public com.google.protobuf.ByteString
          getBadgeBytes(int index) {
        return badge_.getByteString(index);
      }
      /**
       * <code>repeated string badge = 4;</code>
       */
      public Builder setBadge(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureBadgeIsMutable();
        badge_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string badge = 4;</code>
       */
      public Builder addBadge(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureBadgeIsMutable();
        badge_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string badge = 4;</code>
       */
      public Builder addAllBadge(
          java.lang.Iterable<java.lang.String> values) {
        ensureBadgeIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, badge_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string badge = 4;</code>
       */
      public Builder clearBadge() {
        badge_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string badge = 4;</code>
       */
      public Builder addBadgeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        ensureBadgeIsMutable();
        badge_.add(value);
        onChanged();
        return this;
      }

      private java.lang.Object head_ = "";
      /**
       * <code>string head = 5;</code>
       */
      public java.lang.String getHead() {
        java.lang.Object ref = head_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          head_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string head = 5;</code>
       */
      public com.google.protobuf.ByteString
          getHeadBytes() {
        java.lang.Object ref = head_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          head_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string head = 5;</code>
       */
      public Builder setHead(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        head_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string head = 5;</code>
       */
      public Builder clearHead() {
        
        head_ = getDefaultInstance().getHead();
        onChanged();
        return this;
      }
      /**
       * <code>string head = 5;</code>
       */
      public Builder setHeadBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        head_ = value;
        onChanged();
        return this;
      }

      private int bid_ ;
      /**
       * <pre>
       *气泡id
       * </pre>
       *
       * <code>int32 bid = 6;</code>
       */
      public int getBid() {
        return bid_;
      }
      /**
       * <pre>
       *气泡id
       * </pre>
       *
       * <code>int32 bid = 6;</code>
       */
      public Builder setBid(int value) {
        
        bid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *气泡id
       * </pre>
       *
       * <code>int32 bid = 6;</code>
       */
      public Builder clearBid() {
        
        bid_ = 0;
        onChanged();
        return this;
      }

      private int identify_ ;
      /**
       * <pre>
       * 0 无 1 vip 2 svip
       * </pre>
       *
       * <code>int32 identify = 7;</code>
       */
      public int getIdentify() {
        return identify_;
      }
      /**
       * <pre>
       * 0 无 1 vip 2 svip
       * </pre>
       *
       * <code>int32 identify = 7;</code>
       */
      public Builder setIdentify(int value) {
        
        identify_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 0 无 1 vip 2 svip
       * </pre>
       *
       * <code>int32 identify = 7;</code>
       */
      public Builder clearIdentify() {
        
        identify_ = 0;
        onChanged();
        return this;
      }

      private int rid_ ;
      /**
       * <pre>
       * 用户rid
       * </pre>
       *
       * <code>int32 rid = 8;</code>
       */
      public int getRid() {
        return rid_;
      }
      /**
       * <pre>
       * 用户rid
       * </pre>
       *
       * <code>int32 rid = 8;</code>
       */
      public Builder setRid(int value) {
        
        rid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 用户rid
       * </pre>
       *
       * <code>int32 rid = 8;</code>
       */
      public Builder clearRid() {
        
        rid_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object uid_ = "";
      /**
       * <pre>
       * </pre>
       *
       * <code>string uid = 9;</code>
       */
      public java.lang.String getUid() {
        java.lang.Object ref = uid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          uid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>string uid = 9;</code>
       */
      public com.google.protobuf.ByteString
          getUidBytes() {
        java.lang.Object ref = uid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          uid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>string uid = 9;</code>
       */
      public Builder setUid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>string uid = 9;</code>
       */
      public Builder clearUid() {
        
        uid_ = getDefaultInstance().getUid();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>string uid = 9;</code>
       */
      public Builder setUidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        uid_ = value;
        onChanged();
        return this;
      }

      private int role_ ;
      /**
       * <pre>
       *当前房间的身份（0不显示，1房主2副房主3管理员4会员）
       * </pre>
       *
       * <code>int32 role = 10;</code>
       */
      public int getRole() {
        return role_;
      }
      /**
       * <pre>
       *当前房间的身份（0不显示，1房主2副房主3管理员4会员）
       * </pre>
       *
       * <code>int32 role = 10;</code>
       */
      public Builder setRole(int value) {
        
        role_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前房间的身份（0不显示，1房主2副房主3管理员4会员）
       * </pre>
       *
       * <code>int32 role = 10;</code>
       */
      public Builder clearRole() {
        
        role_ = 0;
        onChanged();
        return this;
      }

      private int newVipLevel_ ;
      /**
       * <code>int32 newVipLevel = 11;</code>
       */
      public int getNewVipLevel() {
        return newVipLevel_;
      }
      /**
       * <code>int32 newVipLevel = 11;</code>
       */
      public Builder setNewVipLevel(int value) {
        
        newVipLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 newVipLevel = 11;</code>
       */
      public Builder clearNewVipLevel() {
        
        newVipLevel_ = 0;
        onChanged();
        return this;
      }

      private com.quhong.proto.YoustarProtoUname.RidInfo ridInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder> ridInfoBuilder_;
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public boolean hasRidInfo() {
        return ridInfoBuilder_ != null || ridInfo_ != null;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo() {
        if (ridInfoBuilder_ == null) {
          return ridInfo_ == null ? com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
        } else {
          return ridInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public Builder setRidInfo(com.quhong.proto.YoustarProtoUname.RidInfo value) {
        if (ridInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ridInfo_ = value;
          onChanged();
        } else {
          ridInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public Builder setRidInfo(
          com.quhong.proto.YoustarProtoUname.RidInfo.Builder builderForValue) {
        if (ridInfoBuilder_ == null) {
          ridInfo_ = builderForValue.build();
          onChanged();
        } else {
          ridInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public Builder mergeRidInfo(com.quhong.proto.YoustarProtoUname.RidInfo value) {
        if (ridInfoBuilder_ == null) {
          if (ridInfo_ != null) {
            ridInfo_ =
              com.quhong.proto.YoustarProtoUname.RidInfo.newBuilder(ridInfo_).mergeFrom(value).buildPartial();
          } else {
            ridInfo_ = value;
          }
          onChanged();
        } else {
          ridInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public Builder clearRidInfo() {
        if (ridInfoBuilder_ == null) {
          ridInfo_ = null;
          onChanged();
        } else {
          ridInfo_ = null;
          ridInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfo.Builder getRidInfoBuilder() {
        
        onChanged();
        return getRidInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder() {
        if (ridInfoBuilder_ != null) {
          return ridInfoBuilder_.getMessageOrBuilder();
        } else {
          return ridInfo_ == null ?
              com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
        }
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder> 
          getRidInfoFieldBuilder() {
        if (ridInfoBuilder_ == null) {
          ridInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder>(
                  getRidInfo(),
                  getParentForChildren(),
                  isClean());
          ridInfo_ = null;
        }
        return ridInfoBuilder_;
      }

      private int isNewUser_ ;
      /**
       * <pre>
       *是否为新用户 0 不是 1 是
       * </pre>
       *
       * <code>int32 isNewUser = 13;</code>
       */
      public int getIsNewUser() {
        return isNewUser_;
      }
      /**
       * <pre>
       *是否为新用户 0 不是 1 是
       * </pre>
       *
       * <code>int32 isNewUser = 13;</code>
       */
      public Builder setIsNewUser(int value) {
        
        isNewUser_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否为新用户 0 不是 1 是
       * </pre>
       *
       * <code>int32 isNewUser = 13;</code>
       */
      public Builder clearIsNewUser() {
        
        isNewUser_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Uname)
    }

    // @@protoc_insertion_point(class_scope:Uname)
    private static final com.quhong.proto.YoustarProtoUname.Uname DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoUname.Uname();
    }

    public static com.quhong.proto.YoustarProtoUname.Uname getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Uname>
        PARSER = new com.google.protobuf.AbstractParser<Uname>() {
      public Uname parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new Uname(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Uname> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Uname> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoUname.Uname getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RankInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:RankInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *排行名次 从1开始
     * </pre>
     *
     * <code>int32 rank = 1;</code>
     */
    int getRank();

    /**
     * <code>string name = 2;</code>
     */
    java.lang.String getName();
    /**
     * <code>string name = 2;</code>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>string head = 3;</code>
     */
    java.lang.String getHead();
    /**
     * <code>string head = 3;</code>
     */
    com.google.protobuf.ByteString
        getHeadBytes();

    /**
     * <code>int32 rid = 4;</code>
     */
    int getRid();

    /**
     * <pre>
     * 房间榜才有，房间id
     * </pre>
     *
     * <code>string room_id = 5;</code>
     */
    java.lang.String getRoomId();
    /**
     * <pre>
     * 房间榜才有，房间id
     * </pre>
     *
     * <code>string room_id = 5;</code>
     */
    com.google.protobuf.ByteString
        getRoomIdBytes();

    /**
     * <pre>
     * 1 发送榜 2 接收榜 3 房间榜
     * </pre>
     *
     * <code>int32 tagType = 6;</code>
     */
    int getTagType();

    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 7;</code>
     */
    boolean hasRidInfo();
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 7;</code>
     */
    com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo();
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 7;</code>
     */
    com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder();
  }
  /**
   * <pre>
   * 排行信息
   * </pre>
   *
   * Protobuf type {@code RankInfo}
   */
  public  static final class RankInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:RankInfo)
      RankInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RankInfo.newBuilder() to construct.
    private RankInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RankInfo() {
      rank_ = 0;
      name_ = "";
      head_ = "";
      rid_ = 0;
      roomId_ = "";
      tagType_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RankInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              rank_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              head_ = s;
              break;
            }
            case 32: {

              rid_ = input.readInt32();
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              roomId_ = s;
              break;
            }
            case 48: {

              tagType_ = input.readInt32();
              break;
            }
            case 58: {
              com.quhong.proto.YoustarProtoUname.RidInfo.Builder subBuilder = null;
              if (ridInfo_ != null) {
                subBuilder = ridInfo_.toBuilder();
              }
              ridInfo_ = input.readMessage(com.quhong.proto.YoustarProtoUname.RidInfo.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(ridInfo_);
                ridInfo_ = subBuilder.buildPartial();
              }

              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoUname.internal_static_RankInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoUname.internal_static_RankInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoUname.RankInfo.class, com.quhong.proto.YoustarProtoUname.RankInfo.Builder.class);
    }

    public static final int RANK_FIELD_NUMBER = 1;
    private int rank_;
    /**
     * <pre>
     *排行名次 从1开始
     * </pre>
     *
     * <code>int32 rank = 1;</code>
     */
    public int getRank() {
      return rank_;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 2;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 2;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int HEAD_FIELD_NUMBER = 3;
    private volatile java.lang.Object head_;
    /**
     * <code>string head = 3;</code>
     */
    public java.lang.String getHead() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        head_ = s;
        return s;
      }
    }
    /**
     * <code>string head = 3;</code>
     */
    public com.google.protobuf.ByteString
        getHeadBytes() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        head_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int RID_FIELD_NUMBER = 4;
    private int rid_;
    /**
     * <code>int32 rid = 4;</code>
     */
    public int getRid() {
      return rid_;
    }

    public static final int ROOM_ID_FIELD_NUMBER = 5;
    private volatile java.lang.Object roomId_;
    /**
     * <pre>
     * 房间榜才有，房间id
     * </pre>
     *
     * <code>string room_id = 5;</code>
     */
    public java.lang.String getRoomId() {
      java.lang.Object ref = roomId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        roomId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 房间榜才有，房间id
     * </pre>
     *
     * <code>string room_id = 5;</code>
     */
    public com.google.protobuf.ByteString
        getRoomIdBytes() {
      java.lang.Object ref = roomId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        roomId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TAGTYPE_FIELD_NUMBER = 6;
    private int tagType_;
    /**
     * <pre>
     * 1 发送榜 2 接收榜 3 房间榜
     * </pre>
     *
     * <code>int32 tagType = 6;</code>
     */
    public int getTagType() {
      return tagType_;
    }

    public static final int RIDINFO_FIELD_NUMBER = 7;
    private com.quhong.proto.YoustarProtoUname.RidInfo ridInfo_;
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 7;</code>
     */
    public boolean hasRidInfo() {
      return ridInfo_ != null;
    }
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 7;</code>
     */
    public com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo() {
      return ridInfo_ == null ? com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
    }
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 7;</code>
     */
    public com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder() {
      return getRidInfo();
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (rank_ != 0) {
        output.writeInt32(1, rank_);
      }
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      if (!getHeadBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, head_);
      }
      if (rid_ != 0) {
        output.writeInt32(4, rid_);
      }
      if (!getRoomIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, roomId_);
      }
      if (tagType_ != 0) {
        output.writeInt32(6, tagType_);
      }
      if (ridInfo_ != null) {
        output.writeMessage(7, getRidInfo());
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (rank_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, rank_);
      }
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      if (!getHeadBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, head_);
      }
      if (rid_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, rid_);
      }
      if (!getRoomIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, roomId_);
      }
      if (tagType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, tagType_);
      }
      if (ridInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getRidInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoUname.RankInfo)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoUname.RankInfo other = (com.quhong.proto.YoustarProtoUname.RankInfo) obj;

      boolean result = true;
      result = result && (getRank()
          == other.getRank());
      result = result && getName()
          .equals(other.getName());
      result = result && getHead()
          .equals(other.getHead());
      result = result && (getRid()
          == other.getRid());
      result = result && getRoomId()
          .equals(other.getRoomId());
      result = result && (getTagType()
          == other.getTagType());
      result = result && (hasRidInfo() == other.hasRidInfo());
      if (hasRidInfo()) {
        result = result && getRidInfo()
            .equals(other.getRidInfo());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + HEAD_FIELD_NUMBER;
      hash = (53 * hash) + getHead().hashCode();
      hash = (37 * hash) + RID_FIELD_NUMBER;
      hash = (53 * hash) + getRid();
      hash = (37 * hash) + ROOM_ID_FIELD_NUMBER;
      hash = (53 * hash) + getRoomId().hashCode();
      hash = (37 * hash) + TAGTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getTagType();
      if (hasRidInfo()) {
        hash = (37 * hash) + RIDINFO_FIELD_NUMBER;
        hash = (53 * hash) + getRidInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoUname.RankInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoUname.RankInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 排行信息
     * </pre>
     *
     * Protobuf type {@code RankInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:RankInfo)
        com.quhong.proto.YoustarProtoUname.RankInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoUname.internal_static_RankInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoUname.internal_static_RankInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoUname.RankInfo.class, com.quhong.proto.YoustarProtoUname.RankInfo.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoUname.RankInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        rank_ = 0;

        name_ = "";

        head_ = "";

        rid_ = 0;

        roomId_ = "";

        tagType_ = 0;

        if (ridInfoBuilder_ == null) {
          ridInfo_ = null;
        } else {
          ridInfo_ = null;
          ridInfoBuilder_ = null;
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoUname.internal_static_RankInfo_descriptor;
      }

      public com.quhong.proto.YoustarProtoUname.RankInfo getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoUname.RankInfo.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoUname.RankInfo build() {
        com.quhong.proto.YoustarProtoUname.RankInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoUname.RankInfo buildPartial() {
        com.quhong.proto.YoustarProtoUname.RankInfo result = new com.quhong.proto.YoustarProtoUname.RankInfo(this);
        result.rank_ = rank_;
        result.name_ = name_;
        result.head_ = head_;
        result.rid_ = rid_;
        result.roomId_ = roomId_;
        result.tagType_ = tagType_;
        if (ridInfoBuilder_ == null) {
          result.ridInfo_ = ridInfo_;
        } else {
          result.ridInfo_ = ridInfoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoUname.RankInfo) {
          return mergeFrom((com.quhong.proto.YoustarProtoUname.RankInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoUname.RankInfo other) {
        if (other == com.quhong.proto.YoustarProtoUname.RankInfo.getDefaultInstance()) return this;
        if (other.getRank() != 0) {
          setRank(other.getRank());
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (!other.getHead().isEmpty()) {
          head_ = other.head_;
          onChanged();
        }
        if (other.getRid() != 0) {
          setRid(other.getRid());
        }
        if (!other.getRoomId().isEmpty()) {
          roomId_ = other.roomId_;
          onChanged();
        }
        if (other.getTagType() != 0) {
          setTagType(other.getTagType());
        }
        if (other.hasRidInfo()) {
          mergeRidInfo(other.getRidInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoUname.RankInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoUname.RankInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int rank_ ;
      /**
       * <pre>
       *排行名次 从1开始
       * </pre>
       *
       * <code>int32 rank = 1;</code>
       */
      public int getRank() {
        return rank_;
      }
      /**
       * <pre>
       *排行名次 从1开始
       * </pre>
       *
       * <code>int32 rank = 1;</code>
       */
      public Builder setRank(int value) {
        
        rank_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *排行名次 从1开始
       * </pre>
       *
       * <code>int32 rank = 1;</code>
       */
      public Builder clearRank() {
        
        rank_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 2;</code>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object head_ = "";
      /**
       * <code>string head = 3;</code>
       */
      public java.lang.String getHead() {
        java.lang.Object ref = head_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          head_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string head = 3;</code>
       */
      public com.google.protobuf.ByteString
          getHeadBytes() {
        java.lang.Object ref = head_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          head_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string head = 3;</code>
       */
      public Builder setHead(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        head_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string head = 3;</code>
       */
      public Builder clearHead() {
        
        head_ = getDefaultInstance().getHead();
        onChanged();
        return this;
      }
      /**
       * <code>string head = 3;</code>
       */
      public Builder setHeadBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        head_ = value;
        onChanged();
        return this;
      }

      private int rid_ ;
      /**
       * <code>int32 rid = 4;</code>
       */
      public int getRid() {
        return rid_;
      }
      /**
       * <code>int32 rid = 4;</code>
       */
      public Builder setRid(int value) {
        
        rid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 rid = 4;</code>
       */
      public Builder clearRid() {
        
        rid_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object roomId_ = "";
      /**
       * <pre>
       * 房间榜才有，房间id
       * </pre>
       *
       * <code>string room_id = 5;</code>
       */
      public java.lang.String getRoomId() {
        java.lang.Object ref = roomId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          roomId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 房间榜才有，房间id
       * </pre>
       *
       * <code>string room_id = 5;</code>
       */
      public com.google.protobuf.ByteString
          getRoomIdBytes() {
        java.lang.Object ref = roomId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          roomId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 房间榜才有，房间id
       * </pre>
       *
       * <code>string room_id = 5;</code>
       */
      public Builder setRoomId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        roomId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 房间榜才有，房间id
       * </pre>
       *
       * <code>string room_id = 5;</code>
       */
      public Builder clearRoomId() {
        
        roomId_ = getDefaultInstance().getRoomId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 房间榜才有，房间id
       * </pre>
       *
       * <code>string room_id = 5;</code>
       */
      public Builder setRoomIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        roomId_ = value;
        onChanged();
        return this;
      }

      private int tagType_ ;
      /**
       * <pre>
       * 1 发送榜 2 接收榜 3 房间榜
       * </pre>
       *
       * <code>int32 tagType = 6;</code>
       */
      public int getTagType() {
        return tagType_;
      }
      /**
       * <pre>
       * 1 发送榜 2 接收榜 3 房间榜
       * </pre>
       *
       * <code>int32 tagType = 6;</code>
       */
      public Builder setTagType(int value) {
        
        tagType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 1 发送榜 2 接收榜 3 房间榜
       * </pre>
       *
       * <code>int32 tagType = 6;</code>
       */
      public Builder clearTagType() {
        
        tagType_ = 0;
        onChanged();
        return this;
      }

      private com.quhong.proto.YoustarProtoUname.RidInfo ridInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder> ridInfoBuilder_;
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public boolean hasRidInfo() {
        return ridInfoBuilder_ != null || ridInfo_ != null;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo() {
        if (ridInfoBuilder_ == null) {
          return ridInfo_ == null ? com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
        } else {
          return ridInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public Builder setRidInfo(com.quhong.proto.YoustarProtoUname.RidInfo value) {
        if (ridInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ridInfo_ = value;
          onChanged();
        } else {
          ridInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public Builder setRidInfo(
          com.quhong.proto.YoustarProtoUname.RidInfo.Builder builderForValue) {
        if (ridInfoBuilder_ == null) {
          ridInfo_ = builderForValue.build();
          onChanged();
        } else {
          ridInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public Builder mergeRidInfo(com.quhong.proto.YoustarProtoUname.RidInfo value) {
        if (ridInfoBuilder_ == null) {
          if (ridInfo_ != null) {
            ridInfo_ =
              com.quhong.proto.YoustarProtoUname.RidInfo.newBuilder(ridInfo_).mergeFrom(value).buildPartial();
          } else {
            ridInfo_ = value;
          }
          onChanged();
        } else {
          ridInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public Builder clearRidInfo() {
        if (ridInfoBuilder_ == null) {
          ridInfo_ = null;
          onChanged();
        } else {
          ridInfo_ = null;
          ridInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfo.Builder getRidInfoBuilder() {
        
        onChanged();
        return getRidInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder() {
        if (ridInfoBuilder_ != null) {
          return ridInfoBuilder_.getMessageOrBuilder();
        } else {
          return ridInfo_ == null ?
              com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
        }
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder> 
          getRidInfoFieldBuilder() {
        if (ridInfoBuilder_ == null) {
          ridInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder>(
                  getRidInfo(),
                  getParentForChildren(),
                  isClean());
          ridInfo_ = null;
        }
        return ridInfoBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:RankInfo)
    }

    // @@protoc_insertion_point(class_scope:RankInfo)
    private static final com.quhong.proto.YoustarProtoUname.RankInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoUname.RankInfo();
    }

    public static com.quhong.proto.YoustarProtoUname.RankInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RankInfo>
        PARSER = new com.google.protobuf.AbstractParser<RankInfo>() {
      public RankInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new RankInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RankInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RankInfo> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoUname.RankInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RankInfoListOrBuilder extends
      // @@protoc_insertion_point(interface_extends:RankInfoList)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 1 发送榜 2 接收榜 3 房间榜
     * </pre>
     *
     * <code>int32 tagType = 1;</code>
     */
    int getTagType();

    /**
     * <code>string title_en = 2;</code>
     */
    java.lang.String getTitleEn();
    /**
     * <code>string title_en = 2;</code>
     */
    com.google.protobuf.ByteString
        getTitleEnBytes();

    /**
     * <code>string title_ar = 3;</code>
     */
    java.lang.String getTitleAr();
    /**
     * <code>string title_ar = 3;</code>
     */
    com.google.protobuf.ByteString
        getTitleArBytes();

    /**
     * <pre>
     *排行列表
     * </pre>
     *
     * <code>repeated .RankInfo rankInfo = 4;</code>
     */
    java.util.List<com.quhong.proto.YoustarProtoUname.RankInfo> 
        getRankInfoList();
    /**
     * <pre>
     *排行列表
     * </pre>
     *
     * <code>repeated .RankInfo rankInfo = 4;</code>
     */
    com.quhong.proto.YoustarProtoUname.RankInfo getRankInfo(int index);
    /**
     * <pre>
     *排行列表
     * </pre>
     *
     * <code>repeated .RankInfo rankInfo = 4;</code>
     */
    int getRankInfoCount();
    /**
     * <pre>
     *排行列表
     * </pre>
     *
     * <code>repeated .RankInfo rankInfo = 4;</code>
     */
    java.util.List<? extends com.quhong.proto.YoustarProtoUname.RankInfoOrBuilder> 
        getRankInfoOrBuilderList();
    /**
     * <pre>
     *排行列表
     * </pre>
     *
     * <code>repeated .RankInfo rankInfo = 4;</code>
     */
    com.quhong.proto.YoustarProtoUname.RankInfoOrBuilder getRankInfoOrBuilder(
        int index);
  }
  /**
   * <pre>
   * 排行信息
   * </pre>
   *
   * Protobuf type {@code RankInfoList}
   */
  public  static final class RankInfoList extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:RankInfoList)
      RankInfoListOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RankInfoList.newBuilder() to construct.
    private RankInfoList(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RankInfoList() {
      tagType_ = 0;
      titleEn_ = "";
      titleAr_ = "";
      rankInfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RankInfoList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              tagType_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              titleEn_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              titleAr_ = s;
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                rankInfo_ = new java.util.ArrayList<com.quhong.proto.YoustarProtoUname.RankInfo>();
                mutable_bitField0_ |= 0x00000008;
              }
              rankInfo_.add(
                  input.readMessage(com.quhong.proto.YoustarProtoUname.RankInfo.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          rankInfo_ = java.util.Collections.unmodifiableList(rankInfo_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoUname.internal_static_RankInfoList_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoUname.internal_static_RankInfoList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoUname.RankInfoList.class, com.quhong.proto.YoustarProtoUname.RankInfoList.Builder.class);
    }

    private int bitField0_;
    public static final int TAGTYPE_FIELD_NUMBER = 1;
    private int tagType_;
    /**
     * <pre>
     * 1 发送榜 2 接收榜 3 房间榜
     * </pre>
     *
     * <code>int32 tagType = 1;</code>
     */
    public int getTagType() {
      return tagType_;
    }

    public static final int TITLE_EN_FIELD_NUMBER = 2;
    private volatile java.lang.Object titleEn_;
    /**
     * <code>string title_en = 2;</code>
     */
    public java.lang.String getTitleEn() {
      java.lang.Object ref = titleEn_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        titleEn_ = s;
        return s;
      }
    }
    /**
     * <code>string title_en = 2;</code>
     */
    public com.google.protobuf.ByteString
        getTitleEnBytes() {
      java.lang.Object ref = titleEn_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        titleEn_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TITLE_AR_FIELD_NUMBER = 3;
    private volatile java.lang.Object titleAr_;
    /**
     * <code>string title_ar = 3;</code>
     */
    public java.lang.String getTitleAr() {
      java.lang.Object ref = titleAr_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        titleAr_ = s;
        return s;
      }
    }
    /**
     * <code>string title_ar = 3;</code>
     */
    public com.google.protobuf.ByteString
        getTitleArBytes() {
      java.lang.Object ref = titleAr_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        titleAr_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int RANKINFO_FIELD_NUMBER = 4;
    private java.util.List<com.quhong.proto.YoustarProtoUname.RankInfo> rankInfo_;
    /**
     * <pre>
     *排行列表
     * </pre>
     *
     * <code>repeated .RankInfo rankInfo = 4;</code>
     */
    public java.util.List<com.quhong.proto.YoustarProtoUname.RankInfo> getRankInfoList() {
      return rankInfo_;
    }
    /**
     * <pre>
     *排行列表
     * </pre>
     *
     * <code>repeated .RankInfo rankInfo = 4;</code>
     */
    public java.util.List<? extends com.quhong.proto.YoustarProtoUname.RankInfoOrBuilder> 
        getRankInfoOrBuilderList() {
      return rankInfo_;
    }
    /**
     * <pre>
     *排行列表
     * </pre>
     *
     * <code>repeated .RankInfo rankInfo = 4;</code>
     */
    public int getRankInfoCount() {
      return rankInfo_.size();
    }
    /**
     * <pre>
     *排行列表
     * </pre>
     *
     * <code>repeated .RankInfo rankInfo = 4;</code>
     */
    public com.quhong.proto.YoustarProtoUname.RankInfo getRankInfo(int index) {
      return rankInfo_.get(index);
    }
    /**
     * <pre>
     *排行列表
     * </pre>
     *
     * <code>repeated .RankInfo rankInfo = 4;</code>
     */
    public com.quhong.proto.YoustarProtoUname.RankInfoOrBuilder getRankInfoOrBuilder(
        int index) {
      return rankInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tagType_ != 0) {
        output.writeInt32(1, tagType_);
      }
      if (!getTitleEnBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, titleEn_);
      }
      if (!getTitleArBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, titleAr_);
      }
      for (int i = 0; i < rankInfo_.size(); i++) {
        output.writeMessage(4, rankInfo_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tagType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, tagType_);
      }
      if (!getTitleEnBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, titleEn_);
      }
      if (!getTitleArBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, titleAr_);
      }
      for (int i = 0; i < rankInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, rankInfo_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoUname.RankInfoList)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoUname.RankInfoList other = (com.quhong.proto.YoustarProtoUname.RankInfoList) obj;

      boolean result = true;
      result = result && (getTagType()
          == other.getTagType());
      result = result && getTitleEn()
          .equals(other.getTitleEn());
      result = result && getTitleAr()
          .equals(other.getTitleAr());
      result = result && getRankInfoList()
          .equals(other.getRankInfoList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAGTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getTagType();
      hash = (37 * hash) + TITLE_EN_FIELD_NUMBER;
      hash = (53 * hash) + getTitleEn().hashCode();
      hash = (37 * hash) + TITLE_AR_FIELD_NUMBER;
      hash = (53 * hash) + getTitleAr().hashCode();
      if (getRankInfoCount() > 0) {
        hash = (37 * hash) + RANKINFO_FIELD_NUMBER;
        hash = (53 * hash) + getRankInfoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoUname.RankInfoList parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfoList parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfoList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfoList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfoList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfoList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfoList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfoList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfoList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfoList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfoList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.RankInfoList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoUname.RankInfoList prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 排行信息
     * </pre>
     *
     * Protobuf type {@code RankInfoList}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:RankInfoList)
        com.quhong.proto.YoustarProtoUname.RankInfoListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoUname.internal_static_RankInfoList_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoUname.internal_static_RankInfoList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoUname.RankInfoList.class, com.quhong.proto.YoustarProtoUname.RankInfoList.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoUname.RankInfoList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRankInfoFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        tagType_ = 0;

        titleEn_ = "";

        titleAr_ = "";

        if (rankInfoBuilder_ == null) {
          rankInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          rankInfoBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoUname.internal_static_RankInfoList_descriptor;
      }

      public com.quhong.proto.YoustarProtoUname.RankInfoList getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoUname.RankInfoList.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoUname.RankInfoList build() {
        com.quhong.proto.YoustarProtoUname.RankInfoList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoUname.RankInfoList buildPartial() {
        com.quhong.proto.YoustarProtoUname.RankInfoList result = new com.quhong.proto.YoustarProtoUname.RankInfoList(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.tagType_ = tagType_;
        result.titleEn_ = titleEn_;
        result.titleAr_ = titleAr_;
        if (rankInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008)) {
            rankInfo_ = java.util.Collections.unmodifiableList(rankInfo_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.rankInfo_ = rankInfo_;
        } else {
          result.rankInfo_ = rankInfoBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoUname.RankInfoList) {
          return mergeFrom((com.quhong.proto.YoustarProtoUname.RankInfoList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoUname.RankInfoList other) {
        if (other == com.quhong.proto.YoustarProtoUname.RankInfoList.getDefaultInstance()) return this;
        if (other.getTagType() != 0) {
          setTagType(other.getTagType());
        }
        if (!other.getTitleEn().isEmpty()) {
          titleEn_ = other.titleEn_;
          onChanged();
        }
        if (!other.getTitleAr().isEmpty()) {
          titleAr_ = other.titleAr_;
          onChanged();
        }
        if (rankInfoBuilder_ == null) {
          if (!other.rankInfo_.isEmpty()) {
            if (rankInfo_.isEmpty()) {
              rankInfo_ = other.rankInfo_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureRankInfoIsMutable();
              rankInfo_.addAll(other.rankInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.rankInfo_.isEmpty()) {
            if (rankInfoBuilder_.isEmpty()) {
              rankInfoBuilder_.dispose();
              rankInfoBuilder_ = null;
              rankInfo_ = other.rankInfo_;
              bitField0_ = (bitField0_ & ~0x00000008);
              rankInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRankInfoFieldBuilder() : null;
            } else {
              rankInfoBuilder_.addAllMessages(other.rankInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoUname.RankInfoList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoUname.RankInfoList) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int tagType_ ;
      /**
       * <pre>
       * 1 发送榜 2 接收榜 3 房间榜
       * </pre>
       *
       * <code>int32 tagType = 1;</code>
       */
      public int getTagType() {
        return tagType_;
      }
      /**
       * <pre>
       * 1 发送榜 2 接收榜 3 房间榜
       * </pre>
       *
       * <code>int32 tagType = 1;</code>
       */
      public Builder setTagType(int value) {
        
        tagType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 1 发送榜 2 接收榜 3 房间榜
       * </pre>
       *
       * <code>int32 tagType = 1;</code>
       */
      public Builder clearTagType() {
        
        tagType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object titleEn_ = "";
      /**
       * <code>string title_en = 2;</code>
       */
      public java.lang.String getTitleEn() {
        java.lang.Object ref = titleEn_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          titleEn_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string title_en = 2;</code>
       */
      public com.google.protobuf.ByteString
          getTitleEnBytes() {
        java.lang.Object ref = titleEn_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          titleEn_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string title_en = 2;</code>
       */
      public Builder setTitleEn(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        titleEn_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string title_en = 2;</code>
       */
      public Builder clearTitleEn() {
        
        titleEn_ = getDefaultInstance().getTitleEn();
        onChanged();
        return this;
      }
      /**
       * <code>string title_en = 2;</code>
       */
      public Builder setTitleEnBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        titleEn_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object titleAr_ = "";
      /**
       * <code>string title_ar = 3;</code>
       */
      public java.lang.String getTitleAr() {
        java.lang.Object ref = titleAr_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          titleAr_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string title_ar = 3;</code>
       */
      public com.google.protobuf.ByteString
          getTitleArBytes() {
        java.lang.Object ref = titleAr_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          titleAr_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string title_ar = 3;</code>
       */
      public Builder setTitleAr(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        titleAr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string title_ar = 3;</code>
       */
      public Builder clearTitleAr() {
        
        titleAr_ = getDefaultInstance().getTitleAr();
        onChanged();
        return this;
      }
      /**
       * <code>string title_ar = 3;</code>
       */
      public Builder setTitleArBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        titleAr_ = value;
        onChanged();
        return this;
      }

      private java.util.List<com.quhong.proto.YoustarProtoUname.RankInfo> rankInfo_ =
        java.util.Collections.emptyList();
      private void ensureRankInfoIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          rankInfo_ = new java.util.ArrayList<com.quhong.proto.YoustarProtoUname.RankInfo>(rankInfo_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.quhong.proto.YoustarProtoUname.RankInfo, com.quhong.proto.YoustarProtoUname.RankInfo.Builder, com.quhong.proto.YoustarProtoUname.RankInfoOrBuilder> rankInfoBuilder_;

      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public java.util.List<com.quhong.proto.YoustarProtoUname.RankInfo> getRankInfoList() {
        if (rankInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rankInfo_);
        } else {
          return rankInfoBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public int getRankInfoCount() {
        if (rankInfoBuilder_ == null) {
          return rankInfo_.size();
        } else {
          return rankInfoBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RankInfo getRankInfo(int index) {
        if (rankInfoBuilder_ == null) {
          return rankInfo_.get(index);
        } else {
          return rankInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public Builder setRankInfo(
          int index, com.quhong.proto.YoustarProtoUname.RankInfo value) {
        if (rankInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankInfoIsMutable();
          rankInfo_.set(index, value);
          onChanged();
        } else {
          rankInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public Builder setRankInfo(
          int index, com.quhong.proto.YoustarProtoUname.RankInfo.Builder builderForValue) {
        if (rankInfoBuilder_ == null) {
          ensureRankInfoIsMutable();
          rankInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          rankInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public Builder addRankInfo(com.quhong.proto.YoustarProtoUname.RankInfo value) {
        if (rankInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankInfoIsMutable();
          rankInfo_.add(value);
          onChanged();
        } else {
          rankInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public Builder addRankInfo(
          int index, com.quhong.proto.YoustarProtoUname.RankInfo value) {
        if (rankInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankInfoIsMutable();
          rankInfo_.add(index, value);
          onChanged();
        } else {
          rankInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public Builder addRankInfo(
          com.quhong.proto.YoustarProtoUname.RankInfo.Builder builderForValue) {
        if (rankInfoBuilder_ == null) {
          ensureRankInfoIsMutable();
          rankInfo_.add(builderForValue.build());
          onChanged();
        } else {
          rankInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public Builder addRankInfo(
          int index, com.quhong.proto.YoustarProtoUname.RankInfo.Builder builderForValue) {
        if (rankInfoBuilder_ == null) {
          ensureRankInfoIsMutable();
          rankInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          rankInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public Builder addAllRankInfo(
          java.lang.Iterable<? extends com.quhong.proto.YoustarProtoUname.RankInfo> values) {
        if (rankInfoBuilder_ == null) {
          ensureRankInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rankInfo_);
          onChanged();
        } else {
          rankInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public Builder clearRankInfo() {
        if (rankInfoBuilder_ == null) {
          rankInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          rankInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public Builder removeRankInfo(int index) {
        if (rankInfoBuilder_ == null) {
          ensureRankInfoIsMutable();
          rankInfo_.remove(index);
          onChanged();
        } else {
          rankInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RankInfo.Builder getRankInfoBuilder(
          int index) {
        return getRankInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RankInfoOrBuilder getRankInfoOrBuilder(
          int index) {
        if (rankInfoBuilder_ == null) {
          return rankInfo_.get(index);  } else {
          return rankInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public java.util.List<? extends com.quhong.proto.YoustarProtoUname.RankInfoOrBuilder> 
           getRankInfoOrBuilderList() {
        if (rankInfoBuilder_ != null) {
          return rankInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rankInfo_);
        }
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RankInfo.Builder addRankInfoBuilder() {
        return getRankInfoFieldBuilder().addBuilder(
            com.quhong.proto.YoustarProtoUname.RankInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RankInfo.Builder addRankInfoBuilder(
          int index) {
        return getRankInfoFieldBuilder().addBuilder(
            index, com.quhong.proto.YoustarProtoUname.RankInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *排行列表
       * </pre>
       *
       * <code>repeated .RankInfo rankInfo = 4;</code>
       */
      public java.util.List<com.quhong.proto.YoustarProtoUname.RankInfo.Builder> 
           getRankInfoBuilderList() {
        return getRankInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.quhong.proto.YoustarProtoUname.RankInfo, com.quhong.proto.YoustarProtoUname.RankInfo.Builder, com.quhong.proto.YoustarProtoUname.RankInfoOrBuilder> 
          getRankInfoFieldBuilder() {
        if (rankInfoBuilder_ == null) {
          rankInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.quhong.proto.YoustarProtoUname.RankInfo, com.quhong.proto.YoustarProtoUname.RankInfo.Builder, com.quhong.proto.YoustarProtoUname.RankInfoOrBuilder>(
                  rankInfo_,
                  ((bitField0_ & 0x00000008) == 0x00000008),
                  getParentForChildren(),
                  isClean());
          rankInfo_ = null;
        }
        return rankInfoBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:RankInfoList)
    }

    // @@protoc_insertion_point(class_scope:RankInfoList)
    private static final com.quhong.proto.YoustarProtoUname.RankInfoList DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoUname.RankInfoList();
    }

    public static com.quhong.proto.YoustarProtoUname.RankInfoList getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RankInfoList>
        PARSER = new com.google.protobuf.AbstractParser<RankInfoList>() {
      public RankInfoList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new RankInfoList(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RankInfoList> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RankInfoList> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoUname.RankInfoList getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UserInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:UserInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string uid = 1;</code>
     */
    java.lang.String getUid();
    /**
     * <code>string uid = 1;</code>
     */
    com.google.protobuf.ByteString
        getUidBytes();

    /**
     * <code>string name = 2;</code>
     */
    java.lang.String getName();
    /**
     * <code>string name = 2;</code>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>string head = 3;</code>
     */
    java.lang.String getHead();
    /**
     * <code>string head = 3;</code>
     */
    com.google.protobuf.ByteString
        getHeadBytes();

    /**
     * <code>int32 viceHost = 4;</code>
     */
    int getViceHost();
  }
  /**
   * Protobuf type {@code UserInfo}
   */
  public  static final class UserInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:UserInfo)
      UserInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UserInfo.newBuilder() to construct.
    private UserInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UserInfo() {
      uid_ = "";
      name_ = "";
      head_ = "";
      viceHost_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private UserInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              uid_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              head_ = s;
              break;
            }
            case 32: {

              viceHost_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoUname.internal_static_UserInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoUname.internal_static_UserInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoUname.UserInfo.class, com.quhong.proto.YoustarProtoUname.UserInfo.Builder.class);
    }

    public static final int UID_FIELD_NUMBER = 1;
    private volatile java.lang.Object uid_;
    /**
     * <code>string uid = 1;</code>
     */
    public java.lang.String getUid() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        uid_ = s;
        return s;
      }
    }
    /**
     * <code>string uid = 1;</code>
     */
    public com.google.protobuf.ByteString
        getUidBytes() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 2;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 2;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int HEAD_FIELD_NUMBER = 3;
    private volatile java.lang.Object head_;
    /**
     * <code>string head = 3;</code>
     */
    public java.lang.String getHead() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        head_ = s;
        return s;
      }
    }
    /**
     * <code>string head = 3;</code>
     */
    public com.google.protobuf.ByteString
        getHeadBytes() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        head_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VICEHOST_FIELD_NUMBER = 4;
    private int viceHost_;
    /**
     * <code>int32 viceHost = 4;</code>
     */
    public int getViceHost() {
      return viceHost_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getUidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, uid_);
      }
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      if (!getHeadBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, head_);
      }
      if (viceHost_ != 0) {
        output.writeInt32(4, viceHost_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getUidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, uid_);
      }
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      if (!getHeadBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, head_);
      }
      if (viceHost_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, viceHost_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoUname.UserInfo)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoUname.UserInfo other = (com.quhong.proto.YoustarProtoUname.UserInfo) obj;

      boolean result = true;
      result = result && getUid()
          .equals(other.getUid());
      result = result && getName()
          .equals(other.getName());
      result = result && getHead()
          .equals(other.getHead());
      result = result && (getViceHost()
          == other.getViceHost());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + getUid().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + HEAD_FIELD_NUMBER;
      hash = (53 * hash) + getHead().hashCode();
      hash = (37 * hash) + VICEHOST_FIELD_NUMBER;
      hash = (53 * hash) + getViceHost();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoUname.UserInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.UserInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.UserInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.UserInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.UserInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.UserInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.UserInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.UserInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.UserInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.UserInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.UserInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.UserInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoUname.UserInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code UserInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:UserInfo)
        com.quhong.proto.YoustarProtoUname.UserInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoUname.internal_static_UserInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoUname.internal_static_UserInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoUname.UserInfo.class, com.quhong.proto.YoustarProtoUname.UserInfo.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoUname.UserInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        uid_ = "";

        name_ = "";

        head_ = "";

        viceHost_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoUname.internal_static_UserInfo_descriptor;
      }

      public com.quhong.proto.YoustarProtoUname.UserInfo getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoUname.UserInfo.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoUname.UserInfo build() {
        com.quhong.proto.YoustarProtoUname.UserInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoUname.UserInfo buildPartial() {
        com.quhong.proto.YoustarProtoUname.UserInfo result = new com.quhong.proto.YoustarProtoUname.UserInfo(this);
        result.uid_ = uid_;
        result.name_ = name_;
        result.head_ = head_;
        result.viceHost_ = viceHost_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoUname.UserInfo) {
          return mergeFrom((com.quhong.proto.YoustarProtoUname.UserInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoUname.UserInfo other) {
        if (other == com.quhong.proto.YoustarProtoUname.UserInfo.getDefaultInstance()) return this;
        if (!other.getUid().isEmpty()) {
          uid_ = other.uid_;
          onChanged();
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (!other.getHead().isEmpty()) {
          head_ = other.head_;
          onChanged();
        }
        if (other.getViceHost() != 0) {
          setViceHost(other.getViceHost());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoUname.UserInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoUname.UserInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object uid_ = "";
      /**
       * <code>string uid = 1;</code>
       */
      public java.lang.String getUid() {
        java.lang.Object ref = uid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          uid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string uid = 1;</code>
       */
      public com.google.protobuf.ByteString
          getUidBytes() {
        java.lang.Object ref = uid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          uid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string uid = 1;</code>
       */
      public Builder setUid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string uid = 1;</code>
       */
      public Builder clearUid() {
        
        uid_ = getDefaultInstance().getUid();
        onChanged();
        return this;
      }
      /**
       * <code>string uid = 1;</code>
       */
      public Builder setUidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        uid_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 2;</code>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object head_ = "";
      /**
       * <code>string head = 3;</code>
       */
      public java.lang.String getHead() {
        java.lang.Object ref = head_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          head_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string head = 3;</code>
       */
      public com.google.protobuf.ByteString
          getHeadBytes() {
        java.lang.Object ref = head_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          head_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string head = 3;</code>
       */
      public Builder setHead(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        head_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string head = 3;</code>
       */
      public Builder clearHead() {
        
        head_ = getDefaultInstance().getHead();
        onChanged();
        return this;
      }
      /**
       * <code>string head = 3;</code>
       */
      public Builder setHeadBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        head_ = value;
        onChanged();
        return this;
      }

      private int viceHost_ ;
      /**
       * <code>int32 viceHost = 4;</code>
       */
      public int getViceHost() {
        return viceHost_;
      }
      /**
       * <code>int32 viceHost = 4;</code>
       */
      public Builder setViceHost(int value) {
        
        viceHost_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 viceHost = 4;</code>
       */
      public Builder clearViceHost() {
        
        viceHost_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:UserInfo)
    }

    // @@protoc_insertion_point(class_scope:UserInfo)
    private static final com.quhong.proto.YoustarProtoUname.UserInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoUname.UserInfo();
    }

    public static com.quhong.proto.YoustarProtoUname.UserInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UserInfo>
        PARSER = new com.google.protobuf.AbstractParser<UserInfo>() {
      public UserInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new UserInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<UserInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UserInfo> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoUname.UserInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RidInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:RidInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 用户rid
     * </pre>
     *
     * <code>string rid = 1;</code>
     */
    java.lang.String getRid();
    /**
     * <pre>
     * 用户rid
     * </pre>
     *
     * <code>string rid = 1;</code>
     */
    com.google.protobuf.ByteString
        getRidBytes();

    /**
     * <pre>
     * rid等级 大于0为靓号
     * </pre>
     *
     * <code>int32 level = 2;</code>
     */
    int getLevel();
  }
  /**
   * Protobuf type {@code RidInfo}
   */
  public  static final class RidInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:RidInfo)
      RidInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RidInfo.newBuilder() to construct.
    private RidInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RidInfo() {
      rid_ = "";
      level_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RidInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              rid_ = s;
              break;
            }
            case 16: {

              level_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoUname.internal_static_RidInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoUname.internal_static_RidInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoUname.RidInfo.class, com.quhong.proto.YoustarProtoUname.RidInfo.Builder.class);
    }

    public static final int RID_FIELD_NUMBER = 1;
    private volatile java.lang.Object rid_;
    /**
     * <pre>
     * 用户rid
     * </pre>
     *
     * <code>string rid = 1;</code>
     */
    public java.lang.String getRid() {
      java.lang.Object ref = rid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        rid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 用户rid
     * </pre>
     *
     * <code>string rid = 1;</code>
     */
    public com.google.protobuf.ByteString
        getRidBytes() {
      java.lang.Object ref = rid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <pre>
     * rid等级 大于0为靓号
     * </pre>
     *
     * <code>int32 level = 2;</code>
     */
    public int getLevel() {
      return level_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getRidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, rid_);
      }
      if (level_ != 0) {
        output.writeInt32(2, level_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getRidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, rid_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, level_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoUname.RidInfo)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoUname.RidInfo other = (com.quhong.proto.YoustarProtoUname.RidInfo) obj;

      boolean result = true;
      result = result && getRid()
          .equals(other.getRid());
      result = result && (getLevel()
          == other.getLevel());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RID_FIELD_NUMBER;
      hash = (53 * hash) + getRid().hashCode();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoUname.RidInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.RidInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RidInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.RidInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RidInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoUname.RidInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RidInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.RidInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RidInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.RidInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoUname.RidInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoUname.RidInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoUname.RidInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code RidInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:RidInfo)
        com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoUname.internal_static_RidInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoUname.internal_static_RidInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoUname.RidInfo.class, com.quhong.proto.YoustarProtoUname.RidInfo.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoUname.RidInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        rid_ = "";

        level_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoUname.internal_static_RidInfo_descriptor;
      }

      public com.quhong.proto.YoustarProtoUname.RidInfo getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoUname.RidInfo build() {
        com.quhong.proto.YoustarProtoUname.RidInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoUname.RidInfo buildPartial() {
        com.quhong.proto.YoustarProtoUname.RidInfo result = new com.quhong.proto.YoustarProtoUname.RidInfo(this);
        result.rid_ = rid_;
        result.level_ = level_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoUname.RidInfo) {
          return mergeFrom((com.quhong.proto.YoustarProtoUname.RidInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoUname.RidInfo other) {
        if (other == com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance()) return this;
        if (!other.getRid().isEmpty()) {
          rid_ = other.rid_;
          onChanged();
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoUname.RidInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoUname.RidInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object rid_ = "";
      /**
       * <pre>
       * 用户rid
       * </pre>
       *
       * <code>string rid = 1;</code>
       */
      public java.lang.String getRid() {
        java.lang.Object ref = rid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          rid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 用户rid
       * </pre>
       *
       * <code>string rid = 1;</code>
       */
      public com.google.protobuf.ByteString
          getRidBytes() {
        java.lang.Object ref = rid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          rid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 用户rid
       * </pre>
       *
       * <code>string rid = 1;</code>
       */
      public Builder setRid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        rid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 用户rid
       * </pre>
       *
       * <code>string rid = 1;</code>
       */
      public Builder clearRid() {
        
        rid_ = getDefaultInstance().getRid();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 用户rid
       * </pre>
       *
       * <code>string rid = 1;</code>
       */
      public Builder setRidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        rid_ = value;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       * rid等级 大于0为靓号
       * </pre>
       *
       * <code>int32 level = 2;</code>
       */
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       * rid等级 大于0为靓号
       * </pre>
       *
       * <code>int32 level = 2;</code>
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * rid等级 大于0为靓号
       * </pre>
       *
       * <code>int32 level = 2;</code>
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:RidInfo)
    }

    // @@protoc_insertion_point(class_scope:RidInfo)
    private static final com.quhong.proto.YoustarProtoUname.RidInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoUname.RidInfo();
    }

    public static com.quhong.proto.YoustarProtoUname.RidInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RidInfo>
        PARSER = new com.google.protobuf.AbstractParser<RidInfo>() {
      public RidInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new RidInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RidInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RidInfo> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoUname.RidInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Uname_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Uname_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_RankInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_RankInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_RankInfoList_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_RankInfoList_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_UserInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_UserInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_RidInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_RidInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013uname.proto\"\330\001\n\005Uname\022\014\n\004name\030\001 \001(\t\022\013\n" +
      "\003vip\030\002 \001(\005\022\r\n\005level\030\003 \001(\005\022\r\n\005badge\030\004 \003(\t" +
      "\022\014\n\004head\030\005 \001(\t\022\013\n\003bid\030\006 \001(\005\022\020\n\010identify\030" +
      "\007 \001(\005\022\013\n\003rid\030\010 \001(\005\022\013\n\003uid\030\t \001(\t\022\014\n\004role\030" +
      "\n \001(\005\022\023\n\013newVipLevel\030\013 \001(\005\022\031\n\007ridInfo\030\014 " +
      "\001(\0132\010.RidInfo\022\021\n\tisNewUser\030\r \001(\005\"~\n\010Rank" +
      "Info\022\014\n\004rank\030\001 \001(\005\022\014\n\004name\030\002 \001(\t\022\014\n\004head" +
      "\030\003 \001(\t\022\013\n\003rid\030\004 \001(\005\022\017\n\007room_id\030\005 \001(\t\022\017\n\007" +
      "tagType\030\006 \001(\005\022\031\n\007ridInfo\030\007 \001(\0132\010.RidInfo" +
      "\"`\n\014RankInfoList\022\017\n\007tagType\030\001 \001(\005\022\020\n\010tit",
      "le_en\030\002 \001(\t\022\020\n\010title_ar\030\003 \001(\t\022\033\n\010rankInf" +
      "o\030\004 \003(\0132\t.RankInfo\"E\n\010UserInfo\022\013\n\003uid\030\001 " +
      "\001(\t\022\014\n\004name\030\002 \001(\t\022\014\n\004head\030\003 \001(\t\022\020\n\010viceH" +
      "ost\030\004 \001(\005\"%\n\007RidInfo\022\013\n\003rid\030\001 \001(\t\022\r\n\005lev" +
      "el\030\002 \001(\005B%\n\020com.quhong.protoB\021YoustarPro" +
      "toUnameb\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_Uname_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Uname_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Uname_descriptor,
        new java.lang.String[] { "Name", "Vip", "Level", "Badge", "Head", "Bid", "Identify", "Rid", "Uid", "Role", "NewVipLevel", "RidInfo", "IsNewUser", });
    internal_static_RankInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_RankInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_RankInfo_descriptor,
        new java.lang.String[] { "Rank", "Name", "Head", "Rid", "RoomId", "TagType", "RidInfo", });
    internal_static_RankInfoList_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_RankInfoList_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_RankInfoList_descriptor,
        new java.lang.String[] { "TagType", "TitleEn", "TitleAr", "RankInfo", });
    internal_static_UserInfo_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_UserInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_UserInfo_descriptor,
        new java.lang.String[] { "Uid", "Name", "Head", "ViceHost", });
    internal_static_RidInfo_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_RidInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_RidInfo_descriptor,
        new java.lang.String[] { "Rid", "Level", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
