// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: gift.proto

package com.quhong.proto;

public final class YoustarProtoGift {
  private YoustarProtoGift() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReceiveInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ReceiveInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string name = 1;</code>
     */
    java.lang.String getName();
    /**
     * <code>string name = 1;</code>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>string uid = 2;</code>
     */
    java.lang.String getUid();
    /**
     * <code>string uid = 2;</code>
     */
    com.google.protobuf.ByteString
        getUidBytes();

    /**
     * <code>string head = 3;</code>
     */
    java.lang.String getHead();
    /**
     * <code>string head = 3;</code>
     */
    com.google.protobuf.ByteString
        getHeadBytes();

    /**
     * <code>int32 vip_level = 4;</code>
     */
    int getVipLevel();

    /**
     * <code>int32 ulvl = 5;</code>
     */
    int getUlvl();

    /**
     * <pre>
     * 0 无 1 vip 2 svip
     * </pre>
     *
     * <code>int32 identify = 6;</code>
     */
    int getIdentify();

    /**
     * <code>string rid = 7;</code>
     */
    java.lang.String getRid();
    /**
     * <code>string rid = 7;</code>
     */
    com.google.protobuf.ByteString
        getRidBytes();

    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 8;</code>
     */
    boolean hasRidInfo();
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 8;</code>
     */
    com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo();
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 8;</code>
     */
    com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder();
  }
  /**
   * <pre>
   *礼物接收者信息
   * </pre>
   *
   * Protobuf type {@code ReceiveInfo}
   */
  public  static final class ReceiveInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ReceiveInfo)
      ReceiveInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReceiveInfo.newBuilder() to construct.
    private ReceiveInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReceiveInfo() {
      name_ = "";
      uid_ = "";
      head_ = "";
      vipLevel_ = 0;
      ulvl_ = 0;
      identify_ = 0;
      rid_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReceiveInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              uid_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              head_ = s;
              break;
            }
            case 32: {

              vipLevel_ = input.readInt32();
              break;
            }
            case 40: {

              ulvl_ = input.readInt32();
              break;
            }
            case 48: {

              identify_ = input.readInt32();
              break;
            }
            case 58: {
              java.lang.String s = input.readStringRequireUtf8();

              rid_ = s;
              break;
            }
            case 66: {
              com.quhong.proto.YoustarProtoUname.RidInfo.Builder subBuilder = null;
              if (ridInfo_ != null) {
                subBuilder = ridInfo_.toBuilder();
              }
              ridInfo_ = input.readMessage(com.quhong.proto.YoustarProtoUname.RidInfo.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(ridInfo_);
                ridInfo_ = subBuilder.buildPartial();
              }

              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoGift.internal_static_ReceiveInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoGift.internal_static_ReceiveInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoGift.ReceiveInfo.class, com.quhong.proto.YoustarProtoGift.ReceiveInfo.Builder.class);
    }

    public static final int NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 1;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 1;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int UID_FIELD_NUMBER = 2;
    private volatile java.lang.Object uid_;
    /**
     * <code>string uid = 2;</code>
     */
    public java.lang.String getUid() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        uid_ = s;
        return s;
      }
    }
    /**
     * <code>string uid = 2;</code>
     */
    public com.google.protobuf.ByteString
        getUidBytes() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int HEAD_FIELD_NUMBER = 3;
    private volatile java.lang.Object head_;
    /**
     * <code>string head = 3;</code>
     */
    public java.lang.String getHead() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        head_ = s;
        return s;
      }
    }
    /**
     * <code>string head = 3;</code>
     */
    public com.google.protobuf.ByteString
        getHeadBytes() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        head_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VIP_LEVEL_FIELD_NUMBER = 4;
    private int vipLevel_;
    /**
     * <code>int32 vip_level = 4;</code>
     */
    public int getVipLevel() {
      return vipLevel_;
    }

    public static final int ULVL_FIELD_NUMBER = 5;
    private int ulvl_;
    /**
     * <code>int32 ulvl = 5;</code>
     */
    public int getUlvl() {
      return ulvl_;
    }

    public static final int IDENTIFY_FIELD_NUMBER = 6;
    private int identify_;
    /**
     * <pre>
     * 0 无 1 vip 2 svip
     * </pre>
     *
     * <code>int32 identify = 6;</code>
     */
    public int getIdentify() {
      return identify_;
    }

    public static final int RID_FIELD_NUMBER = 7;
    private volatile java.lang.Object rid_;
    /**
     * <code>string rid = 7;</code>
     */
    public java.lang.String getRid() {
      java.lang.Object ref = rid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        rid_ = s;
        return s;
      }
    }
    /**
     * <code>string rid = 7;</code>
     */
    public com.google.protobuf.ByteString
        getRidBytes() {
      java.lang.Object ref = rid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int RIDINFO_FIELD_NUMBER = 8;
    private com.quhong.proto.YoustarProtoUname.RidInfo ridInfo_;
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 8;</code>
     */
    public boolean hasRidInfo() {
      return ridInfo_ != null;
    }
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 8;</code>
     */
    public com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo() {
      return ridInfo_ == null ? com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
    }
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 8;</code>
     */
    public com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder() {
      return getRidInfo();
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
      }
      if (!getUidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, uid_);
      }
      if (!getHeadBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, head_);
      }
      if (vipLevel_ != 0) {
        output.writeInt32(4, vipLevel_);
      }
      if (ulvl_ != 0) {
        output.writeInt32(5, ulvl_);
      }
      if (identify_ != 0) {
        output.writeInt32(6, identify_);
      }
      if (!getRidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, rid_);
      }
      if (ridInfo_ != null) {
        output.writeMessage(8, getRidInfo());
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
      }
      if (!getUidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, uid_);
      }
      if (!getHeadBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, head_);
      }
      if (vipLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, vipLevel_);
      }
      if (ulvl_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, ulvl_);
      }
      if (identify_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, identify_);
      }
      if (!getRidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, rid_);
      }
      if (ridInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, getRidInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoGift.ReceiveInfo)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoGift.ReceiveInfo other = (com.quhong.proto.YoustarProtoGift.ReceiveInfo) obj;

      boolean result = true;
      result = result && getName()
          .equals(other.getName());
      result = result && getUid()
          .equals(other.getUid());
      result = result && getHead()
          .equals(other.getHead());
      result = result && (getVipLevel()
          == other.getVipLevel());
      result = result && (getUlvl()
          == other.getUlvl());
      result = result && (getIdentify()
          == other.getIdentify());
      result = result && getRid()
          .equals(other.getRid());
      result = result && (hasRidInfo() == other.hasRidInfo());
      if (hasRidInfo()) {
        result = result && getRidInfo()
            .equals(other.getRidInfo());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + getUid().hashCode();
      hash = (37 * hash) + HEAD_FIELD_NUMBER;
      hash = (53 * hash) + getHead().hashCode();
      hash = (37 * hash) + VIP_LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getVipLevel();
      hash = (37 * hash) + ULVL_FIELD_NUMBER;
      hash = (53 * hash) + getUlvl();
      hash = (37 * hash) + IDENTIFY_FIELD_NUMBER;
      hash = (53 * hash) + getIdentify();
      hash = (37 * hash) + RID_FIELD_NUMBER;
      hash = (53 * hash) + getRid().hashCode();
      if (hasRidInfo()) {
        hash = (37 * hash) + RIDINFO_FIELD_NUMBER;
        hash = (53 * hash) + getRidInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoGift.ReceiveInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *礼物接收者信息
     * </pre>
     *
     * Protobuf type {@code ReceiveInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ReceiveInfo)
        com.quhong.proto.YoustarProtoGift.ReceiveInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoGift.internal_static_ReceiveInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoGift.internal_static_ReceiveInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoGift.ReceiveInfo.class, com.quhong.proto.YoustarProtoGift.ReceiveInfo.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoGift.ReceiveInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        name_ = "";

        uid_ = "";

        head_ = "";

        vipLevel_ = 0;

        ulvl_ = 0;

        identify_ = 0;

        rid_ = "";

        if (ridInfoBuilder_ == null) {
          ridInfo_ = null;
        } else {
          ridInfo_ = null;
          ridInfoBuilder_ = null;
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoGift.internal_static_ReceiveInfo_descriptor;
      }

      public com.quhong.proto.YoustarProtoGift.ReceiveInfo getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoGift.ReceiveInfo.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoGift.ReceiveInfo build() {
        com.quhong.proto.YoustarProtoGift.ReceiveInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoGift.ReceiveInfo buildPartial() {
        com.quhong.proto.YoustarProtoGift.ReceiveInfo result = new com.quhong.proto.YoustarProtoGift.ReceiveInfo(this);
        result.name_ = name_;
        result.uid_ = uid_;
        result.head_ = head_;
        result.vipLevel_ = vipLevel_;
        result.ulvl_ = ulvl_;
        result.identify_ = identify_;
        result.rid_ = rid_;
        if (ridInfoBuilder_ == null) {
          result.ridInfo_ = ridInfo_;
        } else {
          result.ridInfo_ = ridInfoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoGift.ReceiveInfo) {
          return mergeFrom((com.quhong.proto.YoustarProtoGift.ReceiveInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoGift.ReceiveInfo other) {
        if (other == com.quhong.proto.YoustarProtoGift.ReceiveInfo.getDefaultInstance()) return this;
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (!other.getUid().isEmpty()) {
          uid_ = other.uid_;
          onChanged();
        }
        if (!other.getHead().isEmpty()) {
          head_ = other.head_;
          onChanged();
        }
        if (other.getVipLevel() != 0) {
          setVipLevel(other.getVipLevel());
        }
        if (other.getUlvl() != 0) {
          setUlvl(other.getUlvl());
        }
        if (other.getIdentify() != 0) {
          setIdentify(other.getIdentify());
        }
        if (!other.getRid().isEmpty()) {
          rid_ = other.rid_;
          onChanged();
        }
        if (other.hasRidInfo()) {
          mergeRidInfo(other.getRidInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoGift.ReceiveInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoGift.ReceiveInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 1;</code>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 1;</code>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 1;</code>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 1;</code>
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 1;</code>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object uid_ = "";
      /**
       * <code>string uid = 2;</code>
       */
      public java.lang.String getUid() {
        java.lang.Object ref = uid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          uid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string uid = 2;</code>
       */
      public com.google.protobuf.ByteString
          getUidBytes() {
        java.lang.Object ref = uid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          uid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string uid = 2;</code>
       */
      public Builder setUid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string uid = 2;</code>
       */
      public Builder clearUid() {
        
        uid_ = getDefaultInstance().getUid();
        onChanged();
        return this;
      }
      /**
       * <code>string uid = 2;</code>
       */
      public Builder setUidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        uid_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object head_ = "";
      /**
       * <code>string head = 3;</code>
       */
      public java.lang.String getHead() {
        java.lang.Object ref = head_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          head_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string head = 3;</code>
       */
      public com.google.protobuf.ByteString
          getHeadBytes() {
        java.lang.Object ref = head_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          head_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string head = 3;</code>
       */
      public Builder setHead(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        head_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string head = 3;</code>
       */
      public Builder clearHead() {
        
        head_ = getDefaultInstance().getHead();
        onChanged();
        return this;
      }
      /**
       * <code>string head = 3;</code>
       */
      public Builder setHeadBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        head_ = value;
        onChanged();
        return this;
      }

      private int vipLevel_ ;
      /**
       * <code>int32 vip_level = 4;</code>
       */
      public int getVipLevel() {
        return vipLevel_;
      }
      /**
       * <code>int32 vip_level = 4;</code>
       */
      public Builder setVipLevel(int value) {
        
        vipLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 vip_level = 4;</code>
       */
      public Builder clearVipLevel() {
        
        vipLevel_ = 0;
        onChanged();
        return this;
      }

      private int ulvl_ ;
      /**
       * <code>int32 ulvl = 5;</code>
       */
      public int getUlvl() {
        return ulvl_;
      }
      /**
       * <code>int32 ulvl = 5;</code>
       */
      public Builder setUlvl(int value) {
        
        ulvl_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 ulvl = 5;</code>
       */
      public Builder clearUlvl() {
        
        ulvl_ = 0;
        onChanged();
        return this;
      }

      private int identify_ ;
      /**
       * <pre>
       * 0 无 1 vip 2 svip
       * </pre>
       *
       * <code>int32 identify = 6;</code>
       */
      public int getIdentify() {
        return identify_;
      }
      /**
       * <pre>
       * 0 无 1 vip 2 svip
       * </pre>
       *
       * <code>int32 identify = 6;</code>
       */
      public Builder setIdentify(int value) {
        
        identify_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 0 无 1 vip 2 svip
       * </pre>
       *
       * <code>int32 identify = 6;</code>
       */
      public Builder clearIdentify() {
        
        identify_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object rid_ = "";
      /**
       * <code>string rid = 7;</code>
       */
      public java.lang.String getRid() {
        java.lang.Object ref = rid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          rid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string rid = 7;</code>
       */
      public com.google.protobuf.ByteString
          getRidBytes() {
        java.lang.Object ref = rid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          rid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string rid = 7;</code>
       */
      public Builder setRid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        rid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string rid = 7;</code>
       */
      public Builder clearRid() {
        
        rid_ = getDefaultInstance().getRid();
        onChanged();
        return this;
      }
      /**
       * <code>string rid = 7;</code>
       */
      public Builder setRidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        rid_ = value;
        onChanged();
        return this;
      }

      private com.quhong.proto.YoustarProtoUname.RidInfo ridInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder> ridInfoBuilder_;
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 8;</code>
       */
      public boolean hasRidInfo() {
        return ridInfoBuilder_ != null || ridInfo_ != null;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 8;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo() {
        if (ridInfoBuilder_ == null) {
          return ridInfo_ == null ? com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
        } else {
          return ridInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 8;</code>
       */
      public Builder setRidInfo(com.quhong.proto.YoustarProtoUname.RidInfo value) {
        if (ridInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ridInfo_ = value;
          onChanged();
        } else {
          ridInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 8;</code>
       */
      public Builder setRidInfo(
          com.quhong.proto.YoustarProtoUname.RidInfo.Builder builderForValue) {
        if (ridInfoBuilder_ == null) {
          ridInfo_ = builderForValue.build();
          onChanged();
        } else {
          ridInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 8;</code>
       */
      public Builder mergeRidInfo(com.quhong.proto.YoustarProtoUname.RidInfo value) {
        if (ridInfoBuilder_ == null) {
          if (ridInfo_ != null) {
            ridInfo_ =
              com.quhong.proto.YoustarProtoUname.RidInfo.newBuilder(ridInfo_).mergeFrom(value).buildPartial();
          } else {
            ridInfo_ = value;
          }
          onChanged();
        } else {
          ridInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 8;</code>
       */
      public Builder clearRidInfo() {
        if (ridInfoBuilder_ == null) {
          ridInfo_ = null;
          onChanged();
        } else {
          ridInfo_ = null;
          ridInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 8;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfo.Builder getRidInfoBuilder() {
        
        onChanged();
        return getRidInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 8;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder() {
        if (ridInfoBuilder_ != null) {
          return ridInfoBuilder_.getMessageOrBuilder();
        } else {
          return ridInfo_ == null ?
              com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
        }
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 8;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder> 
          getRidInfoFieldBuilder() {
        if (ridInfoBuilder_ == null) {
          ridInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder>(
                  getRidInfo(),
                  getParentForChildren(),
                  isClean());
          ridInfo_ = null;
        }
        return ridInfoBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ReceiveInfo)
    }

    // @@protoc_insertion_point(class_scope:ReceiveInfo)
    private static final com.quhong.proto.YoustarProtoGift.ReceiveInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoGift.ReceiveInfo();
    }

    public static com.quhong.proto.YoustarProtoGift.ReceiveInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReceiveInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReceiveInfo>() {
      public ReceiveInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new ReceiveInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReceiveInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReceiveInfo> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoGift.ReceiveInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SendInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SendInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 beans = 1;</code>
     */
    int getBeans();

    /**
     * <code>string name = 2;</code>
     */
    java.lang.String getName();
    /**
     * <code>string name = 2;</code>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>string head = 3;</code>
     */
    java.lang.String getHead();
    /**
     * <code>string head = 3;</code>
     */
    com.google.protobuf.ByteString
        getHeadBytes();

    /**
     * <code>string uid = 4;</code>
     */
    java.lang.String getUid();
    /**
     * <code>string uid = 4;</code>
     */
    com.google.protobuf.ByteString
        getUidBytes();

    /**
     * <code>int32 vip_level = 5;</code>
     */
    int getVipLevel();

    /**
     * <code>int32 ulvl = 6;</code>
     */
    int getUlvl();

    /**
     * <code>repeated string badge_list = 7;</code>
     */
    java.util.List<java.lang.String>
        getBadgeListList();
    /**
     * <code>repeated string badge_list = 7;</code>
     */
    int getBadgeListCount();
    /**
     * <code>repeated string badge_list = 7;</code>
     */
    java.lang.String getBadgeList(int index);
    /**
     * <code>repeated string badge_list = 7;</code>
     */
    com.google.protobuf.ByteString
        getBadgeListBytes(int index);

    /**
     * <pre>
     * 0 无 1 vip 2 svip
     * </pre>
     *
     * <code>int32 identify = 8;</code>
     */
    int getIdentify();

    /**
     * <pre>
     *当前房间的身份（0不显示，1房主2副房主3管理员4会员）
     * </pre>
     *
     * <code>int32 role = 9;</code>
     */
    int getRole();

    /**
     * <code>string rid = 10;</code>
     */
    java.lang.String getRid();
    /**
     * <code>string rid = 10;</code>
     */
    com.google.protobuf.ByteString
        getRidBytes();

    /**
     * <code>int32 newVipLevel = 11;</code>
     */
    int getNewVipLevel();

    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 12;</code>
     */
    boolean hasRidInfo();
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 12;</code>
     */
    com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo();
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 12;</code>
     */
    com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder();

    /**
     * <code>int32 isNewUser = 13;</code>
     */
    int getIsNewUser();
  }
  /**
   * <pre>
   *礼物发送者信息
   * </pre>
   *
   * Protobuf type {@code SendInfo}
   */
  public  static final class SendInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SendInfo)
      SendInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendInfo.newBuilder() to construct.
    private SendInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendInfo() {
      beans_ = 0;
      name_ = "";
      head_ = "";
      uid_ = "";
      vipLevel_ = 0;
      ulvl_ = 0;
      badgeList_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      identify_ = 0;
      role_ = 0;
      rid_ = "";
      newVipLevel_ = 0;
      isNewUser_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              beans_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              head_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              uid_ = s;
              break;
            }
            case 40: {

              vipLevel_ = input.readInt32();
              break;
            }
            case 48: {

              ulvl_ = input.readInt32();
              break;
            }
            case 58: {
              java.lang.String s = input.readStringRequireUtf8();
              if (!((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
                badgeList_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000040;
              }
              badgeList_.add(s);
              break;
            }
            case 64: {

              identify_ = input.readInt32();
              break;
            }
            case 72: {

              role_ = input.readInt32();
              break;
            }
            case 82: {
              java.lang.String s = input.readStringRequireUtf8();

              rid_ = s;
              break;
            }
            case 88: {

              newVipLevel_ = input.readInt32();
              break;
            }
            case 98: {
              com.quhong.proto.YoustarProtoUname.RidInfo.Builder subBuilder = null;
              if (ridInfo_ != null) {
                subBuilder = ridInfo_.toBuilder();
              }
              ridInfo_ = input.readMessage(com.quhong.proto.YoustarProtoUname.RidInfo.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(ridInfo_);
                ridInfo_ = subBuilder.buildPartial();
              }

              break;
            }
            case 104: {

              isNewUser_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
          badgeList_ = badgeList_.getUnmodifiableView();
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoGift.internal_static_SendInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoGift.internal_static_SendInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoGift.SendInfo.class, com.quhong.proto.YoustarProtoGift.SendInfo.Builder.class);
    }

    private int bitField0_;
    public static final int BEANS_FIELD_NUMBER = 1;
    private int beans_;
    /**
     * <code>int32 beans = 1;</code>
     */
    public int getBeans() {
      return beans_;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 2;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 2;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int HEAD_FIELD_NUMBER = 3;
    private volatile java.lang.Object head_;
    /**
     * <code>string head = 3;</code>
     */
    public java.lang.String getHead() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        head_ = s;
        return s;
      }
    }
    /**
     * <code>string head = 3;</code>
     */
    public com.google.protobuf.ByteString
        getHeadBytes() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        head_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int UID_FIELD_NUMBER = 4;
    private volatile java.lang.Object uid_;
    /**
     * <code>string uid = 4;</code>
     */
    public java.lang.String getUid() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        uid_ = s;
        return s;
      }
    }
    /**
     * <code>string uid = 4;</code>
     */
    public com.google.protobuf.ByteString
        getUidBytes() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VIP_LEVEL_FIELD_NUMBER = 5;
    private int vipLevel_;
    /**
     * <code>int32 vip_level = 5;</code>
     */
    public int getVipLevel() {
      return vipLevel_;
    }

    public static final int ULVL_FIELD_NUMBER = 6;
    private int ulvl_;
    /**
     * <code>int32 ulvl = 6;</code>
     */
    public int getUlvl() {
      return ulvl_;
    }

    public static final int BADGE_LIST_FIELD_NUMBER = 7;
    private com.google.protobuf.LazyStringList badgeList_;
    /**
     * <code>repeated string badge_list = 7;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getBadgeListList() {
      return badgeList_;
    }
    /**
     * <code>repeated string badge_list = 7;</code>
     */
    public int getBadgeListCount() {
      return badgeList_.size();
    }
    /**
     * <code>repeated string badge_list = 7;</code>
     */
    public java.lang.String getBadgeList(int index) {
      return badgeList_.get(index);
    }
    /**
     * <code>repeated string badge_list = 7;</code>
     */
    public com.google.protobuf.ByteString
        getBadgeListBytes(int index) {
      return badgeList_.getByteString(index);
    }

    public static final int IDENTIFY_FIELD_NUMBER = 8;
    private int identify_;
    /**
     * <pre>
     * 0 无 1 vip 2 svip
     * </pre>
     *
     * <code>int32 identify = 8;</code>
     */
    public int getIdentify() {
      return identify_;
    }

    public static final int ROLE_FIELD_NUMBER = 9;
    private int role_;
    /**
     * <pre>
     *当前房间的身份（0不显示，1房主2副房主3管理员4会员）
     * </pre>
     *
     * <code>int32 role = 9;</code>
     */
    public int getRole() {
      return role_;
    }

    public static final int RID_FIELD_NUMBER = 10;
    private volatile java.lang.Object rid_;
    /**
     * <code>string rid = 10;</code>
     */
    public java.lang.String getRid() {
      java.lang.Object ref = rid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        rid_ = s;
        return s;
      }
    }
    /**
     * <code>string rid = 10;</code>
     */
    public com.google.protobuf.ByteString
        getRidBytes() {
      java.lang.Object ref = rid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NEWVIPLEVEL_FIELD_NUMBER = 11;
    private int newVipLevel_;
    /**
     * <code>int32 newVipLevel = 11;</code>
     */
    public int getNewVipLevel() {
      return newVipLevel_;
    }

    public static final int RIDINFO_FIELD_NUMBER = 12;
    private com.quhong.proto.YoustarProtoUname.RidInfo ridInfo_;
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 12;</code>
     */
    public boolean hasRidInfo() {
      return ridInfo_ != null;
    }
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 12;</code>
     */
    public com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo() {
      return ridInfo_ == null ? com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
    }
    /**
     * <pre>
     * 靓号
     * </pre>
     *
     * <code>.RidInfo ridInfo = 12;</code>
     */
    public com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder() {
      return getRidInfo();
    }

    public static final int ISNEWUSER_FIELD_NUMBER = 13;
    private int isNewUser_;
    /**
     * <code>int32 isNewUser = 13;</code>
     */
    public int getIsNewUser() {
      return isNewUser_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (beans_ != 0) {
        output.writeInt32(1, beans_);
      }
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      if (!getHeadBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, head_);
      }
      if (!getUidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, uid_);
      }
      if (vipLevel_ != 0) {
        output.writeInt32(5, vipLevel_);
      }
      if (ulvl_ != 0) {
        output.writeInt32(6, ulvl_);
      }
      for (int i = 0; i < badgeList_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, badgeList_.getRaw(i));
      }
      if (identify_ != 0) {
        output.writeInt32(8, identify_);
      }
      if (role_ != 0) {
        output.writeInt32(9, role_);
      }
      if (!getRidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, rid_);
      }
      if (newVipLevel_ != 0) {
        output.writeInt32(11, newVipLevel_);
      }
      if (ridInfo_ != null) {
        output.writeMessage(12, getRidInfo());
      }
      if (isNewUser_ != 0) {
        output.writeInt32(13, isNewUser_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (beans_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, beans_);
      }
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      if (!getHeadBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, head_);
      }
      if (!getUidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, uid_);
      }
      if (vipLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, vipLevel_);
      }
      if (ulvl_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, ulvl_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < badgeList_.size(); i++) {
          dataSize += computeStringSizeNoTag(badgeList_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getBadgeListList().size();
      }
      if (identify_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, identify_);
      }
      if (role_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, role_);
      }
      if (!getRidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, rid_);
      }
      if (newVipLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, newVipLevel_);
      }
      if (ridInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, getRidInfo());
      }
      if (isNewUser_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(13, isNewUser_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoGift.SendInfo)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoGift.SendInfo other = (com.quhong.proto.YoustarProtoGift.SendInfo) obj;

      boolean result = true;
      result = result && (getBeans()
          == other.getBeans());
      result = result && getName()
          .equals(other.getName());
      result = result && getHead()
          .equals(other.getHead());
      result = result && getUid()
          .equals(other.getUid());
      result = result && (getVipLevel()
          == other.getVipLevel());
      result = result && (getUlvl()
          == other.getUlvl());
      result = result && getBadgeListList()
          .equals(other.getBadgeListList());
      result = result && (getIdentify()
          == other.getIdentify());
      result = result && (getRole()
          == other.getRole());
      result = result && getRid()
          .equals(other.getRid());
      result = result && (getNewVipLevel()
          == other.getNewVipLevel());
      result = result && (hasRidInfo() == other.hasRidInfo());
      if (hasRidInfo()) {
        result = result && getRidInfo()
            .equals(other.getRidInfo());
      }
      result = result && (getIsNewUser()
          == other.getIsNewUser());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + BEANS_FIELD_NUMBER;
      hash = (53 * hash) + getBeans();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + HEAD_FIELD_NUMBER;
      hash = (53 * hash) + getHead().hashCode();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + getUid().hashCode();
      hash = (37 * hash) + VIP_LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getVipLevel();
      hash = (37 * hash) + ULVL_FIELD_NUMBER;
      hash = (53 * hash) + getUlvl();
      if (getBadgeListCount() > 0) {
        hash = (37 * hash) + BADGE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getBadgeListList().hashCode();
      }
      hash = (37 * hash) + IDENTIFY_FIELD_NUMBER;
      hash = (53 * hash) + getIdentify();
      hash = (37 * hash) + ROLE_FIELD_NUMBER;
      hash = (53 * hash) + getRole();
      hash = (37 * hash) + RID_FIELD_NUMBER;
      hash = (53 * hash) + getRid().hashCode();
      hash = (37 * hash) + NEWVIPLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getNewVipLevel();
      if (hasRidInfo()) {
        hash = (37 * hash) + RIDINFO_FIELD_NUMBER;
        hash = (53 * hash) + getRidInfo().hashCode();
      }
      hash = (37 * hash) + ISNEWUSER_FIELD_NUMBER;
      hash = (53 * hash) + getIsNewUser();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoGift.SendInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoGift.SendInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.SendInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoGift.SendInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.SendInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoGift.SendInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.SendInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoGift.SendInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.SendInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoGift.SendInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.SendInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoGift.SendInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoGift.SendInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *礼物发送者信息
     * </pre>
     *
     * Protobuf type {@code SendInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SendInfo)
        com.quhong.proto.YoustarProtoGift.SendInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoGift.internal_static_SendInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoGift.internal_static_SendInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoGift.SendInfo.class, com.quhong.proto.YoustarProtoGift.SendInfo.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoGift.SendInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        beans_ = 0;

        name_ = "";

        head_ = "";

        uid_ = "";

        vipLevel_ = 0;

        ulvl_ = 0;

        badgeList_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000040);
        identify_ = 0;

        role_ = 0;

        rid_ = "";

        newVipLevel_ = 0;

        if (ridInfoBuilder_ == null) {
          ridInfo_ = null;
        } else {
          ridInfo_ = null;
          ridInfoBuilder_ = null;
        }
        isNewUser_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoGift.internal_static_SendInfo_descriptor;
      }

      public com.quhong.proto.YoustarProtoGift.SendInfo getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoGift.SendInfo.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoGift.SendInfo build() {
        com.quhong.proto.YoustarProtoGift.SendInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoGift.SendInfo buildPartial() {
        com.quhong.proto.YoustarProtoGift.SendInfo result = new com.quhong.proto.YoustarProtoGift.SendInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.beans_ = beans_;
        result.name_ = name_;
        result.head_ = head_;
        result.uid_ = uid_;
        result.vipLevel_ = vipLevel_;
        result.ulvl_ = ulvl_;
        if (((bitField0_ & 0x00000040) == 0x00000040)) {
          badgeList_ = badgeList_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.badgeList_ = badgeList_;
        result.identify_ = identify_;
        result.role_ = role_;
        result.rid_ = rid_;
        result.newVipLevel_ = newVipLevel_;
        if (ridInfoBuilder_ == null) {
          result.ridInfo_ = ridInfo_;
        } else {
          result.ridInfo_ = ridInfoBuilder_.build();
        }
        result.isNewUser_ = isNewUser_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoGift.SendInfo) {
          return mergeFrom((com.quhong.proto.YoustarProtoGift.SendInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoGift.SendInfo other) {
        if (other == com.quhong.proto.YoustarProtoGift.SendInfo.getDefaultInstance()) return this;
        if (other.getBeans() != 0) {
          setBeans(other.getBeans());
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (!other.getHead().isEmpty()) {
          head_ = other.head_;
          onChanged();
        }
        if (!other.getUid().isEmpty()) {
          uid_ = other.uid_;
          onChanged();
        }
        if (other.getVipLevel() != 0) {
          setVipLevel(other.getVipLevel());
        }
        if (other.getUlvl() != 0) {
          setUlvl(other.getUlvl());
        }
        if (!other.badgeList_.isEmpty()) {
          if (badgeList_.isEmpty()) {
            badgeList_ = other.badgeList_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureBadgeListIsMutable();
            badgeList_.addAll(other.badgeList_);
          }
          onChanged();
        }
        if (other.getIdentify() != 0) {
          setIdentify(other.getIdentify());
        }
        if (other.getRole() != 0) {
          setRole(other.getRole());
        }
        if (!other.getRid().isEmpty()) {
          rid_ = other.rid_;
          onChanged();
        }
        if (other.getNewVipLevel() != 0) {
          setNewVipLevel(other.getNewVipLevel());
        }
        if (other.hasRidInfo()) {
          mergeRidInfo(other.getRidInfo());
        }
        if (other.getIsNewUser() != 0) {
          setIsNewUser(other.getIsNewUser());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoGift.SendInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoGift.SendInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int beans_ ;
      /**
       * <code>int32 beans = 1;</code>
       */
      public int getBeans() {
        return beans_;
      }
      /**
       * <code>int32 beans = 1;</code>
       */
      public Builder setBeans(int value) {
        
        beans_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 beans = 1;</code>
       */
      public Builder clearBeans() {
        
        beans_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 2;</code>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object head_ = "";
      /**
       * <code>string head = 3;</code>
       */
      public java.lang.String getHead() {
        java.lang.Object ref = head_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          head_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string head = 3;</code>
       */
      public com.google.protobuf.ByteString
          getHeadBytes() {
        java.lang.Object ref = head_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          head_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string head = 3;</code>
       */
      public Builder setHead(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        head_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string head = 3;</code>
       */
      public Builder clearHead() {
        
        head_ = getDefaultInstance().getHead();
        onChanged();
        return this;
      }
      /**
       * <code>string head = 3;</code>
       */
      public Builder setHeadBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        head_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object uid_ = "";
      /**
       * <code>string uid = 4;</code>
       */
      public java.lang.String getUid() {
        java.lang.Object ref = uid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          uid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string uid = 4;</code>
       */
      public com.google.protobuf.ByteString
          getUidBytes() {
        java.lang.Object ref = uid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          uid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string uid = 4;</code>
       */
      public Builder setUid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string uid = 4;</code>
       */
      public Builder clearUid() {
        
        uid_ = getDefaultInstance().getUid();
        onChanged();
        return this;
      }
      /**
       * <code>string uid = 4;</code>
       */
      public Builder setUidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        uid_ = value;
        onChanged();
        return this;
      }

      private int vipLevel_ ;
      /**
       * <code>int32 vip_level = 5;</code>
       */
      public int getVipLevel() {
        return vipLevel_;
      }
      /**
       * <code>int32 vip_level = 5;</code>
       */
      public Builder setVipLevel(int value) {
        
        vipLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 vip_level = 5;</code>
       */
      public Builder clearVipLevel() {
        
        vipLevel_ = 0;
        onChanged();
        return this;
      }

      private int ulvl_ ;
      /**
       * <code>int32 ulvl = 6;</code>
       */
      public int getUlvl() {
        return ulvl_;
      }
      /**
       * <code>int32 ulvl = 6;</code>
       */
      public Builder setUlvl(int value) {
        
        ulvl_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 ulvl = 6;</code>
       */
      public Builder clearUlvl() {
        
        ulvl_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList badgeList_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureBadgeListIsMutable() {
        if (!((bitField0_ & 0x00000040) == 0x00000040)) {
          badgeList_ = new com.google.protobuf.LazyStringArrayList(badgeList_);
          bitField0_ |= 0x00000040;
         }
      }
      /**
       * <code>repeated string badge_list = 7;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getBadgeListList() {
        return badgeList_.getUnmodifiableView();
      }
      /**
       * <code>repeated string badge_list = 7;</code>
       */
      public int getBadgeListCount() {
        return badgeList_.size();
      }
      /**
       * <code>repeated string badge_list = 7;</code>
       */
      public java.lang.String getBadgeList(int index) {
        return badgeList_.get(index);
      }
      /**
       * <code>repeated string badge_list = 7;</code>
       */
      public com.google.protobuf.ByteString
          getBadgeListBytes(int index) {
        return badgeList_.getByteString(index);
      }
      /**
       * <code>repeated string badge_list = 7;</code>
       */
      public Builder setBadgeList(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureBadgeListIsMutable();
        badgeList_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string badge_list = 7;</code>
       */
      public Builder addBadgeList(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureBadgeListIsMutable();
        badgeList_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string badge_list = 7;</code>
       */
      public Builder addAllBadgeList(
          java.lang.Iterable<java.lang.String> values) {
        ensureBadgeListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, badgeList_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string badge_list = 7;</code>
       */
      public Builder clearBadgeList() {
        badgeList_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string badge_list = 7;</code>
       */
      public Builder addBadgeListBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        ensureBadgeListIsMutable();
        badgeList_.add(value);
        onChanged();
        return this;
      }

      private int identify_ ;
      /**
       * <pre>
       * 0 无 1 vip 2 svip
       * </pre>
       *
       * <code>int32 identify = 8;</code>
       */
      public int getIdentify() {
        return identify_;
      }
      /**
       * <pre>
       * 0 无 1 vip 2 svip
       * </pre>
       *
       * <code>int32 identify = 8;</code>
       */
      public Builder setIdentify(int value) {
        
        identify_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 0 无 1 vip 2 svip
       * </pre>
       *
       * <code>int32 identify = 8;</code>
       */
      public Builder clearIdentify() {
        
        identify_ = 0;
        onChanged();
        return this;
      }

      private int role_ ;
      /**
       * <pre>
       *当前房间的身份（0不显示，1房主2副房主3管理员4会员）
       * </pre>
       *
       * <code>int32 role = 9;</code>
       */
      public int getRole() {
        return role_;
      }
      /**
       * <pre>
       *当前房间的身份（0不显示，1房主2副房主3管理员4会员）
       * </pre>
       *
       * <code>int32 role = 9;</code>
       */
      public Builder setRole(int value) {
        
        role_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前房间的身份（0不显示，1房主2副房主3管理员4会员）
       * </pre>
       *
       * <code>int32 role = 9;</code>
       */
      public Builder clearRole() {
        
        role_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object rid_ = "";
      /**
       * <code>string rid = 10;</code>
       */
      public java.lang.String getRid() {
        java.lang.Object ref = rid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          rid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string rid = 10;</code>
       */
      public com.google.protobuf.ByteString
          getRidBytes() {
        java.lang.Object ref = rid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          rid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string rid = 10;</code>
       */
      public Builder setRid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        rid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string rid = 10;</code>
       */
      public Builder clearRid() {
        
        rid_ = getDefaultInstance().getRid();
        onChanged();
        return this;
      }
      /**
       * <code>string rid = 10;</code>
       */
      public Builder setRidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        rid_ = value;
        onChanged();
        return this;
      }

      private int newVipLevel_ ;
      /**
       * <code>int32 newVipLevel = 11;</code>
       */
      public int getNewVipLevel() {
        return newVipLevel_;
      }
      /**
       * <code>int32 newVipLevel = 11;</code>
       */
      public Builder setNewVipLevel(int value) {
        
        newVipLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 newVipLevel = 11;</code>
       */
      public Builder clearNewVipLevel() {
        
        newVipLevel_ = 0;
        onChanged();
        return this;
      }

      private com.quhong.proto.YoustarProtoUname.RidInfo ridInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder> ridInfoBuilder_;
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public boolean hasRidInfo() {
        return ridInfoBuilder_ != null || ridInfo_ != null;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfo getRidInfo() {
        if (ridInfoBuilder_ == null) {
          return ridInfo_ == null ? com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
        } else {
          return ridInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public Builder setRidInfo(com.quhong.proto.YoustarProtoUname.RidInfo value) {
        if (ridInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ridInfo_ = value;
          onChanged();
        } else {
          ridInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public Builder setRidInfo(
          com.quhong.proto.YoustarProtoUname.RidInfo.Builder builderForValue) {
        if (ridInfoBuilder_ == null) {
          ridInfo_ = builderForValue.build();
          onChanged();
        } else {
          ridInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public Builder mergeRidInfo(com.quhong.proto.YoustarProtoUname.RidInfo value) {
        if (ridInfoBuilder_ == null) {
          if (ridInfo_ != null) {
            ridInfo_ =
              com.quhong.proto.YoustarProtoUname.RidInfo.newBuilder(ridInfo_).mergeFrom(value).buildPartial();
          } else {
            ridInfo_ = value;
          }
          onChanged();
        } else {
          ridInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public Builder clearRidInfo() {
        if (ridInfoBuilder_ == null) {
          ridInfo_ = null;
          onChanged();
        } else {
          ridInfo_ = null;
          ridInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfo.Builder getRidInfoBuilder() {
        
        onChanged();
        return getRidInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      public com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder getRidInfoOrBuilder() {
        if (ridInfoBuilder_ != null) {
          return ridInfoBuilder_.getMessageOrBuilder();
        } else {
          return ridInfo_ == null ?
              com.quhong.proto.YoustarProtoUname.RidInfo.getDefaultInstance() : ridInfo_;
        }
      }
      /**
       * <pre>
       * 靓号
       * </pre>
       *
       * <code>.RidInfo ridInfo = 12;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder> 
          getRidInfoFieldBuilder() {
        if (ridInfoBuilder_ == null) {
          ridInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoUname.RidInfo, com.quhong.proto.YoustarProtoUname.RidInfo.Builder, com.quhong.proto.YoustarProtoUname.RidInfoOrBuilder>(
                  getRidInfo(),
                  getParentForChildren(),
                  isClean());
          ridInfo_ = null;
        }
        return ridInfoBuilder_;
      }

      private int isNewUser_ ;
      /**
       * <code>int32 isNewUser = 13;</code>
       */
      public int getIsNewUser() {
        return isNewUser_;
      }
      /**
       * <code>int32 isNewUser = 13;</code>
       */
      public Builder setIsNewUser(int value) {
        
        isNewUser_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 isNewUser = 13;</code>
       */
      public Builder clearIsNewUser() {
        
        isNewUser_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SendInfo)
    }

    // @@protoc_insertion_point(class_scope:SendInfo)
    private static final com.quhong.proto.YoustarProtoGift.SendInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoGift.SendInfo();
    }

    public static com.quhong.proto.YoustarProtoGift.SendInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SendInfo>
        PARSER = new com.google.protobuf.AbstractParser<SendInfo>() {
      public SendInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new SendInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendInfo> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoGift.SendInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GiftInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:GiftInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *礼物ID
     * </pre>
     *
     * <code>int32 gift_id = 1;</code>
     */
    int getGiftId();

    /**
     * <pre>
     *动画类型：1:普通 2:交互 3:全屏webp 4:全屏入场webp 5:全屏Aiya
     * </pre>
     *
     * <code>int32 gift_type = 2;</code>
     */
    int getGiftType();

    /**
     * <pre>
     *3: webp  7 svga
     * </pre>
     *
     * <code>int32 carton_load_type = 3;</code>
     */
    int getCartonLoadType();

    /**
     * <pre>
     *礼物数量
     * </pre>
     *
     * <code>int32 gift_number = 4;</code>
     */
    int getGiftNumber();

    /**
     * <pre>
     *礼物图标
     * </pre>
     *
     * <code>string gift_icon = 5;</code>
     */
    java.lang.String getGiftIcon();
    /**
     * <pre>
     *礼物图标
     * </pre>
     *
     * <code>string gift_icon = 5;</code>
     */
    com.google.protobuf.ByteString
        getGiftIconBytes();

    /**
     * <pre>
     *礼物时间
     * </pre>
     *
     * <code>int64 gift_time = 6;</code>
     */
    long getGiftTime();

    /**
     * <pre>
     *礼物价格
     * </pre>
     *
     * <code>int32 gift_price = 7;</code>
     */
    int getGiftPrice();

    /**
     * <pre>
     *是否是盲盒礼物 0否 1是
     * </pre>
     *
     * <code>int32 gift_random = 8;</code>
     */
    int getGiftRandom();

    /**
     * <pre>
     *盲盒礼物图标
     * </pre>
     *
     * <code>string gift_random_icon = 9;</code>
     */
    java.lang.String getGiftRandomIcon();
    /**
     * <pre>
     *盲盒礼物图标
     * </pre>
     *
     * <code>string gift_random_icon = 9;</code>
     */
    com.google.protobuf.ByteString
        getGiftRandomIconBytes();

    /**
     * <pre>
     * 是否融合动画礼物 0否 1是
     * </pre>
     *
     * <code>int32 gift_fusion = 10;</code>
     */
    int getGiftFusion();

    /**
     * <pre>
     * 融合用户id
     * </pre>
     *
     * <code>string fusion_id = 11;</code>
     */
    java.lang.String getFusionId();
    /**
     * <pre>
     * 融合用户id
     * </pre>
     *
     * <code>string fusion_id = 11;</code>
     */
    com.google.protobuf.ByteString
        getFusionIdBytes();

    /**
     * <pre>
     * 融合用户名称
     * </pre>
     *
     * <code>string fusion_name = 12;</code>
     */
    java.lang.String getFusionName();
    /**
     * <pre>
     * 融合用户名称
     * </pre>
     *
     * <code>string fusion_name = 12;</code>
     */
    com.google.protobuf.ByteString
        getFusionNameBytes();

    /**
     * <pre>
     * 融合用户头像
     * </pre>
     *
     * <code>string fusion_head = 13;</code>
     */
    java.lang.String getFusionHead();
    /**
     * <pre>
     * 融合用户头像
     * </pre>
     *
     * <code>string fusion_head = 13;</code>
     */
    com.google.protobuf.ByteString
        getFusionHeadBytes();
  }
  /**
   * <pre>
   *礼物信息
   * </pre>
   *
   * Protobuf type {@code GiftInfo}
   */
  public  static final class GiftInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:GiftInfo)
      GiftInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GiftInfo.newBuilder() to construct.
    private GiftInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GiftInfo() {
      giftId_ = 0;
      giftType_ = 0;
      cartonLoadType_ = 0;
      giftNumber_ = 0;
      giftIcon_ = "";
      giftTime_ = 0L;
      giftPrice_ = 0;
      giftRandom_ = 0;
      giftRandomIcon_ = "";
      giftFusion_ = 0;
      fusionId_ = "";
      fusionName_ = "";
      fusionHead_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GiftInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              giftId_ = input.readInt32();
              break;
            }
            case 16: {

              giftType_ = input.readInt32();
              break;
            }
            case 24: {

              cartonLoadType_ = input.readInt32();
              break;
            }
            case 32: {

              giftNumber_ = input.readInt32();
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              giftIcon_ = s;
              break;
            }
            case 48: {

              giftTime_ = input.readInt64();
              break;
            }
            case 56: {

              giftPrice_ = input.readInt32();
              break;
            }
            case 64: {

              giftRandom_ = input.readInt32();
              break;
            }
            case 74: {
              java.lang.String s = input.readStringRequireUtf8();

              giftRandomIcon_ = s;
              break;
            }
            case 80: {

              giftFusion_ = input.readInt32();
              break;
            }
            case 90: {
              java.lang.String s = input.readStringRequireUtf8();

              fusionId_ = s;
              break;
            }
            case 98: {
              java.lang.String s = input.readStringRequireUtf8();

              fusionName_ = s;
              break;
            }
            case 106: {
              java.lang.String s = input.readStringRequireUtf8();

              fusionHead_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoGift.internal_static_GiftInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoGift.internal_static_GiftInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoGift.GiftInfo.class, com.quhong.proto.YoustarProtoGift.GiftInfo.Builder.class);
    }

    public static final int GIFT_ID_FIELD_NUMBER = 1;
    private int giftId_;
    /**
     * <pre>
     *礼物ID
     * </pre>
     *
     * <code>int32 gift_id = 1;</code>
     */
    public int getGiftId() {
      return giftId_;
    }

    public static final int GIFT_TYPE_FIELD_NUMBER = 2;
    private int giftType_;
    /**
     * <pre>
     *动画类型：1:普通 2:交互 3:全屏webp 4:全屏入场webp 5:全屏Aiya
     * </pre>
     *
     * <code>int32 gift_type = 2;</code>
     */
    public int getGiftType() {
      return giftType_;
    }

    public static final int CARTON_LOAD_TYPE_FIELD_NUMBER = 3;
    private int cartonLoadType_;
    /**
     * <pre>
     *3: webp  7 svga
     * </pre>
     *
     * <code>int32 carton_load_type = 3;</code>
     */
    public int getCartonLoadType() {
      return cartonLoadType_;
    }

    public static final int GIFT_NUMBER_FIELD_NUMBER = 4;
    private int giftNumber_;
    /**
     * <pre>
     *礼物数量
     * </pre>
     *
     * <code>int32 gift_number = 4;</code>
     */
    public int getGiftNumber() {
      return giftNumber_;
    }

    public static final int GIFT_ICON_FIELD_NUMBER = 5;
    private volatile java.lang.Object giftIcon_;
    /**
     * <pre>
     *礼物图标
     * </pre>
     *
     * <code>string gift_icon = 5;</code>
     */
    public java.lang.String getGiftIcon() {
      java.lang.Object ref = giftIcon_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        giftIcon_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *礼物图标
     * </pre>
     *
     * <code>string gift_icon = 5;</code>
     */
    public com.google.protobuf.ByteString
        getGiftIconBytes() {
      java.lang.Object ref = giftIcon_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        giftIcon_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int GIFT_TIME_FIELD_NUMBER = 6;
    private long giftTime_;
    /**
     * <pre>
     *礼物时间
     * </pre>
     *
     * <code>int64 gift_time = 6;</code>
     */
    public long getGiftTime() {
      return giftTime_;
    }

    public static final int GIFT_PRICE_FIELD_NUMBER = 7;
    private int giftPrice_;
    /**
     * <pre>
     *礼物价格
     * </pre>
     *
     * <code>int32 gift_price = 7;</code>
     */
    public int getGiftPrice() {
      return giftPrice_;
    }

    public static final int GIFT_RANDOM_FIELD_NUMBER = 8;
    private int giftRandom_;
    /**
     * <pre>
     *是否是盲盒礼物 0否 1是
     * </pre>
     *
     * <code>int32 gift_random = 8;</code>
     */
    public int getGiftRandom() {
      return giftRandom_;
    }

    public static final int GIFT_RANDOM_ICON_FIELD_NUMBER = 9;
    private volatile java.lang.Object giftRandomIcon_;
    /**
     * <pre>
     *盲盒礼物图标
     * </pre>
     *
     * <code>string gift_random_icon = 9;</code>
     */
    public java.lang.String getGiftRandomIcon() {
      java.lang.Object ref = giftRandomIcon_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        giftRandomIcon_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *盲盒礼物图标
     * </pre>
     *
     * <code>string gift_random_icon = 9;</code>
     */
    public com.google.protobuf.ByteString
        getGiftRandomIconBytes() {
      java.lang.Object ref = giftRandomIcon_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        giftRandomIcon_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int GIFT_FUSION_FIELD_NUMBER = 10;
    private int giftFusion_;
    /**
     * <pre>
     * 是否融合动画礼物 0否 1是
     * </pre>
     *
     * <code>int32 gift_fusion = 10;</code>
     */
    public int getGiftFusion() {
      return giftFusion_;
    }

    public static final int FUSION_ID_FIELD_NUMBER = 11;
    private volatile java.lang.Object fusionId_;
    /**
     * <pre>
     * 融合用户id
     * </pre>
     *
     * <code>string fusion_id = 11;</code>
     */
    public java.lang.String getFusionId() {
      java.lang.Object ref = fusionId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        fusionId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 融合用户id
     * </pre>
     *
     * <code>string fusion_id = 11;</code>
     */
    public com.google.protobuf.ByteString
        getFusionIdBytes() {
      java.lang.Object ref = fusionId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fusionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FUSION_NAME_FIELD_NUMBER = 12;
    private volatile java.lang.Object fusionName_;
    /**
     * <pre>
     * 融合用户名称
     * </pre>
     *
     * <code>string fusion_name = 12;</code>
     */
    public java.lang.String getFusionName() {
      java.lang.Object ref = fusionName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        fusionName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 融合用户名称
     * </pre>
     *
     * <code>string fusion_name = 12;</code>
     */
    public com.google.protobuf.ByteString
        getFusionNameBytes() {
      java.lang.Object ref = fusionName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fusionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FUSION_HEAD_FIELD_NUMBER = 13;
    private volatile java.lang.Object fusionHead_;
    /**
     * <pre>
     * 融合用户头像
     * </pre>
     *
     * <code>string fusion_head = 13;</code>
     */
    public java.lang.String getFusionHead() {
      java.lang.Object ref = fusionHead_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        fusionHead_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 融合用户头像
     * </pre>
     *
     * <code>string fusion_head = 13;</code>
     */
    public com.google.protobuf.ByteString
        getFusionHeadBytes() {
      java.lang.Object ref = fusionHead_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fusionHead_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (giftId_ != 0) {
        output.writeInt32(1, giftId_);
      }
      if (giftType_ != 0) {
        output.writeInt32(2, giftType_);
      }
      if (cartonLoadType_ != 0) {
        output.writeInt32(3, cartonLoadType_);
      }
      if (giftNumber_ != 0) {
        output.writeInt32(4, giftNumber_);
      }
      if (!getGiftIconBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, giftIcon_);
      }
      if (giftTime_ != 0L) {
        output.writeInt64(6, giftTime_);
      }
      if (giftPrice_ != 0) {
        output.writeInt32(7, giftPrice_);
      }
      if (giftRandom_ != 0) {
        output.writeInt32(8, giftRandom_);
      }
      if (!getGiftRandomIconBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, giftRandomIcon_);
      }
      if (giftFusion_ != 0) {
        output.writeInt32(10, giftFusion_);
      }
      if (!getFusionIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, fusionId_);
      }
      if (!getFusionNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, fusionName_);
      }
      if (!getFusionHeadBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 13, fusionHead_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (giftId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, giftId_);
      }
      if (giftType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, giftType_);
      }
      if (cartonLoadType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, cartonLoadType_);
      }
      if (giftNumber_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, giftNumber_);
      }
      if (!getGiftIconBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, giftIcon_);
      }
      if (giftTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, giftTime_);
      }
      if (giftPrice_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, giftPrice_);
      }
      if (giftRandom_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, giftRandom_);
      }
      if (!getGiftRandomIconBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, giftRandomIcon_);
      }
      if (giftFusion_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, giftFusion_);
      }
      if (!getFusionIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, fusionId_);
      }
      if (!getFusionNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, fusionName_);
      }
      if (!getFusionHeadBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, fusionHead_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoGift.GiftInfo)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoGift.GiftInfo other = (com.quhong.proto.YoustarProtoGift.GiftInfo) obj;

      boolean result = true;
      result = result && (getGiftId()
          == other.getGiftId());
      result = result && (getGiftType()
          == other.getGiftType());
      result = result && (getCartonLoadType()
          == other.getCartonLoadType());
      result = result && (getGiftNumber()
          == other.getGiftNumber());
      result = result && getGiftIcon()
          .equals(other.getGiftIcon());
      result = result && (getGiftTime()
          == other.getGiftTime());
      result = result && (getGiftPrice()
          == other.getGiftPrice());
      result = result && (getGiftRandom()
          == other.getGiftRandom());
      result = result && getGiftRandomIcon()
          .equals(other.getGiftRandomIcon());
      result = result && (getGiftFusion()
          == other.getGiftFusion());
      result = result && getFusionId()
          .equals(other.getFusionId());
      result = result && getFusionName()
          .equals(other.getFusionName());
      result = result && getFusionHead()
          .equals(other.getFusionHead());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + GIFT_ID_FIELD_NUMBER;
      hash = (53 * hash) + getGiftId();
      hash = (37 * hash) + GIFT_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getGiftType();
      hash = (37 * hash) + CARTON_LOAD_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getCartonLoadType();
      hash = (37 * hash) + GIFT_NUMBER_FIELD_NUMBER;
      hash = (53 * hash) + getGiftNumber();
      hash = (37 * hash) + GIFT_ICON_FIELD_NUMBER;
      hash = (53 * hash) + getGiftIcon().hashCode();
      hash = (37 * hash) + GIFT_TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getGiftTime());
      hash = (37 * hash) + GIFT_PRICE_FIELD_NUMBER;
      hash = (53 * hash) + getGiftPrice();
      hash = (37 * hash) + GIFT_RANDOM_FIELD_NUMBER;
      hash = (53 * hash) + getGiftRandom();
      hash = (37 * hash) + GIFT_RANDOM_ICON_FIELD_NUMBER;
      hash = (53 * hash) + getGiftRandomIcon().hashCode();
      hash = (37 * hash) + GIFT_FUSION_FIELD_NUMBER;
      hash = (53 * hash) + getGiftFusion();
      hash = (37 * hash) + FUSION_ID_FIELD_NUMBER;
      hash = (53 * hash) + getFusionId().hashCode();
      hash = (37 * hash) + FUSION_NAME_FIELD_NUMBER;
      hash = (53 * hash) + getFusionName().hashCode();
      hash = (37 * hash) + FUSION_HEAD_FIELD_NUMBER;
      hash = (53 * hash) + getFusionHead().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoGift.GiftInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoGift.GiftInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.GiftInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoGift.GiftInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.GiftInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoGift.GiftInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.GiftInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoGift.GiftInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.GiftInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoGift.GiftInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoGift.GiftInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoGift.GiftInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoGift.GiftInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *礼物信息
     * </pre>
     *
     * Protobuf type {@code GiftInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:GiftInfo)
        com.quhong.proto.YoustarProtoGift.GiftInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoGift.internal_static_GiftInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoGift.internal_static_GiftInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoGift.GiftInfo.class, com.quhong.proto.YoustarProtoGift.GiftInfo.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoGift.GiftInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        giftId_ = 0;

        giftType_ = 0;

        cartonLoadType_ = 0;

        giftNumber_ = 0;

        giftIcon_ = "";

        giftTime_ = 0L;

        giftPrice_ = 0;

        giftRandom_ = 0;

        giftRandomIcon_ = "";

        giftFusion_ = 0;

        fusionId_ = "";

        fusionName_ = "";

        fusionHead_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoGift.internal_static_GiftInfo_descriptor;
      }

      public com.quhong.proto.YoustarProtoGift.GiftInfo getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoGift.GiftInfo.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoGift.GiftInfo build() {
        com.quhong.proto.YoustarProtoGift.GiftInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoGift.GiftInfo buildPartial() {
        com.quhong.proto.YoustarProtoGift.GiftInfo result = new com.quhong.proto.YoustarProtoGift.GiftInfo(this);
        result.giftId_ = giftId_;
        result.giftType_ = giftType_;
        result.cartonLoadType_ = cartonLoadType_;
        result.giftNumber_ = giftNumber_;
        result.giftIcon_ = giftIcon_;
        result.giftTime_ = giftTime_;
        result.giftPrice_ = giftPrice_;
        result.giftRandom_ = giftRandom_;
        result.giftRandomIcon_ = giftRandomIcon_;
        result.giftFusion_ = giftFusion_;
        result.fusionId_ = fusionId_;
        result.fusionName_ = fusionName_;
        result.fusionHead_ = fusionHead_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoGift.GiftInfo) {
          return mergeFrom((com.quhong.proto.YoustarProtoGift.GiftInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoGift.GiftInfo other) {
        if (other == com.quhong.proto.YoustarProtoGift.GiftInfo.getDefaultInstance()) return this;
        if (other.getGiftId() != 0) {
          setGiftId(other.getGiftId());
        }
        if (other.getGiftType() != 0) {
          setGiftType(other.getGiftType());
        }
        if (other.getCartonLoadType() != 0) {
          setCartonLoadType(other.getCartonLoadType());
        }
        if (other.getGiftNumber() != 0) {
          setGiftNumber(other.getGiftNumber());
        }
        if (!other.getGiftIcon().isEmpty()) {
          giftIcon_ = other.giftIcon_;
          onChanged();
        }
        if (other.getGiftTime() != 0L) {
          setGiftTime(other.getGiftTime());
        }
        if (other.getGiftPrice() != 0) {
          setGiftPrice(other.getGiftPrice());
        }
        if (other.getGiftRandom() != 0) {
          setGiftRandom(other.getGiftRandom());
        }
        if (!other.getGiftRandomIcon().isEmpty()) {
          giftRandomIcon_ = other.giftRandomIcon_;
          onChanged();
        }
        if (other.getGiftFusion() != 0) {
          setGiftFusion(other.getGiftFusion());
        }
        if (!other.getFusionId().isEmpty()) {
          fusionId_ = other.fusionId_;
          onChanged();
        }
        if (!other.getFusionName().isEmpty()) {
          fusionName_ = other.fusionName_;
          onChanged();
        }
        if (!other.getFusionHead().isEmpty()) {
          fusionHead_ = other.fusionHead_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoGift.GiftInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoGift.GiftInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int giftId_ ;
      /**
       * <pre>
       *礼物ID
       * </pre>
       *
       * <code>int32 gift_id = 1;</code>
       */
      public int getGiftId() {
        return giftId_;
      }
      /**
       * <pre>
       *礼物ID
       * </pre>
       *
       * <code>int32 gift_id = 1;</code>
       */
      public Builder setGiftId(int value) {
        
        giftId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *礼物ID
       * </pre>
       *
       * <code>int32 gift_id = 1;</code>
       */
      public Builder clearGiftId() {
        
        giftId_ = 0;
        onChanged();
        return this;
      }

      private int giftType_ ;
      /**
       * <pre>
       *动画类型：1:普通 2:交互 3:全屏webp 4:全屏入场webp 5:全屏Aiya
       * </pre>
       *
       * <code>int32 gift_type = 2;</code>
       */
      public int getGiftType() {
        return giftType_;
      }
      /**
       * <pre>
       *动画类型：1:普通 2:交互 3:全屏webp 4:全屏入场webp 5:全屏Aiya
       * </pre>
       *
       * <code>int32 gift_type = 2;</code>
       */
      public Builder setGiftType(int value) {
        
        giftType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *动画类型：1:普通 2:交互 3:全屏webp 4:全屏入场webp 5:全屏Aiya
       * </pre>
       *
       * <code>int32 gift_type = 2;</code>
       */
      public Builder clearGiftType() {
        
        giftType_ = 0;
        onChanged();
        return this;
      }

      private int cartonLoadType_ ;
      /**
       * <pre>
       *3: webp  7 svga
       * </pre>
       *
       * <code>int32 carton_load_type = 3;</code>
       */
      public int getCartonLoadType() {
        return cartonLoadType_;
      }
      /**
       * <pre>
       *3: webp  7 svga
       * </pre>
       *
       * <code>int32 carton_load_type = 3;</code>
       */
      public Builder setCartonLoadType(int value) {
        
        cartonLoadType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *3: webp  7 svga
       * </pre>
       *
       * <code>int32 carton_load_type = 3;</code>
       */
      public Builder clearCartonLoadType() {
        
        cartonLoadType_ = 0;
        onChanged();
        return this;
      }

      private int giftNumber_ ;
      /**
       * <pre>
       *礼物数量
       * </pre>
       *
       * <code>int32 gift_number = 4;</code>
       */
      public int getGiftNumber() {
        return giftNumber_;
      }
      /**
       * <pre>
       *礼物数量
       * </pre>
       *
       * <code>int32 gift_number = 4;</code>
       */
      public Builder setGiftNumber(int value) {
        
        giftNumber_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *礼物数量
       * </pre>
       *
       * <code>int32 gift_number = 4;</code>
       */
      public Builder clearGiftNumber() {
        
        giftNumber_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object giftIcon_ = "";
      /**
       * <pre>
       *礼物图标
       * </pre>
       *
       * <code>string gift_icon = 5;</code>
       */
      public java.lang.String getGiftIcon() {
        java.lang.Object ref = giftIcon_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          giftIcon_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *礼物图标
       * </pre>
       *
       * <code>string gift_icon = 5;</code>
       */
      public com.google.protobuf.ByteString
          getGiftIconBytes() {
        java.lang.Object ref = giftIcon_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          giftIcon_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *礼物图标
       * </pre>
       *
       * <code>string gift_icon = 5;</code>
       */
      public Builder setGiftIcon(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        giftIcon_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *礼物图标
       * </pre>
       *
       * <code>string gift_icon = 5;</code>
       */
      public Builder clearGiftIcon() {
        
        giftIcon_ = getDefaultInstance().getGiftIcon();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *礼物图标
       * </pre>
       *
       * <code>string gift_icon = 5;</code>
       */
      public Builder setGiftIconBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        giftIcon_ = value;
        onChanged();
        return this;
      }

      private long giftTime_ ;
      /**
       * <pre>
       *礼物时间
       * </pre>
       *
       * <code>int64 gift_time = 6;</code>
       */
      public long getGiftTime() {
        return giftTime_;
      }
      /**
       * <pre>
       *礼物时间
       * </pre>
       *
       * <code>int64 gift_time = 6;</code>
       */
      public Builder setGiftTime(long value) {
        
        giftTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *礼物时间
       * </pre>
       *
       * <code>int64 gift_time = 6;</code>
       */
      public Builder clearGiftTime() {
        
        giftTime_ = 0L;
        onChanged();
        return this;
      }

      private int giftPrice_ ;
      /**
       * <pre>
       *礼物价格
       * </pre>
       *
       * <code>int32 gift_price = 7;</code>
       */
      public int getGiftPrice() {
        return giftPrice_;
      }
      /**
       * <pre>
       *礼物价格
       * </pre>
       *
       * <code>int32 gift_price = 7;</code>
       */
      public Builder setGiftPrice(int value) {
        
        giftPrice_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *礼物价格
       * </pre>
       *
       * <code>int32 gift_price = 7;</code>
       */
      public Builder clearGiftPrice() {
        
        giftPrice_ = 0;
        onChanged();
        return this;
      }

      private int giftRandom_ ;
      /**
       * <pre>
       *是否是盲盒礼物 0否 1是
       * </pre>
       *
       * <code>int32 gift_random = 8;</code>
       */
      public int getGiftRandom() {
        return giftRandom_;
      }
      /**
       * <pre>
       *是否是盲盒礼物 0否 1是
       * </pre>
       *
       * <code>int32 gift_random = 8;</code>
       */
      public Builder setGiftRandom(int value) {
        
        giftRandom_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否是盲盒礼物 0否 1是
       * </pre>
       *
       * <code>int32 gift_random = 8;</code>
       */
      public Builder clearGiftRandom() {
        
        giftRandom_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object giftRandomIcon_ = "";
      /**
       * <pre>
       *盲盒礼物图标
       * </pre>
       *
       * <code>string gift_random_icon = 9;</code>
       */
      public java.lang.String getGiftRandomIcon() {
        java.lang.Object ref = giftRandomIcon_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          giftRandomIcon_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *盲盒礼物图标
       * </pre>
       *
       * <code>string gift_random_icon = 9;</code>
       */
      public com.google.protobuf.ByteString
          getGiftRandomIconBytes() {
        java.lang.Object ref = giftRandomIcon_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          giftRandomIcon_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *盲盒礼物图标
       * </pre>
       *
       * <code>string gift_random_icon = 9;</code>
       */
      public Builder setGiftRandomIcon(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        giftRandomIcon_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *盲盒礼物图标
       * </pre>
       *
       * <code>string gift_random_icon = 9;</code>
       */
      public Builder clearGiftRandomIcon() {
        
        giftRandomIcon_ = getDefaultInstance().getGiftRandomIcon();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *盲盒礼物图标
       * </pre>
       *
       * <code>string gift_random_icon = 9;</code>
       */
      public Builder setGiftRandomIconBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        giftRandomIcon_ = value;
        onChanged();
        return this;
      }

      private int giftFusion_ ;
      /**
       * <pre>
       * 是否融合动画礼物 0否 1是
       * </pre>
       *
       * <code>int32 gift_fusion = 10;</code>
       */
      public int getGiftFusion() {
        return giftFusion_;
      }
      /**
       * <pre>
       * 是否融合动画礼物 0否 1是
       * </pre>
       *
       * <code>int32 gift_fusion = 10;</code>
       */
      public Builder setGiftFusion(int value) {
        
        giftFusion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否融合动画礼物 0否 1是
       * </pre>
       *
       * <code>int32 gift_fusion = 10;</code>
       */
      public Builder clearGiftFusion() {
        
        giftFusion_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object fusionId_ = "";
      /**
       * <pre>
       * 融合用户id
       * </pre>
       *
       * <code>string fusion_id = 11;</code>
       */
      public java.lang.String getFusionId() {
        java.lang.Object ref = fusionId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          fusionId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 融合用户id
       * </pre>
       *
       * <code>string fusion_id = 11;</code>
       */
      public com.google.protobuf.ByteString
          getFusionIdBytes() {
        java.lang.Object ref = fusionId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fusionId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 融合用户id
       * </pre>
       *
       * <code>string fusion_id = 11;</code>
       */
      public Builder setFusionId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        fusionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 融合用户id
       * </pre>
       *
       * <code>string fusion_id = 11;</code>
       */
      public Builder clearFusionId() {
        
        fusionId_ = getDefaultInstance().getFusionId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 融合用户id
       * </pre>
       *
       * <code>string fusion_id = 11;</code>
       */
      public Builder setFusionIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        fusionId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fusionName_ = "";
      /**
       * <pre>
       * 融合用户名称
       * </pre>
       *
       * <code>string fusion_name = 12;</code>
       */
      public java.lang.String getFusionName() {
        java.lang.Object ref = fusionName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          fusionName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 融合用户名称
       * </pre>
       *
       * <code>string fusion_name = 12;</code>
       */
      public com.google.protobuf.ByteString
          getFusionNameBytes() {
        java.lang.Object ref = fusionName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fusionName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 融合用户名称
       * </pre>
       *
       * <code>string fusion_name = 12;</code>
       */
      public Builder setFusionName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        fusionName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 融合用户名称
       * </pre>
       *
       * <code>string fusion_name = 12;</code>
       */
      public Builder clearFusionName() {
        
        fusionName_ = getDefaultInstance().getFusionName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 融合用户名称
       * </pre>
       *
       * <code>string fusion_name = 12;</code>
       */
      public Builder setFusionNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        fusionName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object fusionHead_ = "";
      /**
       * <pre>
       * 融合用户头像
       * </pre>
       *
       * <code>string fusion_head = 13;</code>
       */
      public java.lang.String getFusionHead() {
        java.lang.Object ref = fusionHead_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          fusionHead_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 融合用户头像
       * </pre>
       *
       * <code>string fusion_head = 13;</code>
       */
      public com.google.protobuf.ByteString
          getFusionHeadBytes() {
        java.lang.Object ref = fusionHead_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fusionHead_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 融合用户头像
       * </pre>
       *
       * <code>string fusion_head = 13;</code>
       */
      public Builder setFusionHead(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        fusionHead_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 融合用户头像
       * </pre>
       *
       * <code>string fusion_head = 13;</code>
       */
      public Builder clearFusionHead() {
        
        fusionHead_ = getDefaultInstance().getFusionHead();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 融合用户头像
       * </pre>
       *
       * <code>string fusion_head = 13;</code>
       */
      public Builder setFusionHeadBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        fusionHead_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:GiftInfo)
    }

    // @@protoc_insertion_point(class_scope:GiftInfo)
    private static final com.quhong.proto.YoustarProtoGift.GiftInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoGift.GiftInfo();
    }

    public static com.quhong.proto.YoustarProtoGift.GiftInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GiftInfo>
        PARSER = new com.google.protobuf.AbstractParser<GiftInfo>() {
      public GiftInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new GiftInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GiftInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GiftInfo> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoGift.GiftInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ReceiveInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ReceiveInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SendInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SendInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_GiftInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_GiftInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\ngift.proto\032\013uname.proto\"\221\001\n\013ReceiveInf" +
      "o\022\014\n\004name\030\001 \001(\t\022\013\n\003uid\030\002 \001(\t\022\014\n\004head\030\003 \001" +
      "(\t\022\021\n\tvip_level\030\004 \001(\005\022\014\n\004ulvl\030\005 \001(\005\022\020\n\010i" +
      "dentify\030\006 \001(\005\022\013\n\003rid\030\007 \001(\t\022\031\n\007ridInfo\030\010 " +
      "\001(\0132\010.RidInfo\"\347\001\n\010SendInfo\022\r\n\005beans\030\001 \001(" +
      "\005\022\014\n\004name\030\002 \001(\t\022\014\n\004head\030\003 \001(\t\022\013\n\003uid\030\004 \001" +
      "(\t\022\021\n\tvip_level\030\005 \001(\005\022\014\n\004ulvl\030\006 \001(\005\022\022\n\nb" +
      "adge_list\030\007 \003(\t\022\020\n\010identify\030\010 \001(\005\022\014\n\004rol" +
      "e\030\t \001(\005\022\013\n\003rid\030\n \001(\t\022\023\n\013newVipLevel\030\013 \001(" +
      "\005\022\031\n\007ridInfo\030\014 \001(\0132\010.RidInfo\022\021\n\tisNewUse",
      "r\030\r \001(\005\"\230\002\n\010GiftInfo\022\017\n\007gift_id\030\001 \001(\005\022\021\n" +
      "\tgift_type\030\002 \001(\005\022\030\n\020carton_load_type\030\003 \001" +
      "(\005\022\023\n\013gift_number\030\004 \001(\005\022\021\n\tgift_icon\030\005 \001" +
      "(\t\022\021\n\tgift_time\030\006 \001(\003\022\022\n\ngift_price\030\007 \001(" +
      "\005\022\023\n\013gift_random\030\010 \001(\005\022\030\n\020gift_random_ic" +
      "on\030\t \001(\t\022\023\n\013gift_fusion\030\n \001(\005\022\021\n\tfusion_" +
      "id\030\013 \001(\t\022\023\n\013fusion_name\030\014 \001(\t\022\023\n\013fusion_" +
      "head\030\r \001(\tB$\n\020com.quhong.protoB\020YoustarP" +
      "rotoGiftb\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.quhong.proto.YoustarProtoUname.getDescriptor(),
        }, assigner);
    internal_static_ReceiveInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ReceiveInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ReceiveInfo_descriptor,
        new java.lang.String[] { "Name", "Uid", "Head", "VipLevel", "Ulvl", "Identify", "Rid", "RidInfo", });
    internal_static_SendInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_SendInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SendInfo_descriptor,
        new java.lang.String[] { "Beans", "Name", "Head", "Uid", "VipLevel", "Ulvl", "BadgeList", "Identify", "Role", "Rid", "NewVipLevel", "RidInfo", "IsNewUser", });
    internal_static_GiftInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_GiftInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_GiftInfo_descriptor,
        new java.lang.String[] { "GiftId", "GiftType", "CartonLoadType", "GiftNumber", "GiftIcon", "GiftTime", "GiftPrice", "GiftRandom", "GiftRandomIcon", "GiftFusion", "FusionId", "FusionName", "FusionHead", });
    com.quhong.proto.YoustarProtoUname.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
