// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: mic_info.proto

package com.quhong.proto;

public final class YoustarProtoRoomMicInfo {
  private YoustarProtoRoomMicInfo() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface RoomMicInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:RoomMicInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 麦位位置
     * </pre>
     *
     * <code>int32 index = 1;</code>
     */
    int getIndex();

    /**
     * <pre>
     * 麦位状态 麦位状态 0空闲 1有人 2上锁
     * </pre>
     *
     * <code>int32 status = 2;</code>
     */
    int getStatus();

    /**
     * <pre>
     * 麦位禁音 0未静音 1静音
     * </pre>
     *
     * <code>int32 mute = 3;</code>
     */
    int getMute();

    /**
     * <pre>
     * 麦位用户信息 status = 1返回
     * </pre>
     *
     * <code>.RoomMicUser user = 4;</code>
     */
    boolean hasUser();
    /**
     * <pre>
     * 麦位用户信息 status = 1返回
     * </pre>
     *
     * <code>.RoomMicUser user = 4;</code>
     */
    com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser getUser();
    /**
     * <pre>
     * 麦位用户信息 status = 1返回
     * </pre>
     *
     * <code>.RoomMicUser user = 4;</code>
     */
    com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUserOrBuilder getUserOrBuilder();
  }
  /**
   * Protobuf type {@code RoomMicInfo}
   */
  public  static final class RoomMicInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:RoomMicInfo)
      RoomMicInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RoomMicInfo.newBuilder() to construct.
    private RoomMicInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RoomMicInfo() {
      index_ = 0;
      status_ = 0;
      mute_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RoomMicInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              index_ = input.readInt32();
              break;
            }
            case 16: {

              status_ = input.readInt32();
              break;
            }
            case 24: {

              mute_ = input.readInt32();
              break;
            }
            case 34: {
              com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.Builder subBuilder = null;
              if (user_ != null) {
                subBuilder = user_.toBuilder();
              }
              user_ = input.readMessage(com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(user_);
                user_ = subBuilder.buildPartial();
              }

              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoRoomMicInfo.internal_static_RoomMicInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoRoomMicInfo.internal_static_RoomMicInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo.class, com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo.Builder.class);
    }

    public static final int INDEX_FIELD_NUMBER = 1;
    private int index_;
    /**
     * <pre>
     * 麦位位置
     * </pre>
     *
     * <code>int32 index = 1;</code>
     */
    public int getIndex() {
      return index_;
    }

    public static final int STATUS_FIELD_NUMBER = 2;
    private int status_;
    /**
     * <pre>
     * 麦位状态 麦位状态 0空闲 1有人 2上锁
     * </pre>
     *
     * <code>int32 status = 2;</code>
     */
    public int getStatus() {
      return status_;
    }

    public static final int MUTE_FIELD_NUMBER = 3;
    private int mute_;
    /**
     * <pre>
     * 麦位禁音 0未静音 1静音
     * </pre>
     *
     * <code>int32 mute = 3;</code>
     */
    public int getMute() {
      return mute_;
    }

    public static final int USER_FIELD_NUMBER = 4;
    private com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser user_;
    /**
     * <pre>
     * 麦位用户信息 status = 1返回
     * </pre>
     *
     * <code>.RoomMicUser user = 4;</code>
     */
    public boolean hasUser() {
      return user_ != null;
    }
    /**
     * <pre>
     * 麦位用户信息 status = 1返回
     * </pre>
     *
     * <code>.RoomMicUser user = 4;</code>
     */
    public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser getUser() {
      return user_ == null ? com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.getDefaultInstance() : user_;
    }
    /**
     * <pre>
     * 麦位用户信息 status = 1返回
     * </pre>
     *
     * <code>.RoomMicUser user = 4;</code>
     */
    public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUserOrBuilder getUserOrBuilder() {
      return getUser();
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (index_ != 0) {
        output.writeInt32(1, index_);
      }
      if (status_ != 0) {
        output.writeInt32(2, status_);
      }
      if (mute_ != 0) {
        output.writeInt32(3, mute_);
      }
      if (user_ != null) {
        output.writeMessage(4, getUser());
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, index_);
      }
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, status_);
      }
      if (mute_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, mute_);
      }
      if (user_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getUser());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo other = (com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo) obj;

      boolean result = true;
      result = result && (getIndex()
          == other.getIndex());
      result = result && (getStatus()
          == other.getStatus());
      result = result && (getMute()
          == other.getMute());
      result = result && (hasUser() == other.hasUser());
      if (hasUser()) {
        result = result && getUser()
            .equals(other.getUser());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (37 * hash) + MUTE_FIELD_NUMBER;
      hash = (53 * hash) + getMute();
      if (hasUser()) {
        hash = (37 * hash) + USER_FIELD_NUMBER;
        hash = (53 * hash) + getUser().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code RoomMicInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:RoomMicInfo)
        com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoRoomMicInfo.internal_static_RoomMicInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoRoomMicInfo.internal_static_RoomMicInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo.class, com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        index_ = 0;

        status_ = 0;

        mute_ = 0;

        if (userBuilder_ == null) {
          user_ = null;
        } else {
          user_ = null;
          userBuilder_ = null;
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoRoomMicInfo.internal_static_RoomMicInfo_descriptor;
      }

      public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo build() {
        com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo buildPartial() {
        com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo result = new com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo(this);
        result.index_ = index_;
        result.status_ = status_;
        result.mute_ = mute_;
        if (userBuilder_ == null) {
          result.user_ = user_;
        } else {
          result.user_ = userBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo) {
          return mergeFrom((com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo other) {
        if (other == com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo.getDefaultInstance()) return this;
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        if (other.getMute() != 0) {
          setMute(other.getMute());
        }
        if (other.hasUser()) {
          mergeUser(other.getUser());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       * 麦位位置
       * </pre>
       *
       * <code>int32 index = 1;</code>
       */
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       * 麦位位置
       * </pre>
       *
       * <code>int32 index = 1;</code>
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 麦位位置
       * </pre>
       *
       * <code>int32 index = 1;</code>
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }

      private int status_ ;
      /**
       * <pre>
       * 麦位状态 麦位状态 0空闲 1有人 2上锁
       * </pre>
       *
       * <code>int32 status = 2;</code>
       */
      public int getStatus() {
        return status_;
      }
      /**
       * <pre>
       * 麦位状态 麦位状态 0空闲 1有人 2上锁
       * </pre>
       *
       * <code>int32 status = 2;</code>
       */
      public Builder setStatus(int value) {
        
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 麦位状态 麦位状态 0空闲 1有人 2上锁
       * </pre>
       *
       * <code>int32 status = 2;</code>
       */
      public Builder clearStatus() {
        
        status_ = 0;
        onChanged();
        return this;
      }

      private int mute_ ;
      /**
       * <pre>
       * 麦位禁音 0未静音 1静音
       * </pre>
       *
       * <code>int32 mute = 3;</code>
       */
      public int getMute() {
        return mute_;
      }
      /**
       * <pre>
       * 麦位禁音 0未静音 1静音
       * </pre>
       *
       * <code>int32 mute = 3;</code>
       */
      public Builder setMute(int value) {
        
        mute_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 麦位禁音 0未静音 1静音
       * </pre>
       *
       * <code>int32 mute = 3;</code>
       */
      public Builder clearMute() {
        
        mute_ = 0;
        onChanged();
        return this;
      }

      private com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser user_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser, com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.Builder, com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUserOrBuilder> userBuilder_;
      /**
       * <pre>
       * 麦位用户信息 status = 1返回
       * </pre>
       *
       * <code>.RoomMicUser user = 4;</code>
       */
      public boolean hasUser() {
        return userBuilder_ != null || user_ != null;
      }
      /**
       * <pre>
       * 麦位用户信息 status = 1返回
       * </pre>
       *
       * <code>.RoomMicUser user = 4;</code>
       */
      public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser getUser() {
        if (userBuilder_ == null) {
          return user_ == null ? com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.getDefaultInstance() : user_;
        } else {
          return userBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 麦位用户信息 status = 1返回
       * </pre>
       *
       * <code>.RoomMicUser user = 4;</code>
       */
      public Builder setUser(com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser value) {
        if (userBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          user_ = value;
          onChanged();
        } else {
          userBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 麦位用户信息 status = 1返回
       * </pre>
       *
       * <code>.RoomMicUser user = 4;</code>
       */
      public Builder setUser(
          com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.Builder builderForValue) {
        if (userBuilder_ == null) {
          user_ = builderForValue.build();
          onChanged();
        } else {
          userBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 麦位用户信息 status = 1返回
       * </pre>
       *
       * <code>.RoomMicUser user = 4;</code>
       */
      public Builder mergeUser(com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser value) {
        if (userBuilder_ == null) {
          if (user_ != null) {
            user_ =
              com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.newBuilder(user_).mergeFrom(value).buildPartial();
          } else {
            user_ = value;
          }
          onChanged();
        } else {
          userBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 麦位用户信息 status = 1返回
       * </pre>
       *
       * <code>.RoomMicUser user = 4;</code>
       */
      public Builder clearUser() {
        if (userBuilder_ == null) {
          user_ = null;
          onChanged();
        } else {
          user_ = null;
          userBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 麦位用户信息 status = 1返回
       * </pre>
       *
       * <code>.RoomMicUser user = 4;</code>
       */
      public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.Builder getUserBuilder() {
        
        onChanged();
        return getUserFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 麦位用户信息 status = 1返回
       * </pre>
       *
       * <code>.RoomMicUser user = 4;</code>
       */
      public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUserOrBuilder getUserOrBuilder() {
        if (userBuilder_ != null) {
          return userBuilder_.getMessageOrBuilder();
        } else {
          return user_ == null ?
              com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.getDefaultInstance() : user_;
        }
      }
      /**
       * <pre>
       * 麦位用户信息 status = 1返回
       * </pre>
       *
       * <code>.RoomMicUser user = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser, com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.Builder, com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUserOrBuilder> 
          getUserFieldBuilder() {
        if (userBuilder_ == null) {
          userBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser, com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.Builder, com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUserOrBuilder>(
                  getUser(),
                  getParentForChildren(),
                  isClean());
          user_ = null;
        }
        return userBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:RoomMicInfo)
    }

    // @@protoc_insertion_point(class_scope:RoomMicInfo)
    private static final com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo();
    }

    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RoomMicInfo>
        PARSER = new com.google.protobuf.AbstractParser<RoomMicInfo>() {
      public RoomMicInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new RoomMicInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RoomMicInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RoomMicInfo> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RoomMicUserOrBuilder extends
      // @@protoc_insertion_point(interface_extends:RoomMicUser)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *头像
     * </pre>
     *
     * <code>string head = 1;</code>
     */
    java.lang.String getHead();
    /**
     * <pre>
     *头像
     * </pre>
     *
     * <code>string head = 1;</code>
     */
    com.google.protobuf.ByteString
        getHeadBytes();

    /**
     * <pre>
     *名字
     * </pre>
     *
     * <code>string name = 2;</code>
     */
    java.lang.String getName();
    /**
     * <pre>
     *名字
     * </pre>
     *
     * <code>string name = 2;</code>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <pre>
     *用户id
     * </pre>
     *
     * <code>string aid = 3;</code>
     */
    java.lang.String getAid();
    /**
     * <pre>
     *用户id
     * </pre>
     *
     * <code>string aid = 3;</code>
     */
    com.google.protobuf.ByteString
        getAidBytes();

    /**
     * <pre>
     *vip等级
     * </pre>
     *
     * <code>int32 vip_level = 4;</code>
     */
    int getVipLevel();

    /**
     * <pre>
     *麦位框链接地址
     * </pre>
     *
     * <code>string mic_frame = 5;</code>
     */
    java.lang.String getMicFrame();
    /**
     * <pre>
     *麦位框链接地址
     * </pre>
     *
     * <code>string mic_frame = 5;</code>
     */
    com.google.protobuf.ByteString
        getMicFrameBytes();

    /**
     * <pre>
     *波纹链接地址
     * </pre>
     *
     * <code>string ripple_url = 6;</code>
     */
    java.lang.String getRippleUrl();
    /**
     * <pre>
     *波纹链接地址
     * </pre>
     *
     * <code>string ripple_url = 6;</code>
     */
    com.google.protobuf.ByteString
        getRippleUrlBytes();

    /**
     * <pre>
     *身份标识
     * </pre>
     *
     * <code>int32 role = 7;</code>
     */
    int getRole();

    /**
     * <pre>
     * 0 无 1 vip 2 svip
     * </pre>
     *
     * <code>int32 identify = 8;</code>
     */
    int getIdentify();

    /**
     * <pre>
     * 0 否 1 是
     * </pre>
     *
     * <code>int32 viceHost = 9;</code>
     */
    int getViceHost();

    /**
     * <pre>
     * 变声类型，0原声、1女声、2男声、3擎天柱、4孩童
     * </pre>
     *
     * <code>int32 voice_type = 10;</code>
     */
    int getVoiceType();

    /**
     * <pre>
     * 用户流id
     * </pre>
     *
     * <code>string streamId = 11;</code>
     */
    java.lang.String getStreamId();
    /**
     * <pre>
     * 用户流id
     * </pre>
     *
     * <code>string streamId = 11;</code>
     */
    com.google.protobuf.ByteString
        getStreamIdBytes();

    /**
     * <code>int32 newVipLevel = 12;</code>
     */
    int getNewVipLevel();

    /**
     * <pre>
     * 是否游戏中 0 没有游戏中 1在游戏中
     * </pre>
     *
     * <code>int32 gameRunning = 13;</code>
     */
    int getGameRunning();
  }
  /**
   * Protobuf type {@code RoomMicUser}
   */
  public  static final class RoomMicUser extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:RoomMicUser)
      RoomMicUserOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RoomMicUser.newBuilder() to construct.
    private RoomMicUser(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RoomMicUser() {
      head_ = "";
      name_ = "";
      aid_ = "";
      vipLevel_ = 0;
      micFrame_ = "";
      rippleUrl_ = "";
      role_ = 0;
      identify_ = 0;
      viceHost_ = 0;
      voiceType_ = 0;
      streamId_ = "";
      newVipLevel_ = 0;
      gameRunning_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RoomMicUser(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              head_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              aid_ = s;
              break;
            }
            case 32: {

              vipLevel_ = input.readInt32();
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              micFrame_ = s;
              break;
            }
            case 50: {
              java.lang.String s = input.readStringRequireUtf8();

              rippleUrl_ = s;
              break;
            }
            case 56: {

              role_ = input.readInt32();
              break;
            }
            case 64: {

              identify_ = input.readInt32();
              break;
            }
            case 72: {

              viceHost_ = input.readInt32();
              break;
            }
            case 80: {

              voiceType_ = input.readInt32();
              break;
            }
            case 90: {
              java.lang.String s = input.readStringRequireUtf8();

              streamId_ = s;
              break;
            }
            case 96: {

              newVipLevel_ = input.readInt32();
              break;
            }
            case 104: {

              gameRunning_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoRoomMicInfo.internal_static_RoomMicUser_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoRoomMicInfo.internal_static_RoomMicUser_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.class, com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.Builder.class);
    }

    public static final int HEAD_FIELD_NUMBER = 1;
    private volatile java.lang.Object head_;
    /**
     * <pre>
     *头像
     * </pre>
     *
     * <code>string head = 1;</code>
     */
    public java.lang.String getHead() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        head_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *头像
     * </pre>
     *
     * <code>string head = 1;</code>
     */
    public com.google.protobuf.ByteString
        getHeadBytes() {
      java.lang.Object ref = head_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        head_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <pre>
     *名字
     * </pre>
     *
     * <code>string name = 2;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *名字
     * </pre>
     *
     * <code>string name = 2;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int AID_FIELD_NUMBER = 3;
    private volatile java.lang.Object aid_;
    /**
     * <pre>
     *用户id
     * </pre>
     *
     * <code>string aid = 3;</code>
     */
    public java.lang.String getAid() {
      java.lang.Object ref = aid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        aid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *用户id
     * </pre>
     *
     * <code>string aid = 3;</code>
     */
    public com.google.protobuf.ByteString
        getAidBytes() {
      java.lang.Object ref = aid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        aid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VIP_LEVEL_FIELD_NUMBER = 4;
    private int vipLevel_;
    /**
     * <pre>
     *vip等级
     * </pre>
     *
     * <code>int32 vip_level = 4;</code>
     */
    public int getVipLevel() {
      return vipLevel_;
    }

    public static final int MIC_FRAME_FIELD_NUMBER = 5;
    private volatile java.lang.Object micFrame_;
    /**
     * <pre>
     *麦位框链接地址
     * </pre>
     *
     * <code>string mic_frame = 5;</code>
     */
    public java.lang.String getMicFrame() {
      java.lang.Object ref = micFrame_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        micFrame_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *麦位框链接地址
     * </pre>
     *
     * <code>string mic_frame = 5;</code>
     */
    public com.google.protobuf.ByteString
        getMicFrameBytes() {
      java.lang.Object ref = micFrame_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        micFrame_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int RIPPLE_URL_FIELD_NUMBER = 6;
    private volatile java.lang.Object rippleUrl_;
    /**
     * <pre>
     *波纹链接地址
     * </pre>
     *
     * <code>string ripple_url = 6;</code>
     */
    public java.lang.String getRippleUrl() {
      java.lang.Object ref = rippleUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        rippleUrl_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *波纹链接地址
     * </pre>
     *
     * <code>string ripple_url = 6;</code>
     */
    public com.google.protobuf.ByteString
        getRippleUrlBytes() {
      java.lang.Object ref = rippleUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rippleUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ROLE_FIELD_NUMBER = 7;
    private int role_;
    /**
     * <pre>
     *身份标识
     * </pre>
     *
     * <code>int32 role = 7;</code>
     */
    public int getRole() {
      return role_;
    }

    public static final int IDENTIFY_FIELD_NUMBER = 8;
    private int identify_;
    /**
     * <pre>
     * 0 无 1 vip 2 svip
     * </pre>
     *
     * <code>int32 identify = 8;</code>
     */
    public int getIdentify() {
      return identify_;
    }

    public static final int VICEHOST_FIELD_NUMBER = 9;
    private int viceHost_;
    /**
     * <pre>
     * 0 否 1 是
     * </pre>
     *
     * <code>int32 viceHost = 9;</code>
     */
    public int getViceHost() {
      return viceHost_;
    }

    public static final int VOICE_TYPE_FIELD_NUMBER = 10;
    private int voiceType_;
    /**
     * <pre>
     * 变声类型，0原声、1女声、2男声、3擎天柱、4孩童
     * </pre>
     *
     * <code>int32 voice_type = 10;</code>
     */
    public int getVoiceType() {
      return voiceType_;
    }

    public static final int STREAMID_FIELD_NUMBER = 11;
    private volatile java.lang.Object streamId_;
    /**
     * <pre>
     * 用户流id
     * </pre>
     *
     * <code>string streamId = 11;</code>
     */
    public java.lang.String getStreamId() {
      java.lang.Object ref = streamId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        streamId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 用户流id
     * </pre>
     *
     * <code>string streamId = 11;</code>
     */
    public com.google.protobuf.ByteString
        getStreamIdBytes() {
      java.lang.Object ref = streamId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        streamId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NEWVIPLEVEL_FIELD_NUMBER = 12;
    private int newVipLevel_;
    /**
     * <code>int32 newVipLevel = 12;</code>
     */
    public int getNewVipLevel() {
      return newVipLevel_;
    }

    public static final int GAMERUNNING_FIELD_NUMBER = 13;
    private int gameRunning_;
    /**
     * <pre>
     * 是否游戏中 0 没有游戏中 1在游戏中
     * </pre>
     *
     * <code>int32 gameRunning = 13;</code>
     */
    public int getGameRunning() {
      return gameRunning_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getHeadBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, head_);
      }
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      if (!getAidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, aid_);
      }
      if (vipLevel_ != 0) {
        output.writeInt32(4, vipLevel_);
      }
      if (!getMicFrameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, micFrame_);
      }
      if (!getRippleUrlBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, rippleUrl_);
      }
      if (role_ != 0) {
        output.writeInt32(7, role_);
      }
      if (identify_ != 0) {
        output.writeInt32(8, identify_);
      }
      if (viceHost_ != 0) {
        output.writeInt32(9, viceHost_);
      }
      if (voiceType_ != 0) {
        output.writeInt32(10, voiceType_);
      }
      if (!getStreamIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, streamId_);
      }
      if (newVipLevel_ != 0) {
        output.writeInt32(12, newVipLevel_);
      }
      if (gameRunning_ != 0) {
        output.writeInt32(13, gameRunning_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getHeadBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, head_);
      }
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      if (!getAidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, aid_);
      }
      if (vipLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, vipLevel_);
      }
      if (!getMicFrameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, micFrame_);
      }
      if (!getRippleUrlBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, rippleUrl_);
      }
      if (role_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, role_);
      }
      if (identify_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, identify_);
      }
      if (viceHost_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, viceHost_);
      }
      if (voiceType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, voiceType_);
      }
      if (!getStreamIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, streamId_);
      }
      if (newVipLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(12, newVipLevel_);
      }
      if (gameRunning_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(13, gameRunning_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser other = (com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser) obj;

      boolean result = true;
      result = result && getHead()
          .equals(other.getHead());
      result = result && getName()
          .equals(other.getName());
      result = result && getAid()
          .equals(other.getAid());
      result = result && (getVipLevel()
          == other.getVipLevel());
      result = result && getMicFrame()
          .equals(other.getMicFrame());
      result = result && getRippleUrl()
          .equals(other.getRippleUrl());
      result = result && (getRole()
          == other.getRole());
      result = result && (getIdentify()
          == other.getIdentify());
      result = result && (getViceHost()
          == other.getViceHost());
      result = result && (getVoiceType()
          == other.getVoiceType());
      result = result && getStreamId()
          .equals(other.getStreamId());
      result = result && (getNewVipLevel()
          == other.getNewVipLevel());
      result = result && (getGameRunning()
          == other.getGameRunning());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + HEAD_FIELD_NUMBER;
      hash = (53 * hash) + getHead().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + AID_FIELD_NUMBER;
      hash = (53 * hash) + getAid().hashCode();
      hash = (37 * hash) + VIP_LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getVipLevel();
      hash = (37 * hash) + MIC_FRAME_FIELD_NUMBER;
      hash = (53 * hash) + getMicFrame().hashCode();
      hash = (37 * hash) + RIPPLE_URL_FIELD_NUMBER;
      hash = (53 * hash) + getRippleUrl().hashCode();
      hash = (37 * hash) + ROLE_FIELD_NUMBER;
      hash = (53 * hash) + getRole();
      hash = (37 * hash) + IDENTIFY_FIELD_NUMBER;
      hash = (53 * hash) + getIdentify();
      hash = (37 * hash) + VICEHOST_FIELD_NUMBER;
      hash = (53 * hash) + getViceHost();
      hash = (37 * hash) + VOICE_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getVoiceType();
      hash = (37 * hash) + STREAMID_FIELD_NUMBER;
      hash = (53 * hash) + getStreamId().hashCode();
      hash = (37 * hash) + NEWVIPLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getNewVipLevel();
      hash = (37 * hash) + GAMERUNNING_FIELD_NUMBER;
      hash = (53 * hash) + getGameRunning();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code RoomMicUser}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:RoomMicUser)
        com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUserOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoRoomMicInfo.internal_static_RoomMicUser_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoRoomMicInfo.internal_static_RoomMicUser_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.class, com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        head_ = "";

        name_ = "";

        aid_ = "";

        vipLevel_ = 0;

        micFrame_ = "";

        rippleUrl_ = "";

        role_ = 0;

        identify_ = 0;

        viceHost_ = 0;

        voiceType_ = 0;

        streamId_ = "";

        newVipLevel_ = 0;

        gameRunning_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoRoomMicInfo.internal_static_RoomMicUser_descriptor;
      }

      public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser build() {
        com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser buildPartial() {
        com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser result = new com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser(this);
        result.head_ = head_;
        result.name_ = name_;
        result.aid_ = aid_;
        result.vipLevel_ = vipLevel_;
        result.micFrame_ = micFrame_;
        result.rippleUrl_ = rippleUrl_;
        result.role_ = role_;
        result.identify_ = identify_;
        result.viceHost_ = viceHost_;
        result.voiceType_ = voiceType_;
        result.streamId_ = streamId_;
        result.newVipLevel_ = newVipLevel_;
        result.gameRunning_ = gameRunning_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser) {
          return mergeFrom((com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser other) {
        if (other == com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser.getDefaultInstance()) return this;
        if (!other.getHead().isEmpty()) {
          head_ = other.head_;
          onChanged();
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (!other.getAid().isEmpty()) {
          aid_ = other.aid_;
          onChanged();
        }
        if (other.getVipLevel() != 0) {
          setVipLevel(other.getVipLevel());
        }
        if (!other.getMicFrame().isEmpty()) {
          micFrame_ = other.micFrame_;
          onChanged();
        }
        if (!other.getRippleUrl().isEmpty()) {
          rippleUrl_ = other.rippleUrl_;
          onChanged();
        }
        if (other.getRole() != 0) {
          setRole(other.getRole());
        }
        if (other.getIdentify() != 0) {
          setIdentify(other.getIdentify());
        }
        if (other.getViceHost() != 0) {
          setViceHost(other.getViceHost());
        }
        if (other.getVoiceType() != 0) {
          setVoiceType(other.getVoiceType());
        }
        if (!other.getStreamId().isEmpty()) {
          streamId_ = other.streamId_;
          onChanged();
        }
        if (other.getNewVipLevel() != 0) {
          setNewVipLevel(other.getNewVipLevel());
        }
        if (other.getGameRunning() != 0) {
          setGameRunning(other.getGameRunning());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object head_ = "";
      /**
       * <pre>
       *头像
       * </pre>
       *
       * <code>string head = 1;</code>
       */
      public java.lang.String getHead() {
        java.lang.Object ref = head_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          head_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *头像
       * </pre>
       *
       * <code>string head = 1;</code>
       */
      public com.google.protobuf.ByteString
          getHeadBytes() {
        java.lang.Object ref = head_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          head_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *头像
       * </pre>
       *
       * <code>string head = 1;</code>
       */
      public Builder setHead(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        head_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *头像
       * </pre>
       *
       * <code>string head = 1;</code>
       */
      public Builder clearHead() {
        
        head_ = getDefaultInstance().getHead();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *头像
       * </pre>
       *
       * <code>string head = 1;</code>
       */
      public Builder setHeadBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        head_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 2;</code>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 2;</code>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 2;</code>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 2;</code>
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 2;</code>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object aid_ = "";
      /**
       * <pre>
       *用户id
       * </pre>
       *
       * <code>string aid = 3;</code>
       */
      public java.lang.String getAid() {
        java.lang.Object ref = aid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          aid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *用户id
       * </pre>
       *
       * <code>string aid = 3;</code>
       */
      public com.google.protobuf.ByteString
          getAidBytes() {
        java.lang.Object ref = aid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          aid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *用户id
       * </pre>
       *
       * <code>string aid = 3;</code>
       */
      public Builder setAid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        aid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *用户id
       * </pre>
       *
       * <code>string aid = 3;</code>
       */
      public Builder clearAid() {
        
        aid_ = getDefaultInstance().getAid();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *用户id
       * </pre>
       *
       * <code>string aid = 3;</code>
       */
      public Builder setAidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        aid_ = value;
        onChanged();
        return this;
      }

      private int vipLevel_ ;
      /**
       * <pre>
       *vip等级
       * </pre>
       *
       * <code>int32 vip_level = 4;</code>
       */
      public int getVipLevel() {
        return vipLevel_;
      }
      /**
       * <pre>
       *vip等级
       * </pre>
       *
       * <code>int32 vip_level = 4;</code>
       */
      public Builder setVipLevel(int value) {
        
        vipLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *vip等级
       * </pre>
       *
       * <code>int32 vip_level = 4;</code>
       */
      public Builder clearVipLevel() {
        
        vipLevel_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object micFrame_ = "";
      /**
       * <pre>
       *麦位框链接地址
       * </pre>
       *
       * <code>string mic_frame = 5;</code>
       */
      public java.lang.String getMicFrame() {
        java.lang.Object ref = micFrame_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          micFrame_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *麦位框链接地址
       * </pre>
       *
       * <code>string mic_frame = 5;</code>
       */
      public com.google.protobuf.ByteString
          getMicFrameBytes() {
        java.lang.Object ref = micFrame_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          micFrame_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *麦位框链接地址
       * </pre>
       *
       * <code>string mic_frame = 5;</code>
       */
      public Builder setMicFrame(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        micFrame_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *麦位框链接地址
       * </pre>
       *
       * <code>string mic_frame = 5;</code>
       */
      public Builder clearMicFrame() {
        
        micFrame_ = getDefaultInstance().getMicFrame();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *麦位框链接地址
       * </pre>
       *
       * <code>string mic_frame = 5;</code>
       */
      public Builder setMicFrameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        micFrame_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object rippleUrl_ = "";
      /**
       * <pre>
       *波纹链接地址
       * </pre>
       *
       * <code>string ripple_url = 6;</code>
       */
      public java.lang.String getRippleUrl() {
        java.lang.Object ref = rippleUrl_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          rippleUrl_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *波纹链接地址
       * </pre>
       *
       * <code>string ripple_url = 6;</code>
       */
      public com.google.protobuf.ByteString
          getRippleUrlBytes() {
        java.lang.Object ref = rippleUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          rippleUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *波纹链接地址
       * </pre>
       *
       * <code>string ripple_url = 6;</code>
       */
      public Builder setRippleUrl(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        rippleUrl_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *波纹链接地址
       * </pre>
       *
       * <code>string ripple_url = 6;</code>
       */
      public Builder clearRippleUrl() {
        
        rippleUrl_ = getDefaultInstance().getRippleUrl();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *波纹链接地址
       * </pre>
       *
       * <code>string ripple_url = 6;</code>
       */
      public Builder setRippleUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        rippleUrl_ = value;
        onChanged();
        return this;
      }

      private int role_ ;
      /**
       * <pre>
       *身份标识
       * </pre>
       *
       * <code>int32 role = 7;</code>
       */
      public int getRole() {
        return role_;
      }
      /**
       * <pre>
       *身份标识
       * </pre>
       *
       * <code>int32 role = 7;</code>
       */
      public Builder setRole(int value) {
        
        role_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *身份标识
       * </pre>
       *
       * <code>int32 role = 7;</code>
       */
      public Builder clearRole() {
        
        role_ = 0;
        onChanged();
        return this;
      }

      private int identify_ ;
      /**
       * <pre>
       * 0 无 1 vip 2 svip
       * </pre>
       *
       * <code>int32 identify = 8;</code>
       */
      public int getIdentify() {
        return identify_;
      }
      /**
       * <pre>
       * 0 无 1 vip 2 svip
       * </pre>
       *
       * <code>int32 identify = 8;</code>
       */
      public Builder setIdentify(int value) {
        
        identify_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 0 无 1 vip 2 svip
       * </pre>
       *
       * <code>int32 identify = 8;</code>
       */
      public Builder clearIdentify() {
        
        identify_ = 0;
        onChanged();
        return this;
      }

      private int viceHost_ ;
      /**
       * <pre>
       * 0 否 1 是
       * </pre>
       *
       * <code>int32 viceHost = 9;</code>
       */
      public int getViceHost() {
        return viceHost_;
      }
      /**
       * <pre>
       * 0 否 1 是
       * </pre>
       *
       * <code>int32 viceHost = 9;</code>
       */
      public Builder setViceHost(int value) {
        
        viceHost_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 0 否 1 是
       * </pre>
       *
       * <code>int32 viceHost = 9;</code>
       */
      public Builder clearViceHost() {
        
        viceHost_ = 0;
        onChanged();
        return this;
      }

      private int voiceType_ ;
      /**
       * <pre>
       * 变声类型，0原声、1女声、2男声、3擎天柱、4孩童
       * </pre>
       *
       * <code>int32 voice_type = 10;</code>
       */
      public int getVoiceType() {
        return voiceType_;
      }
      /**
       * <pre>
       * 变声类型，0原声、1女声、2男声、3擎天柱、4孩童
       * </pre>
       *
       * <code>int32 voice_type = 10;</code>
       */
      public Builder setVoiceType(int value) {
        
        voiceType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 变声类型，0原声、1女声、2男声、3擎天柱、4孩童
       * </pre>
       *
       * <code>int32 voice_type = 10;</code>
       */
      public Builder clearVoiceType() {
        
        voiceType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object streamId_ = "";
      /**
       * <pre>
       * 用户流id
       * </pre>
       *
       * <code>string streamId = 11;</code>
       */
      public java.lang.String getStreamId() {
        java.lang.Object ref = streamId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          streamId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 用户流id
       * </pre>
       *
       * <code>string streamId = 11;</code>
       */
      public com.google.protobuf.ByteString
          getStreamIdBytes() {
        java.lang.Object ref = streamId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          streamId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 用户流id
       * </pre>
       *
       * <code>string streamId = 11;</code>
       */
      public Builder setStreamId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        streamId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 用户流id
       * </pre>
       *
       * <code>string streamId = 11;</code>
       */
      public Builder clearStreamId() {
        
        streamId_ = getDefaultInstance().getStreamId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 用户流id
       * </pre>
       *
       * <code>string streamId = 11;</code>
       */
      public Builder setStreamIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        streamId_ = value;
        onChanged();
        return this;
      }

      private int newVipLevel_ ;
      /**
       * <code>int32 newVipLevel = 12;</code>
       */
      public int getNewVipLevel() {
        return newVipLevel_;
      }
      /**
       * <code>int32 newVipLevel = 12;</code>
       */
      public Builder setNewVipLevel(int value) {
        
        newVipLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 newVipLevel = 12;</code>
       */
      public Builder clearNewVipLevel() {
        
        newVipLevel_ = 0;
        onChanged();
        return this;
      }

      private int gameRunning_ ;
      /**
       * <pre>
       * 是否游戏中 0 没有游戏中 1在游戏中
       * </pre>
       *
       * <code>int32 gameRunning = 13;</code>
       */
      public int getGameRunning() {
        return gameRunning_;
      }
      /**
       * <pre>
       * 是否游戏中 0 没有游戏中 1在游戏中
       * </pre>
       *
       * <code>int32 gameRunning = 13;</code>
       */
      public Builder setGameRunning(int value) {
        
        gameRunning_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否游戏中 0 没有游戏中 1在游戏中
       * </pre>
       *
       * <code>int32 gameRunning = 13;</code>
       */
      public Builder clearGameRunning() {
        
        gameRunning_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:RoomMicUser)
    }

    // @@protoc_insertion_point(class_scope:RoomMicUser)
    private static final com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser();
    }

    public static com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RoomMicUser>
        PARSER = new com.google.protobuf.AbstractParser<RoomMicUser>() {
      public RoomMicUser parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new RoomMicUser(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RoomMicUser> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RoomMicUser> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoRoomMicInfo.RoomMicUser getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_RoomMicInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_RoomMicInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_RoomMicUser_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_RoomMicUser_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016mic_info.proto\"V\n\013RoomMicInfo\022\r\n\005index" +
      "\030\001 \001(\005\022\016\n\006status\030\002 \001(\005\022\014\n\004mute\030\003 \001(\005\022\032\n\004" +
      "user\030\004 \001(\0132\014.RoomMicUser\"\362\001\n\013RoomMicUser" +
      "\022\014\n\004head\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\013\n\003aid\030\003 \001(" +
      "\t\022\021\n\tvip_level\030\004 \001(\005\022\021\n\tmic_frame\030\005 \001(\t\022" +
      "\022\n\nripple_url\030\006 \001(\t\022\014\n\004role\030\007 \001(\005\022\020\n\010ide" +
      "ntify\030\010 \001(\005\022\020\n\010viceHost\030\t \001(\005\022\022\n\nvoice_t" +
      "ype\030\n \001(\005\022\020\n\010streamId\030\013 \001(\t\022\023\n\013newVipLev" +
      "el\030\014 \001(\005\022\023\n\013gameRunning\030\r \001(\005B+\n\020com.quh" +
      "ong.protoB\027YoustarProtoRoomMicInfob\006prot",
      "o3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_RoomMicInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_RoomMicInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_RoomMicInfo_descriptor,
        new java.lang.String[] { "Index", "Status", "Mute", "User", });
    internal_static_RoomMicUser_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_RoomMicUser_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_RoomMicUser_descriptor,
        new java.lang.String[] { "Head", "Name", "Aid", "VipLevel", "MicFrame", "RippleUrl", "Role", "Identify", "ViceHost", "VoiceType", "StreamId", "NewVipLevel", "GameRunning", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
