package com.quhong.feign;

import com.quhong.data.vo.VipV2BuyVO;
import com.quhong.dto.VipV2BuyDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;


/**
 * 仅k8s服务可用
 */
@FeignClient(name = "ustar-java-vip", url = "ustar-java-vip:8080", fallback = IVIPService.DefaultFallback.class)
public interface IVIPService {

    /**
     * 修改vip
     */
    @PostMapping("/inner/vip/vipChange")
    ApiResult<VipV2BuyVO> vipChange(VipV2BuyDTO dto);


    /**
     * 激活vip
     */
    @PostMapping("/inner/vip/activateVipCard")
    ApiResult<VipV2BuyVO> activateVipCard(VipV2BuyDTO dto);

    /**
     * 移除vip
     */
    @PostMapping("/inner/vip/vipRemove")
    ApiResult<?> vipRemove(VipV2BuyDTO dto);

    @Component
    class DefaultFallback implements IVIPService {

        @Override
        public ApiResult<VipV2BuyVO> vipChange(VipV2BuyDTO dto) {
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public ApiResult<VipV2BuyVO> activateVipCard(VipV2BuyDTO dto) {
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public ApiResult<?> vipRemove(VipV2BuyDTO dto) {
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }
    }
}
