package com.quhong.msg.obj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.core.msg.IProto;
import com.quhong.proto.YoustarProtoGift;

import java.util.List;

public class GiftSendInfoObject implements IProto<YoustarProtoGift.SendInfo> {
    private int beans;
    private int coins;
    private String name = "";
    private String head = "";
    private String uid = "";
    @JSONField(name = "vip_level")
    private int vipLevel;
    private int ulvl;
    @JSONField(name = "badge_list")
    private List<String> badgeList;
    private int identify;
    private int role;//当前房间的身份（0不显示，1房主2副房主3管理员4会员）
    // web返回对象
    private String rid = "";
    private RidInfoObject ridInfo; // 靓号
    private int isNewUser;

    public void fillFrom(JSONObject src) {
        if (src == null) {
            return;
        }
        this.beans = src.getIntValue("beans");
        this.name = src.getString("name");
        this.head = src.getString("head");
        this.uid = src.getString("uid");
        this.vipLevel = src.getIntValue("vip_level");
        this.ulvl = src.getIntValue("ulvl");
        JSONArray jsonArray = src.getJSONArray("badge_list");
        if (jsonArray != null) {
            this.badgeList = jsonArray.toJavaList(String.class);
        }
        this.identify = src.getIntValue("identify");
        this.role = src.getIntValue("role");
        this.isNewUser = src.getIntValue("isNewUser");
    }

    @Override
    public void doFromBody(YoustarProtoGift.SendInfo proto) {
        this.beans = proto.getBeans();
        this.name = proto.getName();
        this.head = proto.getHead();
        this.uid = proto.getUid();
        this.vipLevel = proto.getVipLevel();
        this.ulvl = proto.getUlvl();
        this.badgeList = proto.getBadgeListList();
        this.identify = proto.getIdentify();
        this.role = proto.getRole();
        this.rid = proto.getRid();
        this.ridInfo = new RidInfoObject();
        this.ridInfo.doFromBody(proto.getRidInfo());
        this.isNewUser = proto.getIsNewUser();
    }

    @Override
    public YoustarProtoGift.SendInfo.Builder doToBody() {
        YoustarProtoGift.SendInfo.Builder builder = YoustarProtoGift.SendInfo.newBuilder();
        builder.setBeans(beans);
        builder.setName(name == null ? "" : name);
        builder.setHead(head == null ? "" : head);
        builder.setUid(uid == null ? "" : uid);
        builder.setVipLevel(vipLevel);
        builder.setUlvl(ulvl);
        if (badgeList != null) {
            builder.addAllBadgeList(badgeList);
        }
        builder.setIdentify(identify);
        builder.setRole(role);
        builder.setRid(rid == null ? "" : rid);
        if (this.ridInfo != null) {
            builder.setRidInfo(this.ridInfo.doToBody());
        }
        builder.setIsNewUser(isNewUser);
        return builder;
    }

    public int getBeans() {
        return beans;
    }

    public void setBeans(int beans) {
        this.beans = beans;
    }

    public int getCoins() {
        return coins;
    }

    public void setCoins(int coins) {
        this.coins = coins;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getUlvl() {
        return ulvl;
    }

    public void setUlvl(int ulvl) {
        this.ulvl = ulvl;
    }

    public List<String> getBadgeList() {
        return badgeList;
    }

    public void setBadgeList(List<String> badgeList) {
        this.badgeList = badgeList;
    }

    public int getIdentify() {
        return identify;
    }

    public void setIdentify(int identify) {
        this.identify = identify;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public RidInfoObject getRidInfo() {
        return ridInfo;
    }

    public void setRidInfo(RidInfoObject ridInfo) {
        this.ridInfo = ridInfo;
    }

    public int getIsNewUser() {
        return isNewUser;
    }

    public void setIsNewUser(int isNewUser) {
        this.isNewUser = isNewUser;
    }
}
