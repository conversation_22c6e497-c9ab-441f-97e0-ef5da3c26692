package com.quhong.msg.obj;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.core.msg.IProto;
import com.quhong.proto.YoustarProtoGift;

public class GiftReceiveInfoObject implements IProto<YoustarProtoGift.ReceiveInfo> {
    private String rid = "";
    private String name = "";
    private String uid = "";
    private String head = "";
    @JSONField(name = "vip_level")
    private int vipLevel;
    private int ulvl; //用户等级
    private int identify;
    private RidInfoObject ridInfo; // 靓号

    public void fillFrom(JSONObject src) {
        this.name = src.getString("name");
        this.head = src.getString("head");
        this.uid = src.getString("uid");
        this.vipLevel = src.getIntValue("vip_level");
        this.ulvl = src.getIntValue("ulvl");
        this.identify = src.getIntValue("identify");
    }

    @Override
    public void doFromBody(YoustarProtoGift.ReceiveInfo proto) {
        this.rid = proto.getRid();
        this.name = proto.getName();
        this.uid = proto.getUid();
        this.head = proto.getHead();
        this.vipLevel = proto.getVipLevel();
        this.ulvl = proto.getUlvl();
        this.identify = proto.getIdentify();
        this.ridInfo = new RidInfoObject();
        this.ridInfo.doFromBody(proto.getRidInfo());
    }

    @Override
    public YoustarProtoGift.ReceiveInfo.Builder doToBody() {
        YoustarProtoGift.ReceiveInfo.Builder builder = YoustarProtoGift.ReceiveInfo.newBuilder();
        builder.setRid(rid == null ? "" : rid);
        builder.setName(name == null ? "" : name);
        builder.setUid(uid == null ? "" : uid);
        builder.setHead(head == null ? "" : head);
        builder.setVipLevel(vipLevel);
        builder.setUlvl(ulvl);
        builder.setIdentify(identify);
        if (this.ridInfo != null) {
            builder.setRidInfo(this.ridInfo.doToBody());
        }
        return builder;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getUlvl() {
        return ulvl;
    }

    public void setUlvl(int ulvl) {
        this.ulvl = ulvl;
    }

    public int getIdentify() {
        return identify;
    }

    public void setIdentify(int identify) {
        this.identify = identify;
    }

    public RidInfoObject getRidInfo() {
        return ridInfo;
    }

    public void setRidInfo(RidInfoObject ridInfo) {
        this.ridInfo = ridInfo;
    }
}
