package com.quhong.msg.obj;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.msg.IProto;
import com.quhong.proto.YoustarProtoPk;

public class PKUserInfoObject implements IProto<YoustarProtoPk.PKUserInfo> {
    private String name;
    private String rid;
    private RidInfoObject ridInfo;
    private String uid;
    private String head;
    private int vip;

    public void fillFrom(JSONObject object) {
        this.name = object.getString("name");
        this.rid = object.getString("rid");
        this.uid = object.getString("uid");
        this.head = object.getString("head");
        this.vip = object.getIntValue("vip");
        JSONObject ridInfo = object.getJSONObject("ridInfo");
        if(ridInfo != null){
            this.ridInfo = new RidInfoObject();
            this.ridInfo.fillFrom(ridInfo);
        }
    }

    @Override
    public void doFromBody(YoustarProtoPk.PKUserInfo proto) {
        this.name = proto.getName();
        this.rid = proto.getRid();
        this.uid = proto.getUid();
        this.head = proto.getHead();
        this.vip = proto.getVip();
        this.ridInfo = new RidInfoObject();
        this.ridInfo.doFromBody(proto.getRidInfo());
    }

    @Override
    public YoustarProtoPk.PKUserInfo.Builder doToBody() {
        YoustarProtoPk.PKUserInfo.Builder builder = YoustarProtoPk.PKUserInfo.newBuilder();
        builder.setName(name == null ? "" : name);
        builder.setRid(rid == null ? "" : rid);
        builder.setUid(uid == null ? "" : uid);
        builder.setHead(head == null ? "" : head);
        builder.setVip(vip);
        if (this.ridInfo != null) {
            builder.setRidInfo(this.ridInfo.doToBody());
        }
        return builder;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public RidInfoObject getRidInfo() {
        return ridInfo;
    }

    public void setRidInfo(RidInfoObject ridInfo) {
        this.ridInfo = ridInfo;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }
}
