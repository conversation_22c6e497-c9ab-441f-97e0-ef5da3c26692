package com.quhong.msg.obj;

import com.quhong.core.msg.IProto;
import com.quhong.proto.YoustarProtoRoomMicInfo;

public class RoomMicUserObject implements IProto<YoustarProtoRoomMicInfo.RoomMicUser> {
    private String head; //头像
    private String name; //名字
    private String aid; //用户id
    private int vip_level; //vip等级
    private String mic_frame; //麦位框链接地址
    private String ripple_url; //波纹链接地址
    private int role;
    private int identify;
    private int viceHost;
    private int voice_type; // 变声类型，0原声、1女声、2男声、3擎天柱、4孩童
    private String streamId; //用户流id
    private int gameRunning;// 是否游戏中 0 没有游戏中 1在游戏中
    // 以下非proto字段
    private int upMicTime;
    private String thirdPartId; // 第三方id，推拉流id，后台删除流用


    @Override
    public void doFromBody(YoustarProtoRoomMicInfo.RoomMicUser proto) {
        this.head = proto.getHead();
        this.name = proto.getName();
        this.aid = proto.getAid();
        this.vip_level = proto.getVipLevel();
        this.mic_frame = proto.getMicFrame();
        this.ripple_url = proto.getRippleUrl();
        this.role = proto.getRole();
        this.identify = proto.getIdentify();
        this.viceHost = proto.getViceHost();
        this.voice_type = proto.getVoiceType();
        this.streamId = proto.getStreamId();
        this.gameRunning = proto.getGameRunning();
    }

    @Override
    public YoustarProtoRoomMicInfo.RoomMicUser.Builder doToBody() {
        YoustarProtoRoomMicInfo.RoomMicUser.Builder builder = YoustarProtoRoomMicInfo.RoomMicUser.newBuilder();
        builder.setHead(this.head == null ? "" : this.head);
        builder.setName(this.name == null ? "" : this.name);
        builder.setAid(this.aid == null ? "" : this.aid);
        builder.setVipLevel(this.vip_level);
        builder.setMicFrame(this.mic_frame == null ? "" : this.mic_frame);
        builder.setRippleUrl(this.ripple_url == null ? "" : this.ripple_url);
        builder.setRole(role);
        builder.setIdentify(identify);
        builder.setViceHost(viceHost);
        builder.setVoiceType(voice_type);
        builder.setStreamId(this.streamId == null ? "" : this.streamId);
        builder.setGameRunning(gameRunning);
        return builder;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getVip_level() {
        return vip_level;
    }

    public void setVip_level(int vip_level) {
        this.vip_level = vip_level;
    }

    public String getMic_frame() {
        return mic_frame;
    }

    public void setMic_frame(String mic_frame) {
        this.mic_frame = mic_frame;
    }

    public String getRipple_url() {
        return ripple_url;
    }

    public void setRipple_url(String ripple_url) {
        this.ripple_url = ripple_url;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public int getIdentify() {
        return identify;
    }

    public void setIdentify(int identify) {
        this.identify = identify;
    }

    public int getViceHost() {
        return viceHost;
    }

    public void setViceHost(int viceHost) {
        this.viceHost = viceHost;
    }

    public int getVoice_type() {
        return voice_type;
    }

    public void setVoice_type(int voice_type) {
        this.voice_type = voice_type;
    }

    public int getUpMicTime() {
        return upMicTime;
    }

    public void setUpMicTime(int upMicTime) {
        this.upMicTime = upMicTime;
    }

    public String getThirdPartId() {
        return thirdPartId;
    }

    public void setThirdPartId(String thirdPartId) {
        this.thirdPartId = thirdPartId;
    }

    public String getStreamId() {
        return streamId;
    }

    public void setStreamId(String streamId) {
        this.streamId = streamId;
    }

    public int getGameRunning() {
        return gameRunning;
    }

    public void setGameRunning(int gameRunning) {
        this.gameRunning = gameRunning;
    }
}
