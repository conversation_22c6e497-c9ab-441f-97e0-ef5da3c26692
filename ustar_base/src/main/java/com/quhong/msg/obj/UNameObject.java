package com.quhong.msg.obj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.msg.IProto;
import com.quhong.proto.YoustarProtoUname;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class UNameObject implements IProto<YoustarProtoUname.Uname> {
    private String name = "";
    private int vip;
    private int level;
    private List<String> badgeList;
    private String head;
    /**
     * 气泡id
     */
    private int bid;
    private int identify;
    private int rid; // 用户rid
    private RidInfoObject ridInfo;
    private String uid;
    private int role;//当前房间的身份（0不显示，1房主2副房主3管理员4会员）
    private int isNewUser ; // 是否为设备首账号 0不是 1是

    public UNameObject() {

    }

    public void fillFrom(JSONObject object) {
        this.name = object.getString("name");
        this.vip = object.getIntValue("vip");
        this.level = object.getIntValue("level");
        JSONArray badgeArr = object.getJSONArray("badges");
        if (badgeArr != null) {
            this.badgeList = new ArrayList<>();
            Iterator<Object> iter = badgeArr.iterator();
            while (iter.hasNext()) {
                this.badgeList.add((String) iter.next());
            }
        }
        this.head = object.getString("head");
        this.bid = object.getIntValue("bid");
        this.identify = object.getIntValue("identify");
        this.rid = object.getIntValue("rid");
        this.uid = object.getString("uid");
        this.role = object.getIntValue("role");
        JSONObject ridInfo = object.getJSONObject("ridInfo");
        if(ridInfo != null){
            this.ridInfo = new RidInfoObject();
            this.ridInfo.fillFrom(ridInfo);
        }
        this.isNewUser = object.getIntValue("isNewUser");
    }


    public void fillFrom2(JSONObject object) {
        this.name = object.getString("name");
        this.vip = object.getIntValue("vip");
        this.level = object.getIntValue("ulvl");
        JSONArray badgeArr = object.getJSONArray("badge");
        if (badgeArr != null) {
            this.badgeList = new ArrayList<>();
            Iterator<Object> iter = badgeArr.iterator();
            while (iter.hasNext()) {
                this.badgeList.add((String) iter.next());
            }
        }
        this.head = object.getString("head");
        this.bid = object.getIntValue("bubble_id");
        this.identify = object.getIntValue("identify");
        this.rid = object.getIntValue("rid");
        this.uid = object.getString("uid");
        this.role = object.getIntValue("role");
        JSONObject ridInfo = object.getJSONObject("ridInfo");
        if(ridInfo != null){
            this.ridInfo = new RidInfoObject();
            this.ridInfo.fillFrom(ridInfo);
        }
        this.isNewUser = object.getIntValue("isNewUser");
    }

    @Override
    public void doFromBody(YoustarProtoUname.Uname proto) {
        this.name = proto.getName();
        this.vip = proto.getVip();
        this.level = proto.getLevel();
        this.badgeList = new ArrayList<>();
        this.bid = proto.getBid();
        this.head = proto.getHead();
        for (String badge : proto.getBadgeList()) {
            this.badgeList.add(badge);
        }
        this.identify = proto.getIdentify();
        this.rid = proto.getRid();
        this.uid = proto.getUid();
        this.role = proto.getRole();
        this.ridInfo = new RidInfoObject();
        this.ridInfo.doFromBody(proto.getRidInfo());
        this.isNewUser = proto.getIsNewUser();
    }

    @Override
    public YoustarProtoUname.Uname.Builder doToBody() {
        YoustarProtoUname.Uname.Builder builder = YoustarProtoUname.Uname.newBuilder();
        builder.setName(name == null ? "" : name);
        builder.setVip(vip);
        builder.setLevel(level);
        if (this.badgeList != null) {
            for (String badge : this.badgeList) {
                builder.addBadge(badge);
            }
        }
        builder.setHead(head == null ? "" : head);
        builder.setBid(bid);
        builder.setIdentify(identify);
        builder.setRid(rid);
        builder.setUid(uid == null ? "" : uid);
        builder.setRole(role);
        if (this.ridInfo != null) {
            builder.setRidInfo(this.ridInfo.doToBody());
        }
        builder.setIsNewUser(isNewUser);
        return builder;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public List<String> getBadgeList() {
        return badgeList;
    }

    public void setBadgeList(List<String> badgeList) {
        this.badgeList = badgeList;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getBid() {
        return bid;
    }

    public void setBid(int bid) {
        this.bid = bid;
    }

    public int getIdentify() {
        return identify;
    }

    public void setIdentify(int identify) {
        this.identify = identify;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public RidInfoObject getRidInfo() {
        return ridInfo;
    }

    public void setRidInfo(RidInfoObject ridInfo) {
        this.ridInfo = ridInfo;
    }

    public int getIsNewUser() {
        return isNewUser;
    }

    public void setIsNewUser(int isNewUser) {
        this.isNewUser = isNewUser;
    }
}
