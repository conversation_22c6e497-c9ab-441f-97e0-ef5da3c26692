package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.RidInfoObject;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.BIG_GIFT_MSG)
public class BigGiftMsg extends MarsServerMsg {

    private String roomRid;  //礼物发送房间rid
    private String fromRid;   //发送者rid
    private String fromName;   //发送者名字
    private String fromHead;  //发送者头像
    private int fromVipLevel; //发送者vip等级
    private String toName; //接收者名字(给1个用户发送大礼物时才会有值) 或者房间名字(发送给多个用户时才有值)
    private String giftIcon; //礼物图标
    private int number; // 发送礼物数量
    private int level;  // 大礼物广播UI样式分为3档：1299～3998钻、3999～7999钻、8000钻以上，分别对应1、2、3
    private int sendType;  // 发送类型  1为全麦位 2单个用户 3全房间 4多个用户
    private String fromRoomId; // 发送者所在房间id
    private int giftId; // 礼物ID
    private String giftName; //礼物名称
    private String giftNameAr; //阿语礼物名称
    private RidInfoObject fromRidInfo; // 新版发送者rid


    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.BigGiftMessage msg = YoustarProtoRoom.BigGiftMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.roomRid = msg.getRoomRid();
        this.fromRid = msg.getFromRid();
        this.fromName = msg.getFromName();
        this.fromHead = msg.getFromHead();
        this.fromVipLevel = msg.getFromVipLevel();
        this.toName = msg.getToName();
        this.giftIcon = msg.getGiftIcon();
        this.number = msg.getNumber();
        this.level = msg.getLevel();
        this.sendType = msg.getSendType();
        this.fromRoomId = msg.getFromRoomId();
        this.giftId = msg.getGiftId();
        this.giftName = msg.getGiftName();
        this.giftNameAr = msg.getGiftNameAr();
        this.fromRidInfo = new RidInfoObject();
        this.fromRidInfo.doFromBody(msg.getFromRidInfo());
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.BigGiftMessage.Builder builder = YoustarProtoRoom.BigGiftMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setRoomRid(this.roomRid == null ? "" : this.roomRid);
        builder.setFromRid(this.fromRid == null ? "" : this.fromRid);
        builder.setFromName(this.fromName == null ? "" : this.fromName);
        builder.setFromHead(this.fromHead == null ? "" : this.fromHead);
        builder.setFromRoomId(this.fromRoomId == null ? "" : this.fromRoomId);
        builder.setFromVipLevel(this.fromVipLevel);
        builder.setToName(this.toName == null ? "" : this.toName);
        builder.setGiftIcon(this.giftIcon == null ? "" : this.giftIcon);
        builder.setNumber(this.number);
        builder.setLevel(this.level);
        builder.setSendType(this.sendType);
        builder.setGiftId(this.giftId);
        builder.setGiftName(this.giftName == null ? "" : this.giftName);
        builder.setGiftNameAr(this.giftNameAr == null ? "" : this.giftNameAr);
        if (this.fromRidInfo != null) {
            builder.setFromRidInfo(this.fromRidInfo.doToBody());
        }
        return builder.build().toByteArray();
    }

    public String getRoomRid() {
        return roomRid;
    }

    public void setRoomRid(String roomRid) {
        this.roomRid = roomRid;
    }

    public String getFromRid() {
        return fromRid;
    }

    public void setFromRid(String fromRid) {
        this.fromRid = fromRid;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public String getFromHead() {
        return fromHead;
    }

    public void setFromHead(String fromHead) {
        this.fromHead = fromHead;
    }

    public int getFromVipLevel() {
        return fromVipLevel;
    }

    public void setFromVipLevel(int fromVipLevel) {
        this.fromVipLevel = fromVipLevel;
    }

    public String getToName() {
        return toName;
    }

    public void setToName(String toName) {
        this.toName = toName;
    }

    public String getGiftIcon() {
        return giftIcon;
    }

    public void setGiftIcon(String giftIcon) {
        this.giftIcon = giftIcon;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getSendType() {
        return sendType;
    }

    public void setSendType(int sendType) {
        this.sendType = sendType;
    }

    public String getFromRoomId() {
        return fromRoomId;
    }

    public void setFromRoomId(String fromRoomId) {
        this.fromRoomId = fromRoomId;
    }

    public int getGiftId() {
        return giftId;
    }

    public void setGiftId(int giftId) {
        this.giftId = giftId;
    }

    public String getGiftName() {
        return giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public String getGiftNameAr() {
        return giftNameAr;
    }

    public void setGiftNameAr(String giftNameAr) {
        this.giftNameAr = giftNameAr;
    }

    public RidInfoObject getFromRidInfo() {
        return fromRidInfo;
    }

    public void setFromRidInfo(RidInfoObject fromRidInfo) {
        this.fromRidInfo = fromRidInfo;
    }
}
