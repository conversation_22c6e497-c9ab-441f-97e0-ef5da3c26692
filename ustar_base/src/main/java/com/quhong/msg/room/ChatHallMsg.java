package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.CHAT_HALL_MSG)
public class ChatHallMsg extends MarsServerMsg {

    private String aid; // 发送者uid
    private String name; // 发送者名字
    private String head; // 发送者头像
    private int gender; // 发送者性别
    private int vipLevel; // 发送者vip等级
    private String fromRoomId; // 发送者所在房间id，可能为空
    private String content; // 消息内容
    private int msgType; // 消息类型，同房间内及私信msgType
    private int bubbleId; // 发送者气泡id
    private String msgInfo; // json格式的特殊消息，例如图片对象等

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.ChatHallMessage msg = YoustarProtoRoom.ChatHallMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.aid = msg.getAid();
        this.name = msg.getName();
        this.head = msg.getAid();
        this.gender = msg.getGender();
        this.vipLevel = msg.getVipLevel();
        this.fromRoomId = msg.getFromRoomId();
        this.content = msg.getContent();
        this.msgType = msg.getMsgType();
        this.bubbleId = msg.getBubbleId();
        this.msgInfo = msg.getMsgInfo();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.ChatHallMessage.Builder builder = YoustarProtoRoom.ChatHallMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setAid(aid == null ? "" : aid);
        builder.setName(name == null ? "" : name);
        builder.setHead(head == null ? "" : head);
        builder.setGender(gender);
        builder.setVipLevel(vipLevel);
        builder.setFromRoomId(fromRoomId == null ? "" : fromRoomId);
        builder.setContent(content == null ? "" : content);
        builder.setMsgType(msgType);
        builder.setBubbleId(bubbleId);
        builder.setMsgInfo(msgInfo == null ? "" : msgInfo);
        return builder.build().toByteArray();
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getFromRoomId() {
        return fromRoomId;
    }

    public void setFromRoomId(String fromRoomId) {
        this.fromRoomId = fromRoomId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public int getBubbleId() {
        return bubbleId;
    }

    public void setBubbleId(int bubbleId) {
        this.bubbleId = bubbleId;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

    public void setMsgInfo(String msgInfo) {
        this.msgInfo = msgInfo;
    }
}
