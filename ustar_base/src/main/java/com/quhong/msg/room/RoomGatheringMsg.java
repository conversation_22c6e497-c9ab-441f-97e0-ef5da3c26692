package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoRoom;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
@Message(cmd = Cmd.ROOM_GATHERING_MSG)
public class RoomGatheringMsg extends MarsServerMsg {

    /**
     * 用户头像
     */
    private String head;

    /**
     * 用户性别
     */
    private int gender;

    /**
     * 用户vip等级
     */
    private int vipLevel;

    /**
     * 召集类型 0召集全球用户 1召集房间会员 2召集房间粉丝 3召集我的粉丝
     */
    private int type;

    /**
     * 广播内容
     */
    private String msg;

    /**
     * 房间id
     */
    private String fromRoomId;

    /**
     * 用户rid
     */
    private int rid;

    /**
     * 召集id
     */
    private int gatherId;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.RoomGatheringMessage msg = YoustarProtoRoom.RoomGatheringMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.head = msg.getHead();
        this.gender = msg.getGender();
        this.vipLevel = msg.getVipLevel();
        this.type = msg.getType();
        this.msg = msg.getMsg();
        this.fromRoomId = msg.getFromRoomId();
        this.rid = msg.getRid();
        this.gatherId = msg.getGatherId();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.RoomGatheringMessage.Builder builder = YoustarProtoRoom.RoomGatheringMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setHead(this.head == null ? "" : this.head);
        builder.setGender(this.gender);
        builder.setVipLevel(this.vipLevel);
        builder.setType(this.type);
        builder.setMsg(this.msg == null ? "" : this.msg);
        builder.setFromRoomId(this.fromRoomId == null ? "" : this.fromRoomId);
        builder.setRid(this.rid);
        builder.setGatherId(this.gatherId);
        return builder.build().toByteArray();
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getFromRoomId() {
        return fromRoomId;
    }

    public void setFromRoomId(String fromRoomId) {
        this.fromRoomId = fromRoomId;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getGatherId() {
        return gatherId;
    }

    public void setGatherId(int gatherId) {
        this.gatherId = gatherId;
    }
}
