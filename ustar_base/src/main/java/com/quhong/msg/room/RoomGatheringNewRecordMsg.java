package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoRoom;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
@Message(cmd = Cmd.ROOM_GATHERING_NEW_RECORD_MSG)
public class RoomGatheringNewRecordMsg extends MarsServerMsg {

    /**
     * 召集类型 0召集全球用户 1召集房间会员 2召集房间粉丝 3召集我的粉丝
     */
    private int type;

    /**
     * 发送者uid
     */
    private String aid;

    /**
     * 发送者昵称
     */
    private String name;

    /**
     * 发送者头像
     */
    private String head;

    /**
     * 发送者vip等级
     */
    private int vipLevel;

    /**
     * 发送者性别
     *
     */
    private int gender;

    /**
     * 发送者年龄
     *
     */
    private int age;

    /**
     * 发送时间
     */
    private String time;

    /**
     * 发送时间阿语
     */
    private String timeAr;

    /**
     * 广播内容
     */
    private String msg;

    /**
     * 房间id
     */
    private String fromRoomId;

    /**
     * 发送者rid
     */
    private int rid;

    /**
     * 召集id
     */
    private int gatherId;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.RoomGatheringNewRecordMessage msg = YoustarProtoRoom.RoomGatheringNewRecordMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.type = msg.getType();
        this.aid = msg.getAid();
        this.name = msg.getName();
        this.head = msg.getHead();
        this.vipLevel = msg.getVipLevel();
        this.gender = msg.getGender();
        this.age = msg.getAge();
        this.time = msg.getTime();
        this.timeAr = msg.getTimeAr();
        this.msg = msg.getMsg();
        this.fromRoomId = msg.getFromRoomId();
        this.rid = msg.getRid();
        this.gatherId = msg.getGatherId();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.RoomGatheringNewRecordMessage.Builder builder = YoustarProtoRoom.RoomGatheringNewRecordMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setType(this.type);
        builder.setAid(this.aid == null ? "" : this.aid);
        builder.setName(this.name == null ? "" : this.name);
        builder.setHead(this.head == null ? "" : this.head);
        builder.setVipLevel(this.vipLevel);
        builder.setGender(this.gender);
        builder.setAge(this.age);
        builder.setTime(this.time == null ? "" : this.time);
        builder.setTimeAr(this.timeAr == null ? "" : this.timeAr);
        builder.setMsg(this.msg == null ? "" : this.msg);
        builder.setFromRoomId(this.fromRoomId == null ? "" : this.fromRoomId);
        builder.setRid(this.getRid());
        builder.setGatherId(this.gatherId);
        return builder.build().toByteArray();
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getFromRoomId() {
        return fromRoomId;
    }

    public void setFromRoomId(String fromRoomId) {
        this.fromRoomId = fromRoomId;
    }

    public String getTimeAr() {
        return timeAr;
    }

    public void setTimeAr(String timeAr) {
        this.timeAr = timeAr;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getGatherId() {
        return gatherId;
    }

    public void setGatherId(int gatherId) {
        this.gatherId = gatherId;
    }
}
