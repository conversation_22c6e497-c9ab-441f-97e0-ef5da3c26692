package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.ROOM_USER_CHANGE_PUSH)
public class RoomUserChangePUshMsg extends MarsServerMsg {
    public static final int ENTER = 1;
    public static final int LEAVE = 2;

    private int type; // 1 进入 2 离开(离开时vip_level和head为默认值)
    private int vip_level; // vip 等级
    private String head = ""; // 头像
    private int onlineNumber; // 房间内在线人数
    private int identify;
    private String name;
    private int visitorActors;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.RoomUserChangeMessage msg = YoustarProtoRoom.RoomUserChangeMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.type = msg.getType();
        this.vip_level = msg.getVipLevel();
        this.head = msg.getHead();
        this.onlineNumber = msg.getOnlineNums();
        this.identify = msg.getIdentify();
        this.name = msg.getName();
        this.visitorActors = msg.getVisitorActors();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.RoomUserChangeMessage.Builder builder = YoustarProtoRoom.RoomUserChangeMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setType(this.type);
        builder.setVipLevel(this.vip_level);
        builder.setHead(this.head == null ? "" : this.head);
        builder.setOnlineNums(this.onlineNumber);
        builder.setIdentify(identify);
        builder.setName(name == null ? "" : this.name);
        builder.setVisitorActors(this.visitorActors);
        return builder.build().toByteArray();
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getVip_level() {
        return vip_level;
    }

    public void setVip_level(int vip_level) {
        this.vip_level = vip_level;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getOnlineNumber() {
        return onlineNumber;
    }

    public void setOnlineNumber(int onlineNumber) {
        this.onlineNumber = onlineNumber;
    }

    public int getIdentify() {
        return identify;
    }

    public void setIdentify(int identify) {
        this.identify = identify;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getVisitorActors() {
        return visitorActors;
    }

    public void setVisitorActors(int visitorActors) {
        this.visitorActors = visitorActors;
    }
}
