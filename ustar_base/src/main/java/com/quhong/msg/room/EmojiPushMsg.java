package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.ROOM_EMOJI_PUSH)
public class EmojiPushMsg extends MarsServerMsg {
    private String icon_file;
    private String nameText;

    @Override
    public void fillFrom(JSONObject object) {
        this.icon_file = object.getString("icon_file");
        this.nameText = object.getString("nameText");
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.EmojiMessage msg = YoustarProtoRoom.EmojiMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.icon_file = msg.getIconFile();
        this.nameText = msg.getNameText();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.EmojiMessage.Builder builder = YoustarProtoRoom.EmojiMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setIconFile(this.icon_file == null ? "" : this.icon_file);
        builder.setNameText(this.nameText == null ? "" : this.nameText);
        return builder.build().toByteArray();
    }

    public String getIcon_file() {
        return icon_file;
    }

    public void setIcon_file(String icon_file) {
        this.icon_file = icon_file;
    }

    public String getNameText() {
        return nameText;
    }

    public void setNameText(String nameText) {
        this.nameText = nameText;
    }
}
