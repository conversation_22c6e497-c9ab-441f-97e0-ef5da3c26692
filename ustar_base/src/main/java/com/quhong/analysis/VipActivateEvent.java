package com.quhong.analysis;

/**
 * vip激活记录表
 */
public class VipActivateEvent extends UserEvent {

    public String vip_level;        // vip等级
    public int activate_type;       // 1 购买 2 vip卡激活
    public int items_service_life;  // 资源使用期限
    public int cost_diamonds;       // 花费的钻石数
    public int is_discount;         // 是否享受购买折扣 1 享受 0 不享受
    public int ctime; // 数据创建时间

    @Override
    public String getEventName() {
        return "vip_activate_record";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }


    public String getVip_level() {
        return vip_level;
    }

    public void setVip_level(String vip_level) {
        this.vip_level = vip_level;
    }

    public int getActivate_type() {
        return activate_type;
    }

    public void setActivate_type(int activate_type) {
        this.activate_type = activate_type;
    }

    public int getItems_service_life() {
        return items_service_life;
    }

    public void setItems_service_life(int items_service_life) {
        this.items_service_life = items_service_life;
    }

    public int getCost_diamonds() {
        return cost_diamonds;
    }

    public void setCost_diamonds(int cost_diamonds) {
        this.cost_diamonds = cost_diamonds;
    }

    public int getIs_discount() {
        return is_discount;
    }

    public void setIs_discount(int is_discount) {
        this.is_discount = is_discount;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
