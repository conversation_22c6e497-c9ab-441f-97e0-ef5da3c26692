package com.quhong.analysis;

/**
 * 每天的vip人员数据在此记录
 */
public class DailyVipUserEvent extends UserEvent {

    public String vip_level;        // vip等级
    public int activate_type;       // 1 购买 2 vip卡激活
    public String date;             // 日期
    public int ctime;               // 数据创建时间

    @Override
    public String getEventName() {
        return "daily_vip_user";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }


    public String getVip_level() {
        return vip_level;
    }

    public void setVip_level(String vip_level) {
        this.vip_level = vip_level;
    }

    public int getActivate_type() {
        return activate_type;
    }

    public void setActivate_type(int activate_type) {
        this.activate_type = activate_type;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
