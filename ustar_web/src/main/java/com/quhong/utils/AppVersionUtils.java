package com.quhong.utils;

import com.quhong.enums.ClientOS;
import com.quhong.handler.HttpEnvData;

import java.util.HashMap;
import java.util.Map;

public class AppVersionUtils {

    public static final Map<Integer, int[]> VERSION_MAP = new HashMap<>();

    static {
        VERSION_MAP.put(825, new int[]{410, 750});
        VERSION_MAP.put(826, new int[]{411, 751});
        VERSION_MAP.put(829, new int[]{415, 753});
        VERSION_MAP.put(830, new int[]{422, 754});
        VERSION_MAP.put(831, new int[]{428, 755});
        VERSION_MAP.put(832, new int[]{434, 757});
        VERSION_MAP.put(833, new int[]{435, 758});
        VERSION_MAP.put(834, new int[]{436, 760});
        VERSION_MAP.put(8341, new int[]{444, 758}); // 图灵顿更新版本版本
        VERSION_MAP.put(835, new int[]{448, 761}); // 朋友圈版本
        VERSION_MAP.put(8351, new int[]{454, 762}); // 强更版 v5版本密钥
        VERSION_MAP.put(836, new int[]{456, 764});
        VERSION_MAP.put(8361, new int[]{461, 765}); // Ludo加钻石提交审核版本
        VERSION_MAP.put(837, new int[]{462, 766}); // 分享房间
        VERSION_MAP.put(838, new int[]{465, 767});
        VERSION_MAP.put(839, new int[]{468, 768}); //新版商城
        VERSION_MAP.put(841, new int[]{475, 770}); //分享房间活动
        VERSION_MAP.put(842, new int[]{478, 771}); // 申请上麦、房间聚集
        VERSION_MAP.put(843, new int[]{480, 772}); // 礼物投票、新版新人礼包
        VERSION_MAP.put(844, new int[]{485, 774}); // live房间、盲盒礼物
        VERSION_MAP.put(845, new int[]{488, 776}); // 礼物冠名，守护关系
        VERSION_MAP.put(8451, new int[]{490, 778}); // 支付优惠奖励分开展示
        VERSION_MAP.put(847, new int[]{495, 780});
        VERSION_MAP.put(8471, new int[]{500, 782}); // 朋友圈打赏改变
        VERSION_MAP.put(849, new int[]{515, 786}); // 房间等级，每日任务
        VERSION_MAP.put(850, new int[]{520, 788}); //
        VERSION_MAP.put(853, new int[]{545, 796}); // domino游戏
        VERSION_MAP.put(854, new int[]{553, 798}); // 金币礼物
        VERSION_MAP.put(8563, new int[]{563, 800}); // 数美设备id
        VERSION_MAP.put(856, new int[]{566, 802}); // 家族体系
        VERSION_MAP.put(857, new int[]{572, 806}); // 发送表情消息
        VERSION_MAP.put(8572, new int[]{575, 808}); // 数美风控
        VERSION_MAP.put(8573, new int[]{576, 809}); // 朋友圈改礼物钻石
        VERSION_MAP.put(8574, new int[]{577, 810}); // Carrom Pool Game
        VERSION_MAP.put(858, new int[]{578, 812}); // 朋友圈话题
        VERSION_MAP.put(859, new int[]{582, 814}); // 859
        VERSION_MAP.put(8591, new int[]{584, 816}); // 8591 首页改版
        VERSION_MAP.put(8592, new int[]{589, 816}); // 创建房间调整
        VERSION_MAP.put(8593, new int[]{595, 819}); // 桌球游戏
        VERSION_MAP.put(860, new int[]{590, 817}); // 家族
        VERSION_MAP.put(861, new int[]{603, 824}); // 麦位主题
        VERSION_MAP.put(8611, new int[]{606, 825}); // 火箭功能斋月灯
        VERSION_MAP.put(862, new int[]{608, 828}); // 真心话大冒险游戏加主题
        VERSION_MAP.put(8621, new int[]{611, 831}); // 国家版新人礼物、每日任务
        VERSION_MAP.put(864, new int[]{616, 834}); // VIP升级
        VERSION_MAP.put(865, new int[]{622, 838}); // 谁是卧底游戏
        VERSION_MAP.put(866, new int[]{625, 840}); // 打招呼功能
    }

    /**
     * 检测用户是否是某个版本，版本号大于700的认为是IOS
     *
     * @param version     迭代版本号
     * @param versionCode 客户端版本号
     */
    public static boolean versionCheck(int version, int versionCode) {
        return versionCheck(version, versionCode, versionCode > 700 ? ClientOS.IOS : ClientOS.ANDROID);
    }

    /**
     * 检测用户是否是某个版本
     *
     * @param version 迭代版本号
     * @param req     HttpEnvData请求
     */
    public static boolean versionCheck(int version, HttpEnvData req) {
        return versionCheck(version, req.getVersioncode(), req.getOs());
    }

    /**
     * 检测用户是否是某个版本
     *
     * @param version     迭代版本号
     * @param versionCode 客户端版本号
     * @param os          客户端
     */
    public static boolean versionCheck(int version, int versionCode, int os) {
        int[] versionArrays = VERSION_MAP.get(version);
        if (null == versionArrays) {
            return false;
        }
        return versionCode >= versionArrays[ClientOS.IOS == os ? ClientOS.IOS : ClientOS.ANDROID];
    }
}
