package com.quhong.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.room.data.RoomActorDetailData;

public class RspRoomMicUserData {
    private String head;
    private String name;
    private String aid;
    @JSONField(name = "ripple_url")
    private String rippleUrl;
    @JSONField(name = "mic_frame")
    private String micFrame;
    @JSONField(name = "vip_level")
    private int vipLevel;
    private int role;
    private int identify;
    private int viceHost;
    private int voice_type; // [非数据库字段]变声类型，0原声、1女声、2男声、3擎天柱、4孩童
    private String streamId;
    private int gameRunning;

    public RspRoomMicUserData() {

    }

    public void copyFrom(RoomActorDetailData detailData) {
        this.head = detailData.getHead();
        this.name = detailData.getName();
        this.aid = detailData.getAid();
        this.rippleUrl = detailData.getRippleUrl();
        this.micFrame = detailData.getMicFrame();
        this.vipLevel = detailData.getVipLevel();
        this.role = detailData.getRole();
        this.identify = detailData.getIdentify();
        this.viceHost = detailData.getViceHost();
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getRippleUrl() {
        return rippleUrl;
    }

    public void setRippleUrl(String rippleUrl) {
        this.rippleUrl = rippleUrl;
    }

    public String getMicFrame() {
        return micFrame;
    }

    public void setMicFrame(String micFrame) {
        this.micFrame = micFrame;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public int getIdentify() {
        return identify;
    }

    public void setIdentify(int identify) {
        this.identify = identify;
    }

    public int getViceHost() {
        return viceHost;
    }

    public void setViceHost(int viceHost) {
        this.viceHost = viceHost;
    }

    public int getVoice_type() {
        return voice_type;
    }

    public void setVoice_type(int voice_type) {
        this.voice_type = voice_type;
    }

    public String getStreamId() {
        return streamId;
    }

    public void setStreamId(String streamId) {
        this.streamId = streamId;
    }

    public int getGameRunning() {
        return gameRunning;
    }

    public void setGameRunning(int gameRunning) {
        this.gameRunning = gameRunning;
    }
}
