package com.quhong.data.vo;

import com.alibaba.fastjson.JSONObject;

public class ChatHallMsgVO {

    private String msgId; // 消息id去重用
    private String aid; // 发送者uid
    private String name; // 发送者名字
    private String head; // 发送者头像
    private int gender; // 发送者性别
    private int vipLevel; // 发送者vip等级
    private String fromRoomId; // 发送者所在房间id，可能为空
    private String content; // 消息内容
    private int msgType; // 消息类型，同房间内及私信msgType
    private int bubbleId; // 聊天气泡id
    private JSONObject msgInfo; // json格式的特殊消息，例如图片对象等(发送图片时后台会附带url缩略图字段)

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getFromRoomId() {
        return fromRoomId;
    }

    public void setFromRoomId(String fromRoomId) {
        this.fromRoomId = fromRoomId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public int getBubbleId() {
        return bubbleId;
    }

    public void setBubbleId(int bubbleId) {
        this.bubbleId = bubbleId;
    }

    public JSONObject getMsgInfo() {
        return msgInfo;
    }

    public void setMsgInfo(JSONObject msgInfo) {
        this.msgInfo = msgInfo;
    }

    @Override
    public String toString() {
        return "ChatHallMsgVO{" +
                "msgId='" + msgId + '\'' +
                ", aid='" + aid + '\'' +
                ", name='" + name + '\'' +
                ", head='" + head + '\'' +
                ", gender=" + gender +
                ", vipLevel=" + vipLevel +
                ", fromRoomId='" + fromRoomId + '\'' +
                ", content='" + content + '\'' +
                ", msgType=" + msgType +
                ", bubbleId=" + bubbleId +
                ", msgInfo=" + msgInfo +
                '}';
    }
}
