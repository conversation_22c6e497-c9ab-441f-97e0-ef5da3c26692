package com.quhong.data.vo;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * VIP V2购买响应VO
 * <AUTHOR>
 */
public class VipV2BuyVO {
    /**
     * 用户当前vip等级
     */
    private int vipLevel;
    /**
     * vip购买时间
     */
    private int buyTime;
    /**
     * vip过期时间
     */
    private int expireTime;
    /**
     * 当前时间戳
     */
    private int now;

    /**
     * 结束时间, 给vip修改国家使用
     */
    @JSONField(serialize = false)
    private int lastVipEndTime;

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getBuyTime() {
        return buyTime;
    }

    public void setBuyTime(int buyTime) {
        this.buyTime = buyTime;
    }

    public int getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(int expireTime) {
        this.expireTime = expireTime;
    }

    public int getNow() {
        return now;
    }

    public void setNow(int now) {
        this.now = now;
    }

    public int getLastVipEndTime() {
        return lastVipEndTime;
    }

    public void setLastVipEndTime(int lastVipEndTime) {
        this.lastVipEndTime = lastVipEndTime;
    }

}
