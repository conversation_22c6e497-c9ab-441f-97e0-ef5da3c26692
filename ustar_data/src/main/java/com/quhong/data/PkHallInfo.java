package com.quhong.data;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2022/12/5
 */
public class PkHallInfo {

    @JSONField(name = "pid")
    private String pkGameId;

    @JSONField(name = "room_id")
    private String roomId;

    @JSO<PERSON>ield(name = "pk_gift_icon")
    private String pkGiftIcon;

    @JSONField(name = "pk_status")
    private int status;

    @JSONField(name = "pk_time")
    private String pkTime;

    @JSONField(name = "pk_creator_info")
    private PkUserInfo pkCreatorInfo;

    @JSONField(name = "pk_challenger_info")
    private PkUserInfo pkChallengerInfo;

    public static class PkUserInfo {

        private String head;

        private String name;

        @JSONField(name = "vip_level")
        private int vipLevel;

        public PkUserInfo() {
        }

        public PkUserInfo(String head, String name, int vipLevel) {
            this.head = head;
            this.name = name;
            this.vipLevel = vipLevel;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getVipLevel() {
            return vipLevel;
        }

        public void setVipLevel(int vipLevel) {
            this.vipLevel = vipLevel;
        }
    }

    public String getPkGameId() {
        return pkGameId;
    }

    public void setPkGameId(String pkGameId) {
        this.pkGameId = pkGameId;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getPkGiftIcon() {
        return pkGiftIcon;
    }

    public void setPkGiftIcon(String pkGiftIcon) {
        this.pkGiftIcon = pkGiftIcon;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getPkTime() {
        return pkTime;
    }

    public void setPkTime(String pkTime) {
        this.pkTime = pkTime;
    }

    public PkUserInfo getPkCreatorInfo() {
        return pkCreatorInfo;
    }

    public void setPkCreatorInfo(PkUserInfo pkCreatorInfo) {
        this.pkCreatorInfo = pkCreatorInfo;
    }

    public PkUserInfo getPkChallengerInfo() {
        return pkChallengerInfo;
    }

    public void setPkChallengerInfo(PkUserInfo pkChallengerInfo) {
        this.pkChallengerInfo = pkChallengerInfo;
    }
}
