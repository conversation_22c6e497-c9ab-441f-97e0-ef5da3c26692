package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.BuddleSourceData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Collections;
import java.util.List;

@Lazy
@Component
public class BuddleSourceDao {
    private static final Logger logger = LoggerFactory.getLogger(BuddleSourceDao.class);

    public static final String TABLE_NAME = "buddle_source";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;


    public BuddleSourceData findData(int bubbleId) {
        try {
            Criteria criteria = Criteria.where("buddle_id").is(bubbleId);
            return mongoTemplate.findOne(new Query(criteria), BuddleSourceData.class);
        } catch (Exception e) {
            logger.error("findData BuddleSourceData from db error. bubbleId={} {}", bubbleId, e.getMessage(), e);
        }
        return null;
    }

    public BuddleSourceData findDataByStatus(int bubbleId, int status) {
        try {
            Criteria criteria = Criteria.where("buddle_id").is(bubbleId).and("status").is(status);
            return mongoTemplate.findOne(new Query(criteria), BuddleSourceData.class);
        } catch (Exception e) {
            logger.error("findDataByStatus BuddleSourceData from db error. bubbleId={} {}", bubbleId, e.getMessage(), e);
        }
        return null;
    }

    public BuddleSourceData findStoreData(int bubbleId) {
        try {
            Criteria criteria = Criteria.where("buddle_id").is(bubbleId);
            return mongoTemplate.findOne(new Query(criteria), BuddleSourceData.class);
        } catch (Exception e) {
            logger.error("findData BuddleSourceData from db error. bubbleId={} {}", bubbleId, e.getMessage(), e);
        }
        return null;
    }

    public List<BuddleSourceData> getBubbleSourcePage(Integer start, Integer pageSize) {
        try{
            Criteria criteria = Criteria.where("status").is(1).and("item_type").is(5);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.ASC, "forder").and(Sort.by(Sort.Direction.DESC, "_id"));
            query.with(sort);
            query.skip(start).limit(pageSize);
            return mongoTemplate.find(query, BuddleSourceData.class);
        }catch (Exception e){
            logger.error("get float screen page list error. {}", e.getMessage() ,e);
            return Collections.emptyList();
        }
    }

    public List<BuddleSourceData> getValidBubbleSource() {
        try{
            Criteria criteria = Criteria.where("status").is(1);
            Query query = new Query(criteria);
            return mongoTemplate.find(query, BuddleSourceData.class);
        }catch (Exception e){
            logger.error("get float screen page list error. {}", e.getMessage() ,e);
            return Collections.emptyList();
        }
    }


    /**
     * 运营系统相关
     */
    private boolean isNumeric(String str) {
        try {
            Integer.parseInt(str);
            return true;
        } catch(Exception e){
            return false;
        }
    }

    public List<BuddleSourceData> selectBubbleSourcePage(int itemType, int status, String search, int start, int size) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            if(status != -1){
                criteria.and("status").is(status);
            }

            if (!StringUtils.isEmpty(search)){
                if(isNumeric(search)){
                    criteria.and("buddle_id").is(Integer.parseInt(search));;
                }else {
                    criteria.and("name").regex(".*?" + search + ".*?");
                }
            }

            if(itemType != -1){
                criteria.and("item_type").is(itemType);
            }

            Sort sort;
            if(itemType == 5){
                sort = Sort.by(Sort.Order.asc("forder"), Sort.Order.desc("_id"));
            }else {
                sort = Sort.by(Sort.Direction.DESC, "_id");
            }

            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(size);
            return mongoTemplate.find(query, BuddleSourceData.class);
        } catch (Exception e) {
            logger.error("BuddleSourceData selectPage error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public long selectCount(int itemType, int status, String search) {
        // 查询条件
        Criteria criteria = new Criteria();
        if(status != -1){
            criteria.and("status").is(status);
        }

        if (!StringUtils.isEmpty(search)){
            if(isNumeric(search)){
                criteria.and("buddle_id").is(Integer.parseInt(search));;
            }else {
                criteria.and("name").regex(".*?" + search + ".*?");
            }
        }

        if(itemType != -1){
            criteria.and("item_type").is(itemType);
        }

        return mongoTemplate.count(new Query(criteria), TABLE_NAME);
    }

    public BuddleSourceData getLastBubbleSourceData(){

        Query query = new Query();
        query.with(Sort.by(Sort.Order.desc("buddle_id")));
        return mongoTemplate.findOne(query, BuddleSourceData.class);
    }

    public BuddleSourceData getBubbleSourceByID(String docId){
        try{
            return mongoTemplate.findById(docId, BuddleSourceData.class);
        }catch (Exception e){
            logger.error("BuddleSourceData query error. msg = {}", e.getMessage(), e);

        }
        return null;
    }


    public void insert(BuddleSourceData data){
        try {
            mongoTemplate.insert(data);
        } catch (Exception e) {
            logger.error("BuddleSourceData insert error. msg = {}",e.getMessage(),e);
        }
    }

    public void updateData(String docId, Update update) {
        try {
            mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(docId)), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("BuddleSourceData  updateData error.  id={} {}", docId, e.getMessage(), e);
        }
    }

}
