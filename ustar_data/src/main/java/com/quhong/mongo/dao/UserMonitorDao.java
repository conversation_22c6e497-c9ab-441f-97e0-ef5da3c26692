package com.quhong.mongo.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.enums.UserMonitorState;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.UserMonitorData;
import com.quhong.userMonitor.UserMonitorRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Component
public class UserMonitorDao {
    private static final Logger logger = LoggerFactory.getLogger(UserMonitorDao.class);

    public static final String TABLE_NAME = "user_monitor";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Autowired
    private UserMonitorRedis userMonitorRedis;
    @Autowired
    private CommonConfig commonConfig;

    /**
     * 冻结禁言是否生效
     *
     * @param uid
     * @return
     */
    public boolean isFreezeInRoom(String uid) {
        boolean ret = isFreeze(uid);
        if (!ret) {
            return false;
        }
        if (commonConfig.getConfigIntValue(CommonConfig.ROOM_SEND_SWITCH) > 0) {
//            logger.info("uid is freeze.config is open .uid={}", uid);
            // 开关开启
            return true;
        }
//        logger.info("uid is freeze.config is close .uid={}", uid);
        return false;
    }

    public boolean isFreeze(String uid) {
        try {
            // 查看是否被警告，封禁，冻结等
            boolean ret = userMonitorRedis.isMember(uid);
            if (!ret) {
//                logger.info("actor not in user monitor zset. uid={}", uid);
                return false;
            }
            logger.info("actor in user monitor zset. uid={}", uid);
            UserMonitorData monitorData = findData(uid, Arrays.asList(UserMonitorState.FREEZE));
            if (monitorData == null || monitorData.getRelease_at() < DateHelper.getNowSeconds()) {
                // 对象不存在或者已经处于释放状态
//                logger.info("actor is not freeze from db. uid={}", uid);
                return false;
            }
//            logger.info("actor is freeze from db. uid={}", uid);
            return true;
        } catch (Exception e) {
            logger.error("is freeze error. uid={} {}", uid, e.getMessage(), e);
        }
        return false;
    }

    public boolean isTakeMic(String uid) {
        Integer value = commonConfig.getConfigIntValue(CommonConfig.MONITOR_TAKE_MIC);
        if (value == null || value != 1) {
            logger.info("uid take mic.config is open .uid={}", uid);
            // 开关开启
            return true;
        }
        // 冻结或封禁的用户，不具有操作麦位的权限
        UserMonitorData monitorData = findData(uid, Arrays.asList(UserMonitorState.FREEZE, UserMonitorState.BAN));
        if (monitorData == null) {
            return true;
        }
        return false;
    }

    public boolean isSendChatMsg(String uid) {
        try {
            if (commonConfig.getConfigIntValue(CommonConfig.MONITOR_SEND_MSG) < 1) {
                return true;
            }
            boolean ret = userMonitorRedis.isMember(uid);
            if (!ret) {
                return true;
            }
            UserMonitorData monitorData = findData(uid, Arrays.asList(UserMonitorState.FREEZE, UserMonitorState.BAN));
            if (monitorData == null || monitorData.getRelease_at() < DateHelper.getNowSeconds()) {
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("is send chat msg error. uid={} {}", uid, e.getMessage(), e);
        }
        return true;
    }

    public boolean joinRoomPrivilege(String uid) {
        try {
            UserMonitorData monitorData = findData(uid, Collections.singletonList(UserMonitorState.BAN));
            return monitorData == null || monitorData.getRelease_at() < DateHelper.getNowSeconds();
        } catch (Exception e) {
            logger.error("get has join room privilege error. uid={} {}", uid, e.getMessage(), e);
        }
        return true;
    }

    public boolean friendApplyPrivilege(String uid) {
        return notFreezeOrBan(uid);
    }

    public boolean notFreezeOrBan(String uid) {
        try {
            UserMonitorData monitorData = findData(uid, Arrays.asList(UserMonitorState.FREEZE, UserMonitorState.BAN));
            return monitorData == null || monitorData.getRelease_at() < DateHelper.getNowSeconds();
        } catch (Exception e) {
            logger.error("get freeze or ban privilege error. uid={} {}", uid, e.getMessage(), e);
        }
        return true;
    }

    private UserMonitorData findData(String uid, List<Integer> codeList) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("code").in(codeList);
            return mongoTemplate.findOne(new Query(criteria), UserMonitorData.class);
        } catch (Exception e) {
            logger.error("query user monitor data error. uid={} code={} {}", uid, codeList, e.getMessage(), e);
        }
        return null;
    }

    public UserMonitorData findBanData(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("code").is(UserMonitorState.BAN);
            return mongoTemplate.findOne(new Query(criteria), UserMonitorData.class);
        } catch (Exception e) {
            logger.error("query user monitor data error. uid={} code={} {}", uid, UserMonitorState.BAN, e.getMessage(), e);
        }
        return null;
    }

    public UserMonitorData findDataByCode(String uid, int code) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("code").is(code);
            return mongoTemplate.findOne(new Query(criteria), UserMonitorData.class);
        } catch (Exception e) {
            logger.error("query user monitor data error. uid={} code={} {}", uid, UserMonitorState.BAN, e.getMessage(), e);
        }
        return null;
    }

    public UserMonitorData save(UserMonitorData data) {
        try {
            return mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save UserMonitorData data error. uid={} {}", data.getUid(), e.getMessage(), e);
            return null;
        }
    }

    public void removeData(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            mongoTemplate.remove(new Query(criteria), UserMonitorData.class);
        } catch (Exception e) {
            logger.error("query user monitor data error. uid={} code={} {}", uid, UserMonitorState.BAN, e.getMessage(), e);
        }
    }

    public UserMonitorData findData(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            return mongoTemplate.findOne(new Query(criteria), UserMonitorData.class);
        } catch (Exception e) {
            logger.error("findData monitor data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public  List<UserMonitorData> findDataByReleaseAt(int startTime, int endTime) {
        Criteria criteria = Criteria.where("release_at").gte(startTime).lt(endTime)
                .and("code").is(UserMonitorState.BAN); // 3表示封禁状态
        Query query = new Query(criteria);
        List<UserMonitorData> unblockUsers = mongoTemplate.find(query, UserMonitorData.class, TABLE_NAME);

        return unblockUsers;
    }

}
