package com.quhong.mongo.dao;

import com.quhong.cache.CacheMap;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.BadgeData;
import com.quhong.mongo.data.BadgeListData;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 徽章配置信息
 * 已存在缓存五分钟
 */
@Component
public class BadgeListDao {
    private static final Logger logger = LoggerFactory.getLogger(BadgeListDao.class);
    /**
     * 缓存五分钟
     */
    private static final long CACHE_TIME = 5 * 60 * 1000L;

    public static final String TABLE_NAME = "badge_list";

    private CacheMap<Integer, BadgeListData> cacheMap;
    private CacheMap<String, Map<Integer, List<BadgeListData>>> cacheListMap;
    private CacheMap<String, Map<Integer, String>> badgeSourceMap;

    public BadgeListDao() {
        cacheMap = new CacheMap<>(CACHE_TIME);
        cacheListMap = new CacheMap<>(CACHE_TIME);
        badgeSourceMap = new CacheMap<>(CACHE_TIME);
    }

    @Resource(name = DataRedisBean.MIC_SOURCE)
    private StringRedisTemplate redisTemplate;

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    @Deprecated
    public List<String> getSmallIcons(List<BadgeData> srcList) {
        try {
            List<Object> ids = srcList.stream().map(badgeData -> String.valueOf(badgeData.getBadge_id())).collect(Collectors.toList());
            List<Object> objectList = redisTemplate.opsForHash().multiGet(getRedisKey(), ids);
            return objectList.stream().map(Object::toString).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("get badge source list error. {}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<String> getSmallIconsFromCache(List<BadgeData> srcList) {
        try {
            List<Integer> ids = srcList.stream().map(BadgeData::getBadge_id).collect(Collectors.toList());
            return getSmallIconsFromCacheByIds(ids);
        } catch (Exception e) {
            logger.error("get badge source list error. {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }


    public List<String> getSmallIconsFromCacheByIds(List<Integer> ids) {
        try {
            List<String> result = new ArrayList<>();
            Map<Integer, String> map = getAllBadgeSourceFromCache();
            for (Integer id : ids) {
                String smallIcons = map.get(id);
                if (null != smallIcons) {
                    result.add(smallIcons);
                }
            }
            return result;
        } catch (Exception e) {
            logger.error("get badge source list error. {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public Map<Integer, String> getAllBadgeSourceFromCache() {
        if (badgeSourceMap.hasData(TABLE_NAME)) {
            return badgeSourceMap.getData(TABLE_NAME);
        }
        return getAllBadgeSource();
    }

    public synchronized Map<Integer, String> getAllBadgeSource() {
        try {
            if (badgeSourceMap.hasData(TABLE_NAME)) {
                return badgeSourceMap.getData(TABLE_NAME);
            }
            Map<Integer, String> resultMap = new HashMap<>();
            Map<Object, Object> map = redisTemplate.opsForHash().entries(getRedisKey());
            for (Map.Entry<Object, Object> entry : map.entrySet()) {
                try {
                    resultMap.put(Integer.parseInt(String.valueOf(entry.getKey())), String.valueOf(entry.getValue()));
                } catch (Exception e) {
                    logger.error("parse badge source error. key={} value={}", entry.getKey(), entry.getValue(), e);
                }
            }
            if (!resultMap.isEmpty()) {
                badgeSourceMap.cacheData(TABLE_NAME, resultMap);
            }
            return resultMap;
        } catch (Exception e) {
            logger.error("get all badge source error. {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    public BadgeListData getBadgeListData(int badgeId) {
        BadgeListData badgeListData = cacheMap.getData(badgeId);
        if (badgeListData != null) {
            return badgeListData;
        }
        badgeListData = findData(badgeId);
        if (badgeListData == null) {
            logger.error("can not find badge list data. badgeId={}", badgeId);
            return null;
        }
        cacheMap.cacheData(badgeId, badgeListData);
        return badgeListData;
    }

    public Map<Integer, List<BadgeListData>> getAllBadgeListData() {
        Map<Integer, List<BadgeListData>> badgeMap = cacheListMap.getData(getAllBadgeKey());
        if (!CollectionUtils.isEmpty(badgeMap)) {
            return badgeMap;
        }
        List<BadgeListData> list = findAllData();
        if (list == null) {
            logger.error("can not find badge list data.");
            return null;
        }
        badgeMap = list.stream().filter(k -> k.getValid() == 1).collect(Collectors.groupingBy(BadgeListData::getBadge_type));
        cacheListMap.cacheData(getAllBadgeKey(), badgeMap);
        return badgeMap;
    }

    public List<BadgeListData> findAllData() {
        try {
            return mongoTemplate.findAll(BadgeListData.class);
        } catch (Exception e) {
            logger.error("get badge id data from db error. {}", e.getMessage(), e);
        }
        return null;
    }

    public BadgeListData findData(int badgeId) {
        try {
            Criteria criteria = Criteria.where("badge_id").is(badgeId);
            return mongoTemplate.findOne(new Query(criteria), BadgeListData.class);
        } catch (Exception e) {
            logger.error("get badge id data from db error. badgeId={} {}", badgeId, e.getMessage(), e);
        }
        return null;
    }

    private String getRedisKey() {
        return "badge_source";
    }

    private String getAllBadgeKey() {
        return "badgeList:";
    }


    private boolean isNumeric(String str) {
        try {
            Integer.parseInt(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public List<BadgeListData> selectBadgeListDataPage(int status, String search, int start, int size) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            if (status != -1) {
                criteria.and("valid").is(status);
            }

            if (!StringUtils.isEmpty(search)) {
                if (isNumeric(search)) {
                    criteria.and("badge_id").is(Integer.parseInt(search));
                } else {
                    criteria.and("name").regex(".*?" + search + ".*?");
                }
            }
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");

            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(size);
            return mongoTemplate.find(query, BadgeListData.class);
        } catch (Exception e) {
            logger.error("selectBadgeListDataPage selectPage error.{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public long selectCount(int status, String search) {
        // 查询条件
        Criteria criteria = new Criteria();
        if (status != -1) {
            criteria.and("valid").is(status);
        }

        if (!StringUtils.isEmpty(search)) {
            if (isNumeric(search)) {
                criteria.and("screen_id").is(Integer.parseInt(search));
            } else {
                criteria.and("name").regex(".*?" + search + ".*?");
            }
        }
        return mongoTemplate.count(new Query(criteria), TABLE_NAME);
    }


    /**
     * 获取用户拥有的勋章
     */
    public List<BadgeListData> findBadgesListByIds( List<Integer> badgeIdList) {
        try {
            if (CollectionUtils.isEmpty(badgeIdList)) {
                return Collections.emptyList();
            }
            Query query = new Query(Criteria.where("badge_id").in(badgeIdList));
            List<BadgeListData> list = mongoTemplate.find(query, BadgeListData.class);
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyList();
            }
            return list;
        } catch (Exception e) {
            logger.error("findBadgesListByIds error badgeIdList={} {}", badgeIdList, e.getMessage());
        }
        return Collections.emptyList();
    }
}
