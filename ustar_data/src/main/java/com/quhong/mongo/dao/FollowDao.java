package com.quhong.mongo.dao;

import com.mongodb.client.result.DeleteResult;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.BlackListData;
import com.quhong.mongo.data.FollowData;
import com.quhong.mongo.data.FriendsData;
import com.quhong.redis.DataRedisBean;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Lazy
public class FollowDao {
    private static final Logger logger = LoggerFactory.getLogger(FollowDao.class);

    public static final String TABLE_NAME = "follow";

    private static final String CACHE_FLAG = "cacheFlag";
    public static final Set<String> WHITE_SET = new HashSet<>(Arrays.asList("5cc2797c66dc630025bf17c2"));
    public static final int MIN_FOLLOW_COUNT = 1000;

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    /**
     * 获取用户uid的粉丝数
     */
    public int getFollowsCount(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid);
            Aggregation agg = Aggregation.newAggregation(Aggregation.match(criteria),
                    Aggregation.group("aid").count().as("count"));
            List<CountData> result = mongoTemplate.aggregate(agg, TABLE_NAME, CountData.class).getMappedResults();
            int count = 0;
            for (CountData countData : result) {
                count += countData.getCount();
            }
            return count;
        } catch (Exception e) {
            logger.error("get follows count error {}", e.getMessage(), e);
            return 0;
        }
    }


    /**
     * 获取用户uid的粉丝数
     */
    public int getFollowsCountByMonGo(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid);
            Aggregation agg = Aggregation.newAggregation(Aggregation.match(criteria),
                    Aggregation.group("aid").count().as("count"));
            List<CountData> result = mongoTemplate.aggregate(agg, TABLE_NAME, CountData.class).getMappedResults();
            int count = 0;
            for (CountData countData : result) {
                count += countData.getCount();
            }
            return count;
        } catch (Exception e) {
            logger.error("get follows count error {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取用户uid的关注数
     */
    public int getFollowingCount(String uid) {
        String key = getFollowListKey(uid);
        try {
            if (Boolean.FALSE.equals(clusterRedis.hasKey(key))) {
                return reBuildCache(uid).size();
            }
            Long size = clusterRedis.opsForSet().size(key);
            return size != null ? size.intValue() - 1 : 0;
        } catch (Exception e) {
            logger.error("get following count error {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取用户uid的新粉丝数
     */
    public int getNewFollowsCount(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("is_new").is(1);
            Aggregation agg = Aggregation.newAggregation(Aggregation.match(criteria),
                    Aggregation.group("aid").count().as("count"));
            List<CountData> result = mongoTemplate.aggregate(agg, TABLE_NAME, CountData.class).getMappedResults();
            int count = 0;
            for (CountData countData : result) {
                count += countData.getCount();
            }
            return count;
        } catch (Exception e) {
            logger.error("get new follows count error {}", e.getMessage(), e);
            return 0;
        }
    }

    public void cleanFollowedUnread(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("is_new").is(1);
            Update update = new Update();
            update.set("is_new", 0);
            mongoTemplate.updateMulti(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("clean unread. uid={} {}", e.getMessage(), e);
        }
    }

    public Set<String> getFollowedUidSet(String uid) {
        try {
            List<FollowData> followList = mongoTemplate.find(new Query(Criteria.where("aid").is(uid)), FollowData.class);
            return followList.stream().map(FollowData::getUid).collect(Collectors.toSet());
        } catch (Exception e) {
            logger.error("get followed uid set error {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 校验uid是否已关注aid
     */
    public boolean isFollowed(String uid, String aid) {
        String key = getFollowListKey(uid);
        try {
            if (Boolean.FALSE.equals(clusterRedis.hasKey(key))) {
                return reBuildCache(uid).contains(aid);
            }
            return Boolean.TRUE.equals(clusterRedis.opsForSet().isMember(key, aid));
        } catch (Exception e) {
            logger.error("get is followed error uid={} aid={}", uid, aid, e);
            return false;
        }
    }

    /**
     * 取消关注
     *
     * @param uid
     * @param aid
     */
    public void removeFollowed(String uid, String aid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("aid").is(aid);
            mongoTemplate.remove(new Query(criteria), FollowData.class);
            clusterRedis.opsForSet().remove(getFollowListKey(uid), aid);
        } catch (Exception e) {
            logger.error("delete followed data error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
        }
    }

    /**
     * 增加关注
     *
     * @param uid
     * @param aid
     */
    public void addFollowed(String uid, String aid) {
        try {
            Query query = new Query(Criteria.where("uid").is(uid).and("aid").is(aid));
            Update update = new Update();
            update.set("is_new", 1);
            update.set("ctime", DateHelper.getNowSeconds());
            mongoTemplate.upsert(query, update, TABLE_NAME);
            String key = getFollowListKey(uid);
            if (Boolean.FALSE.equals(clusterRedis.hasKey(key))) {
                reBuildCache(uid);
            } else {
                clusterRedis.opsForSet().add(key, aid);
            }
        } catch (Exception e) {
            logger.error("add followed data error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
        }
    }

    public List<FollowData> getFollowingList(String uid, Integer start, Integer pageSize) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            Sort sort = Sort.by(Sort.Direction.DESC, "ctime");
            Query query = new Query(criteria).with(sort).skip(start).limit(pageSize);
            return mongoTemplate.find(query, FollowData.class);
        } catch (Exception e) {
            logger.error("find following list error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public FollowData getLastFollowUser(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            Query query = new Query(criteria).limit(1);
            return mongoTemplate.findOne(query, FollowData.class);
        } catch (Exception e) {
            logger.error("getLastFollowUser error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public List<FollowData> getFollowingList(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            return mongoTemplate.find(new Query(criteria), FollowData.class);
        } catch (Exception e) {
            logger.error("find following list error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public List<FollowData> getFollowsList(String uid, Integer start, Integer pageSize) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid);
            Sort sort = Sort.by(Sort.Direction.DESC, "ctime");
            Query query = new Query(criteria).with(sort).skip(start).limit(pageSize);
            return mongoTemplate.find(query, FollowData.class);
        } catch (Exception e) {
            logger.error("find following list error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public FollowData getFollowsData(String uid, String aid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("uid").is(aid);
            return mongoTemplate.findOne(new Query(criteria), FollowData.class);
        } catch (Exception e) {
            logger.error("find follows data error. uid={} aid={} {}", uid, aid, e.getMessage(), e);
        }
        return null;
    }

    /**
     * @param uid
     * @param type 1 我关注的 2 关注我的
     * @return
     */
    public long deleteByUid(String uid, int type, int start, int size) {
        try {
            Criteria criteria = type == 1 ? Criteria.where("uid").is(uid) : Criteria.where("aid").is(uid);
            Sort sort = Sort.by(Sort.Direction.DESC, "ctime");
            Query query = new Query(criteria).with(sort).skip(start).limit(size);
            List<FollowData> all = mongoTemplate.find(query, FollowData.class);
            for (FollowData item : all) {
                if (type == 1) {
                    clusterRedis.opsForSet().remove(getFollowListKey(uid), item.getAid());
                } else {
                    clusterRedis.opsForSet().remove(getFollowListKey(item.getUid()), uid);
                }
            }
            Query dQuery = new Query(criteria);
            dQuery.skip(start);
            dQuery.limit(size);
            DeleteResult deleteResult = mongoTemplate.remove(dQuery, TABLE_NAME);
            return deleteResult.getDeletedCount();
        } catch (Exception e) {
            logger.error("deleteByUid error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }


    public int getMaxFollowNum(String uid) {
        try {
            String numTest;
            if (WHITE_SET.contains(uid)) {
                numTest = (String) clusterRedis.opsForHash().get("hash:common_config_key", "follow_user_num_test");
            } else {
                numTest = (String) clusterRedis.opsForHash().get("hash:common_config_key", "follow_user_num");
            }
            return null == numTest ? MIN_FOLLOW_COUNT : Integer.parseInt(numTest);
        } catch (Exception e) {
            logger.error("getMaxFollowNum data error. uid={}  msg={}", uid, e.getMessage(), e);
        }
        return MIN_FOLLOW_COUNT;
    }

    public Set<String> getFollowingSet(String uid) {
        String key = getFollowListKey(uid);
        try {
            if (Boolean.FALSE.equals(clusterRedis.hasKey(key))) {
                return reBuildCache(uid);
            }
            Set<String> members = clusterRedis.opsForSet().members(key);
            if (!CollectionUtils.isEmpty(members)) {
                members.removeIf(CACHE_FLAG::equals);
                return members;
            }
        } catch (Exception e) {
            logger.error("get following set from redis error. uid={} {}", uid, e.getMessage(), e);

        }
        return Collections.emptySet();
    }

    private Set<String> reBuildCache(String uid) {
        long millis = System.currentTimeMillis();
        List<FollowData> allData = getFollowingList(uid);
        Set<String> followSet;
        if (!CollectionUtils.isEmpty(allData)) {
            followSet = allData.stream().map(FollowData::getAid).collect(Collectors.toSet());
        } else {
            followSet = new HashSet<>();
        }
        // 加入标记位
        followSet.add(CACHE_FLAG);
        String[] toArray = followSet.toArray(new String[0]);
        String key = getFollowListKey(uid);
        clusterRedis.opsForSet().add(key, toArray);
        clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        logger.info("reBuildCache uid={} size={} cost={}", uid, followSet.size(), System.currentTimeMillis() - millis);
        return followSet;
    }

    private String getFollowListKey(String uid) {
        return "set:followList_" + uid;
    }

    public static class CountData {
        private int count;
        private String key;

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }
}
