package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.ActorConfigData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户个人配置
 *
 * <AUTHOR>
 * @date 2022/7/18
 */
@Component
public class ActorConfigDao {

    public static final Logger logger = LoggerFactory.getLogger(ActorConfigDao.class);
    public static final Map<String, Object> DEF_ROOM_CONFIG = new HashMap<String, Object>() {
        private static final long serialVersionUID = -2571748319767782994L;
        {
        put(HIDE_RIDE, 0);
        put(HIDE_GIFT, 0);
        put(HIDE_GIFT_MSG, 0);
        put(HIDE_ALL, 0);
        put(HIDE_GIFT_RIDE_VOICE, 0);
        put(HIDE_MIC, 0);
        put(HIDE_RIPPLE, 0);
        put(HIDE_GIF_AUTO, 0);
        put(MESSAGE_TOP_SWITCH, 1);
        put(HIDE_ENTER_ROOM_MSG, 0);
    }};

    public static final Map<String, Object> DEF_GAME_ROOM_CONFIG = new HashMap<String, Object>() {
        {
            put(HIDE_RIDE, 1);
            put(HIDE_GIFT, 1);
            put(HIDE_GIFT_MSG, 0);
            put(HIDE_ALL, 0);
            put(HIDE_GIFT_RIDE_VOICE, 1);
            put(HIDE_MIC, 0);
            put(HIDE_RIPPLE, 1);
            put(HIDE_GIF_AUTO, 0);
            put(MESSAGE_TOP_SWITCH, 1);
            put(HIDE_ENTER_ROOM_MSG, 0);
            put(HIDE_BIG_GIFT, 1);

        }
    };

    public static final String TABLE_NAME = "actor_config";
    public static final String HIDE_RIDE = "hideRide";
    public static final String HIDE_GIFT = "hideGift";
    public static final String HIDE_GIFT_MSG = "hideGiftMsg";

    public static final String HIDE_ALL = "hideAll";
    public static final String HIDE_GIFT_RIDE_VOICE = "hideGiftRideVoice";
    public static final String HIDE_MIC = "hideMic";
    public static final String HIDE_RIPPLE = "hideRipple";
    public static final String HIDE_GIF_AUTO = "hideGifAuto";
    public static final String HIDE_BIG_GIFT = "hideBigGift"; // 隐藏大礼物广播
    public static final String MESSAGE_TOP_SWITCH = "messageTopSwitch";
    public static final String FIRST_RECHARGE_SHOW_TIME = "firstRechargeShowTime";
    public static final String HIDE_ENTER_ROOM_MSG = "hideEnterRoomMsg";
    public static final String ALL_BC_GAME_SWITCH = "allBCGameSwitch";

    // 成就勋章相关
    public static final String ROCKET_COUNT = "rocketCount";  // 发送火箭次数
    public static final String GIFT_COUNT = "giftCount";  // 发送礼物钻石数
    public static final String FINGER_GUESS_COUNT = "fingerGuessCount";  // 猜拳获得钻石数
    public static final String LUDO_COUNT = "ludoCount";  // ludo获得钻石数
    public static final String UMO_COUNT = "umoCount";    // umo获得钻石数
    public static final String MOMENT_COUNT = "momentCount";    // moment获得钻石数

    public static final String SHOW_VIDEO_IN_DETAIL = "ShowVideoInDetail";//个人主页是否展示视频开关
    public static final String SHOW_GLOBAL_OUT_ROOM = "ShowGlobalOutRoom";//房间外礼物广播横幅
    public static final String SHOW_GLOBAL_IN_ROOM = "ShowGlobalInRoom";//房间内礼物广播横幅

    public static final String TRUTH_DARE_TOPIC = "truthDareTopic";  //真心话大冒险自定义话题
    public static final String VIP_CARD_COUNT = "vipCardCount";  // vipCard数
    public static final String MODIFY_COUNTRY = "modifyCountry";  // 修改国家次数
    public static final String VIP_UPGRADE = "vipUpgrade";  // 864vip升级处理状态
    public static final String REJECT_GREET = "rejectGreet";  // 是否拒绝打招呼


    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public void save(ActorConfigData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save actor config data error. uid={} {}", data.getUid(), e.getMessage(), e);
        }
    }

    public ActorConfigData findData(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid);
            return mongoTemplate.findOne(new Query(criteria), ActorConfigData.class);
        } catch (Exception e) {
            logger.error("find actor config data error. uid={} {}", uid, e.getMessage(), e);
            return null;
        }
    }

    public void updateRoomConfig(String uid, String configName, Object configValue) {
        ActorConfigData configData = findData(uid);
        Map<String, Object> configMap;
        if (configData == null) {
            configData = new ActorConfigData();
            configData.setUid(uid);
        }
        configMap = configData.getRoom_config();
        if (CollectionUtils.isEmpty(configMap)) {
            configMap = new HashMap<>(8);
        }
        configMap.put(configName, configValue);
        configData.setRoom_config(configMap);
        save(configData);
    }

    public int getIntRoomConfig(String uid, String configName, int defaultValue) {
        ActorConfigData data = findData(uid);
        if (data == null) {
            return defaultValue;
        }
        Map<String, Object> roomConfigMap = data.getRoom_config();
        try {
            if (!CollectionUtils.isEmpty(roomConfigMap) && roomConfigMap.get(configName) != null) {
                return (int) roomConfigMap.get(configName);
            }
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
        }
        return defaultValue;
    }


    public void updateUserConfig(String uid, String configName, Object configValue) {
        ActorConfigData configData = findData(uid);
        Map<String, Object> configMap;
        if (configData == null) {
            configData = new ActorConfigData();
            configData.setUid(uid);
        }
        configMap = configData.getUser_config();
        if (CollectionUtils.isEmpty(configMap)) {
            configMap = new HashMap<>(8);
        }
        configMap.put(configName, configValue);
        configData.setUser_config(configMap);
        save(configData);
    }

    public long getLongUserConfig(String uid, String configName, long defaultValue) {
        ActorConfigData data = findData(uid);
        if (data == null) {
            return defaultValue;
        }
        Map<String, Object> userConfigMap = data.getUser_config();
        try {
            if (!CollectionUtils.isEmpty(userConfigMap) && userConfigMap.get(configName) != null) {
                Number number = (Number) userConfigMap.getOrDefault(configName, defaultValue);
                return number.longValue();
            }
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
        }
        return defaultValue;
    }

    public List<String> getListUserConfig(String uid, String configName, List<String> defaultListValue) {
        ActorConfigData data = findData(uid);
        if (data == null) {
            return defaultListValue;
        }
        Map<String, List<String>> userListConfigMap = data.getUser_list_config();
        try {
            if (!CollectionUtils.isEmpty(userListConfigMap) && userListConfigMap.get(configName) != null) {
                return userListConfigMap.getOrDefault(configName, defaultListValue);
            }
        } catch (Exception e) {
            logger.error("getListUserConfig: {}", e.getMessage(), e);
        }
        return defaultListValue;
    }

    public void updateUserListConfig(String uid, String configName, List<String> listValue) {
        ActorConfigData configData = findData(uid);
        Map<String,  List<String>> configMap;
        if (configData == null) {
            configData = new ActorConfigData();
            configData.setUid(uid);
        }
        configMap = configData.getUser_list_config();
        if (CollectionUtils.isEmpty(configMap)) {
            configMap = new HashMap<>(8);
        }
        configMap.put(configName, listValue);
        configData.setUser_list_config(configMap);
        save(configData);
    }


    public ActorConfigData initActorConfigData(String uid) {
        ActorConfigData configData = new ActorConfigData();
        configData.setUid(uid);
        Map<String, Object> distConfig = new HashMap<>(DEF_ROOM_CONFIG);
        configData.setRoom_config(distConfig);
        configData.setGame_room_config(new HashMap<>(DEF_GAME_ROOM_CONFIG));
        return configData;
    }

}
