package com.quhong.mongo.data;

import com.quhong.mongo.dao.EmojiResourceDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 表情资源
 *
 * <AUTHOR>
 * @date 2022/8/11
 */
@Document(collection = EmojiResourceDao.TABLE_NAME)
public class EmojiResourceData {

    @Id
    private ObjectId _id;

    private String emojiConfigId;     // 所属表情配置id

    /**
     * 预览图
     */
    private String icon;

    /**
     * 表情文件
     */
    private String icon_file;

    /**
     * 次序
     */
    private int order;

    /**
     * 是否有效
     */
    private int status;

    /**
     * 自定义名称
     */
    private String name;
    /**
     * 自定义阿语名称
     */
    private String nameAr;

    /**
     * 表情子类型 0 图片表情 1 文字表情 2动效表情
     */
    private int emojiSubType;
    /**
     * 表情点击效果
     * 0: 默认发送表情
     * 1: 启动公屏骰子游戏
     */
    private int actionType;


    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getEmojiConfigId() {
        return emojiConfigId;
    }

    public void setEmojiConfigId(String emojiConfigId) {
        this.emojiConfigId = emojiConfigId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIcon_file() {
        return icon_file;
    }

    public void setIcon_file(String icon_file) {
        this.icon_file = icon_file;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public int getEmojiSubType() {
        return emojiSubType;
    }

    public void setEmojiSubType(int emojiSubType) {
        this.emojiSubType = emojiSubType;
    }

    public int getActionType() {
        return actionType;
    }

    public void setActionType(int actionType) {
        this.actionType = actionType;
    }
}
