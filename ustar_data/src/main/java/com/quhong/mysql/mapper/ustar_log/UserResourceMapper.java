package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.UserResourceData;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/23
 */
public interface UserResourceMapper {

    @Insert("insert into ${tableName} (`uid`,`resource_id`,`resource_type`,`resource_number`,`status`,`end_time`, `resource_origin`, `ctime`) values (#{item.uid}, #{item.resourceId}, #{item.resourceType},#{item.resourceNumber},#{item.status}, #{item.endTime}, #{item.resourceOrigin},#{item.ctime})")
    void insert(@Param("tableName") String tableName, @Param("item") UserResourceData data);

    @Delete("<script>" +
            "insert into ${tableName} (`uid`,`resource_id`,`resource_type`,`resource_number`,`status`,`end_time`, `resource_origin`, `ctime`) values " +
            "<foreach collection = 'dataList' item = 'item' separator = ','>" +
            "(#{item.uid}, #{item.resourceId}, #{item.resourceType},#{item.resourceNumber},#{item.status}, #{item.endTime}, #{item.resourceOrigin}, #{item.ctime})" +
            "</foreach>" +
            "</script>")
    void insertMany(@Param("tableName") String tableName, @Param("dataList") Collection<UserResourceData> dataList);

    @Select("SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} where uid=#{uid} and resource_type=#{resourceType} order by ctime desc limit #{offset},#{pageSize} ")
    List<UserResourceData> selectPage(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resourceType") int resourceType, @Param("offset") int offset, @Param("pageSize") int pageSize);

    @Select("SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} where uid=#{uid} and resource_type=#{resourceType} and status=#{status} order by ctime desc limit #{offset},#{pageSize} ")
    List<UserResourceData> selectPageByStatus(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resourceType") int resourceType, @Param("status") int status, @Param("offset") int offset, @Param("pageSize") int pageSize);

    @Select("SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} where uid=#{uid} and resource_id=#{resourceId} limit 1")
    UserResourceData selectUserResource(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resourceId") int resourceId);

    @Select("SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} where uid=#{uid} and id=#{id} limit 1")
    UserResourceData selectUserResourceById(@Param("tableName") String tableName, @Param("uid") String uid, @Param("id") int id);

    @Select("SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} where uid=#{uid} and resource_id=#{resourceId} and end_time=#{endTime} limit 1")
    UserResourceData selectUserResourceByEndTime(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resourceId") int resourceId, @Param("endTime") long endTime);

    @Select("SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} where uid=#{uid} and resource_type=#{resourceType} and status=1 limit 1")
    UserResourceData selectUserUsingResource(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resourceType") int resourceType);

    @Select("SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} WHERE uid=#{uid} AND status=1")
    List<UserResourceData> selectUserUsingAllResourceList(@Param("tableName") String tableName, @Param("uid") String uid);

    @Update("update ${tableName} set status=#{status} where uid=#{uid} and resource_id=#{resourceId}")
    void updateStatus(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resourceId") int resourceId, @Param("status") int status);

    @Update("update ${tableName} set resource_id=#{resourceId} where uid=#{uid} and id=#{id}")
    void updateResourceIdById(@Param("tableName") String tableName, @Param("uid") String uid, @Param("id") int id, @Param("resourceId") int resourceId);

    @Update("update ${tableName} set status=#{status} where uid=#{uid} and id=#{id}")
    void updateStatusById(@Param("tableName") String tableName, @Param("uid") String uid, @Param("id") int id, @Param("status") int status);

    @Update("update ${tableName} set status=#{status}, end_time=#{endTime} where uid=#{uid} and resource_id=#{resourceId}")
    void updateEndTime(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resourceId") int resourceId, @Param("status") int status, @Param("endTime") long endTime);

    @Update("update ${tableName} set resource_number=#{num} where id=#{id}")
    void updateNumberById(@Param("tableName") String tableName, @Param("id") int id, @Param("num") int num);

    @Delete("delete from ${tableName} where uid=#{uid} and resource_id=#{resourceId}")
    void deleteUserResource(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resourceId") int resourceId);

    @Delete("delete from ${tableName} where id=#{id}")
    void deleteUserResourceById(@Param("tableName") String tableName, @Param("id") int id);

    @Select("SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} WHERE resource_type=#{resourceType} AND end_time > #{startTime} AND end_time <= #{endTime}")
    List<UserResourceData> getExpiredResourceList(@Param("tableName") String tableName, @Param("resourceType") int resourceType, @Param("startTime") int startTime, @Param("endTime") int endTime);

    @Select("SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} WHERE resource_type=#{resourceType} AND end_time > #{startTime} AND end_time <= #{endTime} AND status=0")
    List<UserResourceData> getVipCardExpiredResourceList(@Param("tableName") String tableName, @Param("resourceType") int resourceType, @Param("startTime") int startTime, @Param("endTime") int endTime);


    @Select("SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} WHERE uid=#{uid} AND resource_type=#{resourceType} AND status=1 ORDER BY ctime DESC")
    List<UserResourceData> selectUserUsingResourceList(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resourceType") int resourceType);

    @Select({
            "<script>",
            "SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} WHERE uid=#{uid} AND resource_type=#{resType} ",
            "<if test='resId != null and resId != 0'> ",
            " AND resource_id=#{resId} ",
            " </if>",
            "order by ctime desc limit #{offset},#{pageSize} ",
            "</script>"
    })
    List<UserResourceData> selectListByUid(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resType") Integer resType, @Param("resId") Integer resId, @Param("offset") int offset, @Param("pageSize") int pageSize);

    @Select({
            "<script>",
            "SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} WHERE uid=#{uid} AND resource_type=#{resType} ",
            "<if test='resId != null and resId != 0'> ",
            " AND resource_id=#{resId} ",
            " </if>",
            "order by ctime desc",
            "</script>"
    })
    List<UserResourceData> selectAllListByUid(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resType") Integer resType, @Param("resId") Integer resId);

    @Select({
            "<script>",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM ( ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_0 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_1 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_2 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_3 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_4 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_5 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_6 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_7 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if>) as stat order by ctime DESC limit #{offset},#{pageSize}",
            "</script>"
    })
    List<UserResourceData> selectList(@Param("resType") Integer resType, @Param("resId") Integer resId, @Param("offset") int offset, @Param("pageSize") int pageSize);

    @Select({
            "<script>",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM ( ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_0 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_1 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_2 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_3 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_4 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_5 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_6 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_7 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if>) as stat order by ctime DESC",
            "</script>"
    })
    List<UserResourceData> selectAllList(@Param("resType") Integer resType, @Param("resId") Integer resId);

    @Select({
            "<script>",
            "SELECT count(1) FROM ${tableName} WHERE uid=#{uid} AND resource_type=#{resType}",
            "<if test='resId != null and resId != 0'> ",
            " AND resource_id=#{resId} ",
            " </if>",
            "</script>"
    })
    long selectCountByUid(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resType") Integer resType, @Param("resId") Integer resId);

    @Select({
            "<script>",
            "SELECT count(1) FROM ( ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_0 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_1 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_2 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_3 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_4 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_5 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_6 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if> UNION ALL ",
            "SELECT id,uid,resource_id,resource_type,resource_number,status,end_time,resource_origin,ctime FROM t_user_resource_7 WHERE resource_type=#{resType} <if test='resId != null and resId != 0'> AND resource_id=#{resId} </if>) as stat ",
            "</script>"
    })
    long selectCount(@Param("resType") Integer resType, @Param("resId") Integer resId);


    @Select({
            "<script>",
            "SELECT SUM(resource_number) FROM ${tableName} WHERE uid=#{uid} AND resource_type=#{resType}",
            " AND resource_id=#{resId}",
            " AND end_time>=#{endTime}",
            "</script>"
    })
    Integer selectTotalByUidEndTime(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resType") Integer resType
            , @Param("resId") Integer resId, @Param("endTime") Integer endTime);

    @Select({
            "<script>",
            "SELECT id, uid, resource_id, resource_type, resource_number, status, end_time, resource_origin, ctime FROM ${tableName} WHERE uid=#{uid} AND resource_type=#{resType} ",
            " AND resource_id=#{resId} ",
            " AND end_time>=#{endTime}",
            "order by end_time desc ",
            "</script>"
    })
    List<UserResourceData> selectListByUidEndTime(@Param("tableName") String tableName, @Param("uid") String uid, @Param("resType") Integer resType
            , @Param("resId") Integer resId, @Param("endTime") Integer endTime);


    @Delete("<script>" +
            "delete from ${tableName} where id in " +
            "<foreach collection = 'idList' open = '(' item = 'item' separator = ',' close = ')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    void deleteUserResourceByIdList(@Param("tableName") String tableName, @Param("idList") Collection<Integer> idList);
}
