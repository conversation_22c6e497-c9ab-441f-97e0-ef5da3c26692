package com.quhong.mysql.data;

/**
 * vip配置子配置项
 * @date 2025/5/20
 */
public class VipConfigMeta {
    /**
     * 特性属性: 个人特权和房间特权 0:个人特权 1:房间特权
     */
    private int property;
    /**
     * 特性类型:
     * 0: 勋章
     * 1: 资料卡
     * 2: 个性名片
     * 3: 排名靠前
     * 4: 进场通知
     * 5: 专属聊天气泡
     * 6: 麦位框
     * 7: 贵族专属礼物
     * 8: 专属房间背景
     * 9: 坐骑
     * 10: 直播房
     * 11: 房间主题
     * 12: 锁房间
     * 13: 激活广播
     * 14: 修改国家
     * 15: 防踢
     * 16: 防禁言（新增）
     * 17: 上传房间背景
     * 18: 奖励钻石
     * 19: 发送图片
     * 20: 更多麦位
     * 21: 副房主
     * 22: 隐藏访客身份
     * 23: 飘屏装扮
     * 24: 专属表情
     */
    private int type;
    /**
     * 特权图标
     */
    private String icon;
    /**
     * 特权预览Url
     */
    private String preview;
    /**
     * 动画Url
     */
    private String animationUrl;
    /**
     * 特权标题英语
     */
    private String titleEn;
    /**
     * 特权标题阿语
     */
    private String titleAr;
    /**
     * 特权描述英语
     */
    private String descriptionEn;
    /**
     * 特权描述阿语
     */
    private String descriptionAr;

    public int getProperty() {
        return property;
    }

    public void setProperty(int property) {
        this.property = property;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getPreview() {
        return preview;
    }

    public void setPreview(String preview) {
        this.preview = preview;
    }

    public String getAnimationUrl() {
        return animationUrl;
    }

    public void setAnimationUrl(String animationUrl) {
        this.animationUrl = animationUrl;
    }

    public String getTitleEn() {
        return titleEn;
    }

    public void setTitleEn(String titleEn) {
        this.titleEn = titleEn;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public String getDescriptionEn() {
        return descriptionEn;
    }

    public void setDescriptionEn(String descriptionEn) {
        this.descriptionEn = descriptionEn;
    }

    public String getDescriptionAr() {
        return descriptionAr;
    }

    public void setDescriptionAr(String descriptionAr) {
        this.descriptionAr = descriptionAr;
    }
}
