package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * vip配置表
 * @date 2025/5/20
 */
@TableName("t_vip_config")
public class VipConfigData {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * vip等级
     */
    private Integer vipLevel;
    /**
     * vip广播头像框
     */
    private Integer vipGift;
    /**
     * vip名称英语
     */
    private String nameEn;
    /**
     * vip名称阿语
     */
    private String nameAr;
    /**
     * vip广播头像框
     */
    private String vipFrame;
    /**
     * vip广播勋章
     */
    private String vipModel;
    /**
     * vip广播背景图
     */
    private String vipMsgBackground;
    /**
     * vip购买价格
     */
    private Integer price;
    /**
     * vip有效天数
     */
    private Integer validDay;
    /**
     * 资源key
     */
    private String resourceKey;
    /**
     * 特权
     */
    private String privilege;
    /**
     * 修改时间
     */
    private Integer mtime;
    /**
     * 创建时间
     */
    private Integer ctime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(Integer vipLevel) {
        this.vipLevel = vipLevel;
    }

    public Integer getVipGift() {
        return vipGift;
    }

    public void setVipGift(Integer vipGift) {
        this.vipGift = vipGift;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getVipFrame() {
        return vipFrame;
    }

    public void setVipFrame(String vipFrame) {
        this.vipFrame = vipFrame;
    }

    public String getVipModel() {
        return vipModel;
    }

    public void setVipModel(String vipModel) {
        this.vipModel = vipModel;
    }

    public String getVipMsgBackground() {
        return vipMsgBackground;
    }

    public void setVipMsgBackground(String vipMsgBackground) {
        this.vipMsgBackground = vipMsgBackground;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public Integer getValidDay() {
        return validDay;
    }

    public void setValidDay(Integer validDay) {
        this.validDay = validDay;
    }

    public String getResourceKey() {
        return resourceKey;
    }

    public void setResourceKey(String resourceKey) {
        this.resourceKey = resourceKey;
    }

    public String getPrivilege() {
        return privilege;
    }

    public void setPrivilege(String privilege) {
        this.privilege = privilege;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
