package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 冠名礼物表
 *
 */
@TableName("t_gift_recv_send_first")
public class SendRecvFirstData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 发送者uid
     */
    private String sendUid;

    /**
     * 接受者uid
     */
    private String receiveUid;

    private Integer giftId;

    /**
     * 发送的礼物数量
     */
    private Long giftNum;

    /**
     * 修改时间
     */
    private Integer mtime;

    public SendRecvFirstData() {
    }

    public SendRecvFirstData(String sendUid, String receiveUid, Integer giftId, Long giftNum, Integer mtime) {
        this.sendUid = sendUid;
        this.receiveUid = receiveUid;
        this.giftId = giftId;
        this.giftNum = giftNum;
        this.mtime = mtime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSendUid() {
        return sendUid;
    }

    public void setSendUid(String sendUid) {
        this.sendUid = sendUid;
    }

    public String getReceiveUid() {
        return receiveUid;
    }

    public void setReceiveUid(String receiveUid) {
        this.receiveUid = receiveUid;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public Long getGiftNum() {
        return giftNum;
    }

    public void setGiftNum(Long giftNum) {
        this.giftNum = giftNum;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return "SendRecvFirstData{" +
                "id=" + id +
                ", sendUid='" + sendUid + '\'' +
                ", receiveUid='" + receiveUid + '\'' +
                ", giftId=" + giftId +
                ", giftNum=" + giftNum +
                ", mtime=" + mtime +
                '}';
    }
}
