package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 房间欢迎消息审核表
 *
 * <AUTHOR>
 * @date 2025/8/25 15:16
 */
@TableName("t_welcome_message_review")
public class WelcomeMessageReviewData {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 提交者uid
     */
    private String submitterUid;
    /**
     * 房间ID
     */
    private String roomId;

    /**
     * 消息内容
     */
    private String messageContent;

    /**
     * 审核状态 1：待审核 2：通过 3：拒绝
     */
    private Integer reviewAction;

    /**
     * 拒绝通过的原因，默认为NULL
     */
    private String rejectReason;

    /**
     * 审核人uid，默认为NULL
     */
    private String operatorUid;

    /**
     * 用户提交时间(秒级时间戳)
     */
    private Integer submitTime;

    /**
     * 用户修改消息时间(秒级时间戳)
     */
    private Integer updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSubmitterUid() {
        return submitterUid;
    }

    public void setSubmitterUid(String submitterUid) {
        this.submitterUid = submitterUid;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public Integer getReviewAction() {
        return reviewAction;
    }

    public void setReviewAction(Integer reviewAction) {
        this.reviewAction = reviewAction;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public String getOperatorUid() {
        return operatorUid;
    }

    public void setOperatorUid(String operatorUid) {
        this.operatorUid = operatorUid;
    }

    public Integer getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Integer submitTime) {
        this.submitTime = submitTime;
    }

    public Integer getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }
}