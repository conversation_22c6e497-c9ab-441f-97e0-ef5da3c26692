package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.mysql.data.SendRecvFirstData;
import com.quhong.mysql.data.WelcomeMessageReviewData;
import com.quhong.mysql.mapper.ustar.WelcomeMessageReviewMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 房间欢迎消息dao
 *
 * <AUTHOR>
 * @date 2025/8/25 15:33
 */
@Lazy
@Component
public class WelcomeMessageReviewDao {

    private static final Logger logger = LoggerFactory.getLogger(WelcomeMessageReviewDao.class);

    public static final String TABLE_PRE = "t_welcome_message_review";

    @Resource
    private WelcomeMessageReviewMapper mapper;


    /**
     * 分页获取欢迎消息审核数据
     */
    public IPage<WelcomeMessageReviewData> selectPageList(int page, int pageSize,WelcomeMessageReviewData condition) {

    }

    /**
     * 插入欢迎消息审核数据
     *
     * @param data 欢迎消息审核信息
     * @return 返回插入操作影响的行数
     */
    public int insert(WelcomeMessageReviewData data) {

    }


    public void batchInsert(String roomId, List<WelcomeMessageReviewData> insertList) {
        String tableName = getTableName(roomId);
        mapper.batchInsert(tableName, insertList);
    }
    /**
     * 删除欢迎消息
     *
     * @return 影响行数
     */
    public int delete(String roomId, Integer id) {

    }

    /**
     * 更新欢迎消息,同时把审核状态更新为待审核
     *
     * @return 影响行数
     */
    public int updateContent(String roomId,Integer id,String messageContent) {

    }

    /**
     * 更新欢迎消息审核状态
     *
     * @return 影响行数
     */
    public int updateReviewAction(String roomId, Integer id, String operatorUid, Integer reviewAction,String rejectReason) {

    }


    private String getTableName(WelcomeMessageReviewData data) {
        return getTableName(data.getRoomId());
    }

    /**
     * 获取分表名
     */
    private String getTableName(String roomId) {
        return TABLE_PRE + "_" + roomId.substring(roomId.length() - 1);
    }
}
