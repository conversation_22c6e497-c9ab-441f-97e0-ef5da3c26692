package com.quhong.mysql.dao;

import com.quhong.data.SendRecvFirstJoinData;
import com.quhong.mysql.data.SendRecvFirstData;
import com.quhong.mysql.mapper.ustar_log.SendRecvFirstMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/19
 */
@Lazy
@Component
public class SendRecvFirstDao {

    private final static Logger logger = LoggerFactory.getLogger(SendRecvFirstDao.class);

    public static final String TABLE_PRE = "t_gift_recv_send_first";

    @Resource
    private SendRecvFirstMapper sendRecvFirstMapper;

    public void insert(SendRecvFirstData data) {
        sendRecvFirstMapper.insert(getTableName(data.getReceiveUid()), data);
    }

    public int incGiftNum(SendRecvFirstData data) {
        return sendRecvFirstMapper.incGiftNum(getTableName(data.getReceiveUid()), data);
    }

    public List<SendRecvFirstJoinData> getFirstJoinData(String receiveUid) {
        List<SendRecvFirstJoinData> list = sendRecvFirstMapper.getFirstJoinData(getTableName(receiveUid), receiveUid);
        if (list == null) {
            return Collections.emptyList();
        }
        return list;
    }

    public List<SendRecvFirstData> getGidNumList(String sendUid, String receiveUid) {
        List<SendRecvFirstData> list = sendRecvFirstMapper.getGidNumList(getTableName(receiveUid), receiveUid, sendUid);
        if (list == null) {
            return Collections.emptyList();
        }
        return list;
    }

    public List<SendRecvFirstData> selectBySendUid(String tableName, String sendUid, List<String> uidList) {
        return sendRecvFirstMapper.selectBySendUid(tableName, sendUid, uidList);
    }

    public void batchInsert(String tableName, List<SendRecvFirstData> insertList) {
        sendRecvFirstMapper.batchInsert(tableName, insertList);
    }

    public void batchIncGiftNum(String tableName, List<SendRecvFirstData> updateList) {
        sendRecvFirstMapper.batchIncGiftNum(tableName, updateList);
    }

    public SendRecvFirstData getFirstDataByReceiveGid(String receiveUid, int giftId) {
        return sendRecvFirstMapper.getFirstDataByReceiveGid(getTableName(receiveUid), receiveUid, giftId);
    }

    public SendRecvFirstData getDataByUid(String receiveUid, String sendUid, int giftId) {
        return sendRecvFirstMapper.getDataByUid(getTableName(receiveUid), receiveUid, sendUid, giftId);
    }


    /**
     * 获取分表名
     */
    private String getTableName(String uid) {
        return TABLE_PRE + "_" + uid.substring(uid.length() - 1);
    }
}
