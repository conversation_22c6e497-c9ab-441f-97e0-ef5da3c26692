package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.enums.LogType;
import com.quhong.mysql.data.NewcomerTaskData;
import com.quhong.mysql.mapper.ustar.NewcomerTaskMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22
 */
@Component
public class NewcomerTaskDao {
    private static final List<String> NEW_COMER_TASK_V1_LIST = Arrays.asList("update_personal_info", "send_room_msg_1", "send_room_gift_1", "add_friend", "follow_room", "join_room_member", "like_or_comment_moment_1");
    private static final List<String> NEW_COMER_TASK_V2_LIST = Arrays.asList("stay_in_the_room", "web_rookie_on_mic_time_5_minute", "web_rookie_update_personal_info", "web_rookie_send_room_msg",
            "web_rookie_send_room_gift", "web_rookie_add_friend", "web_rookie_follow_room", "web_rookie_join_room_member", "web_rookie_like_or_comment_moment");

    @Resource
    private NewcomerTaskMapper newcomerTaskMapper;

    public void insert(NewcomerTaskData data) {
        newcomerTaskMapper.insert(data);
    }

    public void update(NewcomerTaskData data) {
        newcomerTaskMapper.updateById(data);
    }

    public NewcomerTaskData getByUidAndTaskKey(String uid, String taskKey) {
        QueryWrapper<NewcomerTaskData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("task_key", taskKey);
        return newcomerTaskMapper.selectOne(queryWrapper);
    }

    public List<NewcomerTaskData> getListByUid(String uid) {
        QueryWrapper<NewcomerTaskData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        return newcomerTaskMapper.selectList(queryWrapper);
    }

    public int selectHasRewardCount(String uid, int version) {
        QueryWrapper<NewcomerTaskData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("status", 1);
        queryWrapper.in("task_key", version == 1 ? NEW_COMER_TASK_V1_LIST : NEW_COMER_TASK_V2_LIST);
        return newcomerTaskMapper.selectCount(queryWrapper);
    }


    public List<NewcomerTaskData> selectNewcomerTaskByTime(String startObj, String endObj) {
        try {
            QueryWrapper<NewcomerTaskData> queryWrapper = new QueryWrapper<>();
            queryWrapper.ge("uid", startObj);
            queryWrapper.le("uid", endObj);
            return newcomerTaskMapper.selectList(queryWrapper);
        }catch (Exception e){
            return Collections.emptyList();
        }
    }
}
