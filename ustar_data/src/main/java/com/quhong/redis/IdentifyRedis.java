package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class IdentifyRedis {
    private static final Logger logger = LoggerFactory.getLogger(IdentifyRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String identifyKey() {
        return "uid:identify";
    }


    public void setUserIdentify(String uid, int status) {
        try {
            clusterTemplate.opsForHash().put(identifyKey(), uid, String.valueOf(status));
        } catch (Exception e) {
            logger.info("setUserIdentify error uid={}, e={}", uid, e.getMessage());
        }
    }


    private String vipExpirePushDateKey(){
         return  "vip_expire_push_date";
    }

    public String getVipExpirePushDate() {
        try {
            return clusterTemplate.opsForValue().get(vipExpirePushDateKey());
        } catch (Exception e) {
            logger.info("getVipExpirePushDate error e={}", e.getMessage());
        }
        return "";
    }

    public void setVipExpirePushDate(String checkDate) {
        try {
            clusterTemplate.opsForValue().set(vipExpirePushDateKey(), checkDate, ExpireTimeConstant.EXPIRE_DAYS_TEN, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setVipExpirePushDate error e={}", e.getMessage());
        }
    }

    /**
     * vip配置: 需要定期处理的vip用户
     */
    private String vipHashAllConfigKey() {
        return "hash:all:vipConfig";
    }

    public void setVipAllConfig(String uid, String value) {
        try {
            clusterTemplate.opsForHash().put(vipHashAllConfigKey(), uid, value);
        } catch (Exception e) {
            logger.info("setVipAllConfig error uid={}, e={}", uid, e.getMessage());
        }
    }

    public void delVipAllConfig(String uid) {
        try {
            clusterTemplate.opsForHash().delete(vipHashAllConfigKey(), uid);
        } catch (Exception e) {
            logger.info("setVipAllConfig error uid={}, e={}", uid, e.getMessage());
        }
    }

    public String getVipAllConfig(String uid) {
        try {
            return (String) clusterTemplate.opsForHash().get(vipHashAllConfigKey(), uid);
        } catch (Exception e) {
            logger.info("getVipAllConfig error uid={}, e={}", uid, e.getMessage());
        }
        return null;
    }

    /**
     * 获取hash key所有值
     */
    public Map<String, String> getVipAllConfigAllMap() {
        Map<String, String> hashMap = new HashMap<>();

        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(vipHashAllConfigKey());
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", String.valueOf(entry.getValue()));
            }
            return hashMap;
        } catch (Exception e) {
            logger.error("getVipAllConfigAllMap error e={}", e.getMessage(), e);
        }
        return hashMap;
    }


}
