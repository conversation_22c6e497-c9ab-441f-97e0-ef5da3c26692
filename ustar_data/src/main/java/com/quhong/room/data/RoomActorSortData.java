package com.quhong.room.data;

import com.alibaba.fastjson.annotation.JSONField;

public class RoomActorSortData {
    protected String uid;
    /**
     * @see com.quhong.enums.RoomRoleType
     */
    protected int role;
    /**
     * 是否副房主 0 否，1 是
     */
    protected int viceHost;
    @JSONField(name = "vip_level")
    protected int vipLevel;
    /**
     * 用户等级
     */
    private int level;
    /**
     * 用户荣誉
     */
    private long honor;
    /**
     * 进场动画预览图
     */
    private String joinCartonIcon;
    /**
     * 用户麦位位置，从零开始, -1为不在麦位上
     */
    private int micPosition;

    /**
     * 注册时间
     */
    private int registrationTime;

    /**
     * 1正在进行游戏 0没有 新版游戏房才有
     */
    private int gameRunning;
    /**
     * 在房间 0否 1是
     */
    private int inRoom = 1;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public int getViceHost() {
        return viceHost;
    }

    public void setViceHost(int viceHost) {
        this.viceHost = viceHost;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public long getHonor() {
        return honor;
    }

    public void setHonor(long honor) {
        this.honor = honor;
    }

    public String getJoinCartonIcon() {
        return joinCartonIcon;
    }

    public void setJoinCartonIcon(String joinCartonIcon) {
        this.joinCartonIcon = joinCartonIcon;
    }

    public int getMicPosition() {
        return micPosition;
    }

    public void setMicPosition(int micPosition) {
        this.micPosition = micPosition;
    }

    public int getRegistrationTime() {
        return registrationTime;
    }

    public void setRegistrationTime(int registrationTime) {
        this.registrationTime = registrationTime;
    }

    public int getGameRunning() {
        return gameRunning;
    }

    public void setGameRunning(int gameRunning) {
        this.gameRunning = gameRunning;
    }

    public int getInRoom() {
        return inRoom;
    }

    public void setInRoom(int inRoom) {
        this.inRoom = inRoom;
    }
}
