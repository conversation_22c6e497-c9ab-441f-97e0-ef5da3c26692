package com.quhong.room.data;

import com.alibaba.fastjson.annotation.JSONField;

public class RoomActorCacheData {
    protected String aid;
    protected String head;
    @JSONField(name = "vip_level")
    protected int vipLevel;
    /**
     * @see com.quhong.enums.RoomRoleType
     */
    protected int role;
    /**
     * 是否副房主 0 否，1 是
     */
    protected int viceHost;
    /**
     * @see com.quhong.enums.IdentifyType
     */
    protected int identify; // 0 无 1 vip 2 svip

    protected String name;
    protected int beautifulRid; //是否靓号, 0 否，1 是

    /**
     * 是否为设备首账号 1 是 0不是
     */
    protected int isNewUser;

    public RoomActorCacheData() {

    }

    public void copyFrom(RoomActorCacheData src) {
        this.aid = src.aid;
        this.head = src.head;
        this.vipLevel = src.vipLevel;
        this.role = src.role;
        this.identify = src.identify;
        this.name = src.name;
        this.viceHost = src.viceHost;
        this.beautifulRid = src.beautifulRid;
        this.isNewUser = src.isNewUser;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public int getIdentify() {
        return identify;
    }

    public void setIdentify(int identify) {
        this.identify = identify;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getViceHost() {
        return viceHost;
    }

    public void setViceHost(int viceHost) {
        this.viceHost = viceHost;
    }

    public int getBeautifulRid() {
        return beautifulRid;
    }

    public void setBeautifulRid(int beautifulRid) {
        this.beautifulRid = beautifulRid;
    }

    public int getIsNewUser() {
        return isNewUser;
    }

    public void setIsNewUser(int isNewUser) {
        this.isNewUser = isNewUser;
    }
}
