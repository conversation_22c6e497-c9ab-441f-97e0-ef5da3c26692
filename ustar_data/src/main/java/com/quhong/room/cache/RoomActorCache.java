package com.quhong.room.cache;

import com.alibaba.fastjson.JSONObject;
import com.quhong.cache.CacheMap;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.RoomRoleData;
import com.quhong.data.SudGameInfo;
import com.quhong.enums.RoomRoleType;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.mysql.dao.RoomMicDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.RoomMicData;
import com.quhong.mysql.data.SmashEggConfigData;
import com.quhong.redis.SudGameRedis;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.data.RoomActorSortData;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 房间内用户缓存
 */
@Lazy
@Service
public class RoomActorCache {
    private static final Logger logger = LoggerFactory.getLogger(RoomActorCache.class);
    private static final long CACHE_TIME_MILLIS = 120 * 1000L;
    private final CacheMap<String, RoomActorDetailData> cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);

    @Autowired
    private VipInfoDao vipInfoDao;
    @Autowired
    private ActorDao actorDao;
    @Autowired
    private BadgeDao badgeDao;
    @Autowired
    private BadgeListDao badgeListDao;
    @Autowired
    private UserLevelDao userLevelDao;
    @Autowired
    private BubbleDao bubbleDao;
    @Autowired
    private RoomMemberDao roomMemberDao;
    @Autowired
    private MicFrameRedis micFrameRedis;
    @Autowired
    private RippleDao rippleDao;
    @Autowired
    private BeautifulRidDao beautifulRidDao;
    @Autowired
    private NewUserHonorDao newUserHonorDao;
    @Autowired
    private JoinCartonDao joinCartonDao;
    @Autowired
    private JoinSourceDao joinSourceDao;
    @Autowired
    private RoomMicDao roomMicDao;
    @Autowired
    private SudGameRedis sudGameRedis;
    @Autowired
    private RoomActorCache roomActorCache ;

    @PostConstruct
    public void postInit() {
        this.cacheMap.start();
    }


    /**
     * 获取排序列表
     *
     * @param roomId
     * @param actorSet
     * @return
     */
    public List<RoomActorSortData> getSortList(String roomId, Set<String> actorSet) {
        List<RoomActorSortData> retList = new ArrayList<>();
        boolean isGameRoom = RoomUtils.isGameRoom(roomId);
        // 优先从缓存获取
        Map<String, RoomRoleData> roleMap = roomMemberDao.getRoleDataMap(roomId, actorSet);
        // 获取vip
        Map<String, Integer> vipMap = vipInfoDao.getVipMap(actorSet);
        // 获取用户等级列表，优先从缓存获取
        Map<String, UserLevelData> levelMap = userLevelDao.getUserLevelMap(actorSet);
        // 获取房间麦位用户
        List<RoomMicData> roomMicDataList = roomMicDao.getDataFromRedis(roomId);
        Map<String, Integer> micMap = roomMicDataList.stream().filter(distinctByKey(RoomMicData::getUid))
                .filter(o -> !StringUtils.isEmpty(o.getUid()))
                .collect(Collectors.toMap(RoomMicData::getUid, RoomMicData::getPosition));
        List<String> inGameList = new ArrayList<>();
        if (isGameRoom) {
            inGameList = sudGameRedis.getInGameUserList(roomId);
        }
        for (String uid : actorSet) {
            RoomActorSortData sortData = new RoomActorSortData();
            sortData.setUid(uid);
            RoomRoleData roomRoleData = roleMap.get(uid);
            if (roomRoleData != null) {
                sortData.setRole(roomRoleData.getRole());
                sortData.setViceHost(roomRoleData.getViceHost());
            } else {
                sortData.setRole(RoomRoleType.AUDIENCE);
            }
            Integer vipLevel = vipMap.get(uid);
            sortData.setVipLevel(vipLevel != null ? vipLevel : 0);
            UserLevelData levelData = levelMap.get(uid);
            sortData.setLevel(levelData != null ? levelData.getLevel() : 0);
            sortData.setMicPosition(micMap.getOrDefault(uid, -1));
            if (isGameRoom) {
                if (inGameList.contains(uid)) {
                    sortData.setGameRunning(1);
                }
            }
            retList.add(sortData);
        }
        return retList;
    }

    /**
     * 获取大R排序列表
     * VIP lounge规则：Queen、VIP5及以上等级的用户和荣誉积分200万及以上的用户。
     */
    public List<RoomActorSortData> getVipLoungeSortList(Set<String> actorSet) {
        List<RoomActorSortData> retList = new ArrayList<>();
        // 获取用户荣誉
        Map<String, Long> honorMap = newUserHonorDao.getHonorMap(actorSet);
        // 获取vip
        Map<String, Integer> vipMap = vipInfoDao.getVipMap(actorSet);
        List<JoinCartonData> joinCartonList = joinCartonDao.findByUidSet(actorSet, 1);
        Map<String, JoinCartonData> joinCartonMap = CollectionUtil.listToKeyMap(joinCartonList, JoinCartonData::getUid);
        // 获取用户等级列表，优先从缓存获取
        Map<String, UserLevelData> levelMap = userLevelDao.getUserLevelMap(actorSet);
        for (String uid : actorSet) {
            int vipLevel = vipMap.getOrDefault(uid, 0);
            long honor = honorMap.getOrDefault(uid, 0L);
            if (vipLevel >= 5 || honor > 2000000) {
                RoomActorSortData sortData = new RoomActorSortData();
                sortData.setUid(uid);
                sortData.setHonor(honor);
                sortData.setVipLevel(vipLevel);
                JoinCartonData joinCartonData = joinCartonMap.get(uid);
                JoinSourceData joinSourceData = joinSourceDao.getSourceData(joinCartonData == null ? 0 : joinCartonData.getJoin_carton_id());
                sortData.setJoinCartonIcon(null == joinSourceData ? null : joinSourceData.getSmall_icon());
                UserLevelData levelData = levelMap.get(uid);
                sortData.setLevel(levelData != null ? levelData.getLevel() : 0);
                retList.add(sortData);
            }
        }
        return retList;
    }

    /**
     * 获取排序列表
     *
     * @param roomId
     * @param actorSet
     * @return
     */
    public List<RoomActorSortData> getNewUserSortList(String roomId, Set<String> actorSet) {
        List<RoomActorSortData> retList = new ArrayList<>();
        // 优先从缓存获取
        Map<String, RoomRoleData> roleMap = roomMemberDao.getRoleDataMap(roomId, actorSet);
        // 获取vip
        Map<String, Integer> vipMap = vipInfoDao.getVipMap(actorSet);
        // 获取用户等级列表，优先从缓存获取
        Map<String, UserLevelData> levelMap = userLevelDao.getUserLevelMap(actorSet);
        // 获取房间麦位用户
        List<RoomMicData> roomMicDataList = roomMicDao.getDataFromRedis(roomId);
        Map<String, Integer> micMap = roomMicDataList.stream().filter(distinctByKey(RoomMicData::getUid))
                .filter(o -> !StringUtils.isEmpty(o.getUid()))
                .collect(Collectors.toMap(RoomMicData::getUid, RoomMicData::getPosition));

        for (String uid : actorSet) {
            RoomActorDetailData detailData = roomActorCache.getData(roomId, uid, false);
            if (detailData == null) {
                logger.error("get detail data error. roomId={} uid={}", roomId, uid);
                continue;
            }
            if (detailData.getIsNewUser() != 1) {
                continue;
            }
            RoomActorSortData sortData = new RoomActorSortData();
            sortData.setUid(uid);
            RoomRoleData roomRoleData = roleMap.get(uid);
            if (roomRoleData != null) {
                sortData.setRole(roomRoleData.getRole());
                sortData.setViceHost(roomRoleData.getViceHost());
            } else {
                sortData.setRole(RoomRoleType.AUDIENCE);
            }
            Integer vipLevel = vipMap.get(uid);
            sortData.setVipLevel(vipLevel != null ? vipLevel : 0);
            UserLevelData levelData = levelMap.get(uid);
            sortData.setLevel(levelData != null ? levelData.getLevel() : 0);
            sortData.setMicPosition(micMap.getOrDefault(uid, -1));
            sortData.setRegistrationTime(new ObjectId(uid).getTimestamp());
            retList.add(sortData);
        }
        return retList;
    }


    public RoomActorDetailData getData(String roomId, String uid, boolean force) {
        return getData(roomId, uid, force, null);
    }

    public RoomActorDetailData getData(String roomId, String uid, boolean force, RoomActorSortData sortData) {
        RoomActorDetailData detailData = cacheMap.getData(uid);
        if (force) {
            force = getFixForce(uid);
        }
        if (detailData == null) {
            // 缓存过期
            detailData = getDataFromDB(uid);
        } else if (force) {
            // 强制刷新数据
            updateBaseData(detailData);
            updateSensitiveData(detailData);
            cacheMap.cacheData(uid, detailData);
        } else if (cacheMap.getWriteTime(uid) > 30 * 1000L) {
            // 30秒刷新敏感数据
            updateSensitiveData(detailData);
            // 120秒刷新基础数据
            if (DateHelper.getNowSeconds() - detailData.getBaseUpdateTime() > 120) {
                updateBaseData(detailData);
            }
            cacheMap.cacheData(uid, detailData);
        }
        if ((!force) && sortData != null) {
            // 解决并发
            detailData.setRole(sortData.getRole());
            detailData.setVipLevel(sortData.getVipLevel());
            detailData.setViceHost(sortData.getViceHost());
            detailData.setLevel(sortData.getLevel());
        } else if (!StringUtils.isEmpty(roomId)) {
            // 解决并发
            RoomActorDetailData detailDataNew = new RoomActorDetailData();
            BeanUtils.copyProperties(detailData, detailDataNew);
            RoomRoleData roomRoleData;
            String rippleUrl;
            if (force) {
                roomRoleData = roomMemberDao.getRoleData(roomId, uid);
                rippleUrl = rippleDao.getRippleUrl(roomId, uid);
            } else {
                roomRoleData = roomMemberDao.getRoleDataFromCache(roomId, uid);
                rippleUrl = rippleDao.getRippleUrlCache(roomId, uid);
            }
            detailDataNew.setRole(roomRoleData.getRole());
            detailDataNew.setViceHost(roomRoleData.getViceHost());
            detailDataNew.setRippleUrl(rippleUrl);
            return detailDataNew;
        }
        return detailData;
    }

    /**
     * 最近5秒内写入的数，都视为是新的
     */
    private boolean getFixForce(String uid) {
        long writeTime = cacheMap.getWriteTime(uid);
        if (0 == writeTime) {
            return true;
        }
        return writeTime > 500L;
    }

    private RoomActorDetailData getDataFromDB(String uid) {
        RoomActorDetailData detailData = new RoomActorDetailData();
        detailData.setAid(uid);
        updateBaseData(detailData);
        updateSensitiveData(detailData);
        cacheMap.cacheData(uid, detailData);
        return detailData;
    }

    private void updateBaseData(RoomActorDetailData detailData) {
        try {
            String uid = detailData.getAid();
            ActorData actorData = actorDao.getActorData(uid);
            if (null == actorData) {
                logger.error("cannot find actor uid={}", uid);
                return;
            }
            detailData.copyFrom(actorData);
            detailData.setVipLevel(vipInfoDao.getIntVipLevel(uid));
            detailData.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), detailData.getVipLevel()));
            // 设置徽章
            List<BadgeData> badgeList = badgeDao.findBadgeList(uid);
            if (badgeList.isEmpty()) {
                detailData.setBadgeList(Collections.emptyList());
            } else {
                detailData.setBadgeList(badgeListDao.getSmallIconsFromCache(badgeList));
            }
            detailData.setBeautifulRid(beautifulRidDao.isBeautifulRid(uid));
            detailData.setLevel(userLevelDao.getUserLevel(uid));
            detailData.setBubbleId(bubbleDao.getBubbleId(uid));
            detailData.setBaseUpdateTime(DateHelper.getNowSeconds());
            detailData.setIsNewUser(ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId()) ? 1 : 0);
        } catch (Exception e) {
            logger.error("update base data error. {}", e.getMessage(), e);
        }
    }

    private void updateSensitiveData(RoomActorDetailData detailData) {
        try {
            String uid = detailData.getAid();
            detailData.setIdentify(detailData.getVipLevel() > 0 ? 1 : 0);
            detailData.setMicFrame(micFrameRedis.getMicSourceFromCache(uid));
            detailData.setRippleUrl(rippleDao.getRippleUrl(uid));
        } catch (Exception e) {
            logger.error("update sensitive data error. {}", e.getMessage(), e);
        }
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
