package com.quhong.mq;

import com.alibaba.fastjson.JSON;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.ResourcesDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
public class MqSenderService {

    private static final Logger logger = LoggerFactory.getLogger(MqSenderService.class);
    private static final String COMMON_EXCHANGE = "direct_common_msg";
    private static final String FANOUT_USER_LEVEL_EXCHANGE = "fanout_user_level";
    public static final String TOPIC_COMMON_EXCHANGE = "topic_common_msg";
    private static final String PARTY_GIRL_ROUTE_KEY = "direct_party_girl";
    private static final String DAILY_CHECK_IN_KEY = "daily_check_in";
    private static final String FCM_ROUTE_KEY = "fcm";
    private static final String FOLLOW_ROUTE_KEY = "follow";
    private static final String SEND_GIFT = "send_gift";
    private static final String SEND_GIFT_TIMES_QUEUE = "send_gift_times";
    private static final String SEND_ROOM_MSG_KEY = "send_room_msg";

    public static final String REFUND_APPLE_ROUTE_KEY = "user_refund.apple";
    public static final String REFUND_GOOGLE_ROUTE_KEY = "user_refund.google";

    // 用户房间相关key
    public static final String ROUTE_KEY_USER_ENTER_ROOM = "user_room.enter_room";
    public static final String ROUTE_KEY_USER_PLAY_BAI_SHUN_GAME = "user_room.play_bai_shun_game";
    public static final String ROUTE_KEY_USER_MIC_UP = "user_room.mic_up";

    private final Map<String, String> RECHARGE_ROUTE_KEY_MAP = new HashMap<String, String>() {{
        put("googlePayCharge", "user_recharge.google");
        put("applePayCharge", "user_recharge.apple");
        put("huaweiPayCharge", "user_recharge.huawei");
        put("payCenterCharge", "user_recharge.payCenter");
    }};

    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送PartyGirl任务消息
     */
    public void sendPartyGirlTaskToMq(CommonData data) {
        try {
            String jsonString = JSON.toJSONString(data);
            rabbitTemplate.convertAndSend(COMMON_EXCHANGE, PARTY_GIRL_ROUTE_KEY, jsonString);
        } catch (Exception e) {
            logger.error("send party girl task data to mq error. data={}", JSON.toJSONString(data), e);
        }
    }

    /**
     * 发送每日签到任务消息
     */
    public void sendDailyCheckInTaskToMq(CommonData data) {
        try {
            String jsonString = JSON.toJSONString(data);
            rabbitTemplate.convertAndSend(COMMON_EXCHANGE, DAILY_CHECK_IN_KEY, jsonString);
        } catch (Exception e) {
            logger.error("send daily check in task data to mq error. data={}", JSON.toJSONString(data), e);
        }
    }

    /**
     * 发送站外fcm消息
     */
    public void sendFcmToMq(String jsonString) {
        try {
            logger.info("send fcm data to mq. data={}", jsonString);
            rabbitTemplate.convertAndSend(COMMON_EXCHANGE, FCM_ROUTE_KEY, jsonString);
        } catch (Exception e) {
            logger.error("send fcm data to mq error. data={}", jsonString, e);
        }
    }

    /**
     * 发送关注好友消息
     */
    public void sendFollowToMq(String jsonString) {
        try {
            logger.info("send follow data to mq. data={}", jsonString);
            rabbitTemplate.convertAndSend(COMMON_EXCHANGE, FOLLOW_ROUTE_KEY, jsonString);
        } catch (Exception e) {
            logger.error("send follow data to mq error. data={}", jsonString, e);
        }
    }

    public void sendFanoutUserLevelMq(CommonData data) {
        try {
            String jsonString = JSON.toJSONString(data);
            rabbitTemplate.convertAndSend(FANOUT_USER_LEVEL_EXCHANGE, SEND_ROOM_MSG_KEY, jsonString);
        } catch (Exception e) {
            logger.error("send fanout user level data to mq error. data={}", JSON.toJSONString(data), e);
        }
    }

    /**
     * 发送到队列
     */
    public void sendToMq(String exchange, String queueName, Object data) {
        try {
            String jsonString = JSON.toJSONString(data);
            rabbitTemplate.convertAndSend(exchange, queueName, jsonString);
        } catch (Exception e) {
            logger.error("send to mq error. queueName={} data={}", queueName, JSON.toJSONString(data), e);
        }
    }

    public void sendGiftTimes(Object data) {
        sendToMq("", SEND_GIFT_TIMES_QUEUE, data);
    }

    public void sendGift(Object data) {
        sendToMq(COMMON_EXCHANGE, SEND_GIFT, data);
    }

    public void asyncChargeDiamonds(Object data) {
        sendToMq("", MqItemConstant.CHARGE_DIAMONDS, data);
    }

    public void asyncHandleResources(ResourcesDTO data) {
        sendToMq("", MqItemConstant.HANDLE_RESOURCES, data);
    }

    public void sendRefundOrderToMq(String routeKey,Object data) {
        sendToMq(TOPIC_COMMON_EXCHANGE, routeKey, data);
    }

    public void sendUserRechargeToMq(String rechargeChannel, Object data) {

        String route_key = RECHARGE_ROUTE_KEY_MAP.get(rechargeChannel);
        if(StringUtils.isEmpty(route_key)){
            logger.error("sendUserRechargeToMq route_key not find rechargeChannel={}", rechargeChannel);
            return;
        }

        sendToMq(TOPIC_COMMON_EXCHANGE, route_key, data);
    }

    public void sendTopicMsgToMq(String routeKey, Object data) {
        sendToMq(TOPIC_COMMON_EXCHANGE, routeKey, data);
    }
}
