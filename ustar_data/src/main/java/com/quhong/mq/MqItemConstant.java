package com.quhong.mq;

public class MqItemConstant {

    // 与配对的好友1v1消息
    public static final String MATCH_MSG = "match_msg";
    // 配对数量、配对成功
    public static final String MATCH_SUCCESS = "match_success";
    // 点喜欢
    public static final String MATCH_LIKE = "match_like";
    // 删除匹配
    public static final String MATCH_DELETE = "match_delete";
    // 发送超级喜欢
    public static final String MATCH_SUPER_LIKE = "match_super_like";
    // 玩一局ludo游戏(每日任务)
    public static final String PLAY_LUDO_GAME = "play_ludo_game";
    // 与配对的好友1v1消息(每日任务)
    public static final String MATCH_MSG_TASK = "match_msg_daily_task";
    // 异步打钻
    public static final String CHARGE_DIAMONDS = "charge_diamonds";
    // pro包打招呼成为好友
    public static final String SAY_HELLO_SUCCESS = "say_hello_success";
    // 异步下发资源
    public static final String HANDLE_RESOURCES = "handle_resources";

    // 更新在房间的时间
    public static final String COUNT_ROOM_TIME = "count_room_time";

    // 退房间记录
    public static final String QUIT_ROOM = "quit_room";
    // 进房间记录
    public static final String ENTER_ROOM = "enter_room";

    public static final String SEND_ROOM_MSG = "send_room_msg";
}
