package com.quhong.enums;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
public class CommonMqTaskConstant {

    public static final String TASK_COMMON_EXCHANGE = "task_common_msg";
    public static final String USER_TASK_QUEUE = "user_daily_task";
    public static final String USER_ROOM_RECOMMEND_QUEUE = "user_room_recommend_queue";
    public static final String FCM_MQ_MSG_QUEUE = "fcm_mq_msg_queue";
    public static final String GAME_MQ_MSG_QUEUE = "game_mq_msg_queue";

    public static final String USER_TASK_ROUTING_KEY = "#.user_daily_task.#";

    public static final String USER_FRIEND_LEVEL_KEY = "user_friend_level";
    // 更新个人资料
    public static final String UPDATE_PERSONAL_INFO = "update_personal_info";
    // 发送房间消息
    public static final String SEND_ROOM_MSG = "send_room_msg";
    // 发送大厅消息
    public static final String SEND_HALL_MSG = "send_hall_msg";
    // 发送房间礼物
    public static final String SEND_ROOM_GIFT = "send_room_gift";
    // 申请加好友
    public static final String APPLY_FRIEND = "apply_friend";
    // 加好友
    public static final String ADD_FRIEND = "add_friend";
    // 关注房间
    public static final String FOLLOW_ROOM = "follow_room";
    // 加入房间会员
    public static final String JOIN_ROOM_MEMBER = "join_room_member";
    // 点赞动态
    public static final String LIKE_MOMENT = "like_moment";
    // 取消点赞动态
    public static final String CANCEL_LIKE_MOMENT = "cancel_like_moment";
    // 评论动态
    public static final String COMMENT_MOMENT = "comment_moment";
    // 上麦时长
    public static final String ON_MIC_TIME = "on_mic_time";
    // 连续上麦时长5分钟
    public static final String ON_MIC_TIME_5_MINUTE = "on_mic_time_5_minute";
    // 连续上麦时长10分钟
    public static final String ON_MIC_TIME_10_MINUTE = "on_mic_time_10_minute";
    // 每日登录
    public static final String DAILY_LOGIN = "daily_login";
    // 邀请用户上麦
    public static final String INVITE_USER_ON_MIC = "invite_user_on_mic";
    // 邀请用户上麦(all)
    public static final String INVITE_USER_ON_MIC_ALL = "invite_user_on_mic_all";
    // 邀请上麦动作
    public static final String INVITE_USER_ACTION = "invite_user_action";
    // 用户上麦(无限制)
    public static final String USER_UP_MIC = "user_up_mic";
    // 用户下麦
    public static final String USER_DOWN_MIC = "user_down_mic";
    // 关注用户
    public static final String FOLLOW_USER = "follow_user";
    // 发布动态
    public static final String POST_MOMENT = "post_moment";
    // 发送动态礼物
    public static final String SEND_MOMENT_GIFT = "send_moment_gift";
    // 观看视频时长
    public static final String WATCH_VIDEO_TIME = "watch_video_time";
    // 玩转盘游戏
    public static final String PLAY_WHEEL = "play_wheel";
    // 玩水果机游戏
    public static final String PLAY_FRUIT_MACHINE = "play_fruit_machine";
    // 玩Ludo游戏
    public static final String PLAY_LUDO = "play_ludo";
    // 玩UMO游戏
    public static final String PLAY_UMO = "play_umo";
    // 玩消消乐游戏
    public static final String PLAY_MONSTER_CRUSH = "play_monster_crush";
    // 玩多米诺游戏
    public static final String PLAY_DOMINO = "play_domino";
    // 玩克罗姆游戏
    public static final String PLAY_CARROM_POOL = "play_carrom_pool";
    // 玩Baloot游戏
    public static final String PLAY_BALOOT = "play_baloot";
    // 玩Jackaroo游戏
    public static final String PLAY_JACKAROO = "play_jackaroo";
    // 玩WOISSPY_GAME游戏
    public static final String PLAY_WOISSPY_GAME = "play_woisspy_game";
    // 玩城市寻宝
    public static final String PLAY_CITY_TREASURE = "play_city_treasure";
    // 玩猜拳游戏
    public static final String PLAY_FINGER_GUESS = "play_finger_guess";
    // 猜拳获胜
    public static final String WIN_FINGER_GUESS = "win_finger_guess";
    // 玩Ludo游戏获胜
    public static final String WIN_LUDO = "win_ludo";
    // 玩Umo游戏获胜
    public static final String WIN_UMO = "win_umo";
    // 玩消消乐游戏获胜
    public static final String WIN_MONSTER_CRUSH = "win_monster_crush";
    // 克罗姆游戏获胜
    public static final String WIN_CARROM_POOL = "win_carrom_pool";
    // Jackaroo游戏获胜
    public static final String WIN_JACKAROO = "win_jackaroo";
    // baloot游戏获胜
    public static final String WIN_BALOOT = "win_baloot";
    // 多米诺游戏
    public static final String WIN_DOMINO = "win_domino";
    // 幸运转盘
    public static final String WIN_LUCKY_WHEEL = "win_lucky_wheel";
    // 赢得WOISSPY_GAME游戏
    public static final String WIN_WOISSPY_GAME = "win_woisspy_game";
    // 充值钻石
    public static final String RECHARGE_DIAMONDS = "recharge_diamonds";
    // 房间加经验
    public static final String TEST_ADD_EXP_ITEM = "test_add_exp_item";
    //玩pk游戏
    public static final String PLAY_PK_GAME = "play_pk_game";
    // 抢红包
    public static final String GET_LUCKY_BAG = "get_lucky_bag";
    // 发送幸运数字
    public static final String SEND_LUCKY_NUMBER = "send_lucky_number";

    // 欢迎进房用户
    public static final String WELCOME_USER_IN_ROOM = "welcome_user_in_room";
    // 踢出房间
    public static final String KICK_OUT_ROOM = "kick_out_room";
    // 房间、私信聊天内容被用户举报
    public static final String USER_MSG_VIOLATION = "user_msg_violation";
    // 个人资料违规
    public static final String USER_PROFILE_VIOLATION = "user_profile_violation";
    // 动态发布、评论内容违规
    public static final String USER_MOMENT_VIOLATION = "user_moment_violation";
    // 房间资料内容违规（房间封面、名称、公告）
    public static final String ROOM_PROFILE_VIOLATION = "room_profile_violation";
    // 非法改装设备登录，设备使用作弊软件
    public static final String DEVICE_RISK_LOGIN = "device_risk_login";
    // py 发送设备关联账号被封禁
    public static final String DEVICE_ACCOUNT_BAN = "device_account_ban";
    // 分享家族
    public static final String SHARE_FAMILY = "share_family";
    // 发送私信礼物
    public static final String SEND_PRIVATE_GIFT = "send_private_gift";
    // 玩百顺游戏获胜
    public static final String WIN_BAI_SHUN_GAME = "win_bai_shun_game";
    // 玩水果机获得钻石
    public static final String WIN_FRUIT_GAME = "win_fruit_game";
    // 玩自研greedy
    public static final String PLAY_GREEDY_GAME = "play_greedy_game";
    // 玩自研greedy赢得钻石
    public static final String WIN_GREEDY_GAME = "win_greedy_game";
    // 玩自研嘉年华消费钻石
    public static final String PLAY_CARNIVAL_GAME = "play_carnival_game";
    // 玩自研嘉年华获得奖励
    public static final String PLAY_CARNIVAL_REWARD = "play_carnival_reward";
    // 玩自研crash
    public static final String PLAY_CRASH_GAME = "play_crash_game";
    // 订阅房间活动
    public static final String SUB_ROOM_EVENT = "sub_room_event";
    // 成功参与sud游戏
    public static final String PLAY_SUD_GAME = "play_sud_game";
    // 进入房间
    public static final String ENTER_ROOM = "enter_room";
    // 创建房间活动
    public static final String CREATE_ROOM_EVENT = "create_room_event";
    // 玩真心话转盘游戏
    public static final String PLAY_TRUTH_DARE_WHEEL = "play_truth_dare_wheel";
    // 玩真心话转盘游戏V2
    public static final String PLAY_TRUTH_DARE_V2_WHEEL = "play_truth_dare_v2";
    // 关注朋友圈话题
    public static final String FOLLOW_MOMENT_TOPIC = "follow_moment_topic";
    // 成为朋友圈话题管理员
    public static final String BECOME_MOMENT_TOPIC_ADMIN = "become_moment_topic_admin";
    // 创建投票
    public static final String CREATE_VOTE = "create_vote";
    // 玩投票
    public static final String PLAY_VOTE = "play_vote";
    // 私信互动
    public static final String REPLAY_PRIVATE_MSG = "replay_private_msg";
    // 成为房间管理员
    public static final String BECOME_ROOM_ADMIN = "become_room_admin";

    // 资料卡更改国家
    public static final String PERSONAL_UPDATE_COUNTRY = "personal_update_country";

    // 发送私信
    public static final String SEND_PRIVATE_MSG = "send_private_msg";

    // 邀请裂变活动完成绑定用户
    public static final String INVITE_BIND_USER = "invite_bind_user";
}
