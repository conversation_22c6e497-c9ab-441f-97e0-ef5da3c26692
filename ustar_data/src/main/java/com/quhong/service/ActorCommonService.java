package com.quhong.service;

import com.quhong.data.ActorData;
import com.quhong.data.vo.ActorRoomStatusVO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorConfigDao;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.BlackListDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Lazy
@Component
public class ActorCommonService {

    private static final Logger logger = LoggerFactory.getLogger(ActorCommonService.class);

    @Resource
    private ActorDao actorDao;
    @Resource
    private BlackListDao blackListDao;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private ActorConfigDao actorConfigDao;

    /**
     * uid查看aid在房间状态
     * @return vo
     */
    public ActorRoomStatusVO getActorRoomStatus(String uid, String aid){
        ActorData actorDataRds = actorDao.getActorData(aid);
        return getActorRoomStatus(uid, aid, actorDataRds.getAccept_talk());
    }

    public ActorRoomStatusVO getActorRoomStatus(String uid, String aid, int acceptTalk){
        try {
            ActorRoomStatusVO vo = new ActorRoomStatusVO();
            if(acceptTalk != 1 || blackListDao.isBlock(aid, uid) || roomBlacklistDao.isBlock(RoomUtils.formatRoomId(aid), uid)){
                return null;
            }
            String roomId = roomPlayerRedis.getActorRoomStatus(aid);
            if(StringUtils.isEmpty(roomId) || RoomUtils.isGameRoom(roomId)){
                return null;
            }
            if (whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                return null;
            }

            MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(roomId);
            if (mongoRoomData == null) {
                return null;
            }
            vo.setInRoomId(roomId);
            vo.setInRoomName(mongoRoomData.getName());
            vo.setInRoomHead(ImageUrlGenerator.generateRoomUserUrl(mongoRoomData.getHead()));
            return vo;
        }catch (Exception e){
            logger.error("getActorRoomStatus error {}", e.getMessage(), e);
        }
        return null;
    }

    public int getRejectGreetStatus(String uid){
        int defaultStatus = ActorUtils.isNewRegisterActor(uid, 30) ? 0 : 1;
        return (int) actorConfigDao.getLongUserConfig(uid, ActorConfigDao.REJECT_GREET, defaultStatus);
    }
}
