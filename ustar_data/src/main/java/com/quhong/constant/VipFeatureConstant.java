package com.quhong.constant;


import java.util.HashMap;
import java.util.Map;

public class VipFeatureConstant {

    /**
     * vip来源
     */
    public static final int SOURCE_BUY_VIP = 0;
    public static final int SOURCE_ADMIN_GIVE_VIP = 1;
    public static final int SOURCE_VIP_CARD = 2;     // Vip卡激活, 不下发钻石及礼物
    public static final int SOURCE_UPGRADE_APP = 3;     // 升级app版本下发

    /**
     * vip记录类型
     */
    public static final int RECORD_BUY_VIP = 0;        // 购买vip
    public static final int RECORD_VIP_CARD  = 1;      // vipCard激活
    public static final int RECORD_VIP_EXPIRE = 2;     // 到期结束
    public static final int RECORD_VIP_REMOVE = 3;     // admin移除vip

    /**
     * 资源描述
     */
    public static final String DESCRIPTION_BUY_VIP = "Buy Vip";                      // 购买vip
    public static final String DESCRIPTION_ACTIVATE_VIP_CARD = "Activate Vip Card";  // 激活vip卡
    public static final String DESCRIPTION_UPGRADE_VIP = "Upgrade App Version";          // vip升级app版本
    public static final String DESCRIPTION_CHANGE_VIP = "Modify Gender Change Vip";  // 性别变更导致vip等级变化
    public static final String DESCRIPTION_EXPIRE_VIP = "Vip Expire";                // vip过期
    public static final String DESCRIPTION_REMOVE_VIP = "Remove Vip";                // 移除vip

    /**
     * vip等级
     */
    public static final Integer VIP_LEVEL_1 = 1;
    public static final Integer VIP_LEVEL_2 = 2;
    public static final Integer VIP_LEVEL_3 = 3;
    public static final Integer VIP_LEVEL_4 = 4;
    public static final Integer VIP_LEVEL_5 = 5;
    public static final Integer VIP_LEVEL_6 = 6;
    public static final Integer VIP_LEVEL_QUEEN = 10;
    /**
     * 特性类型:
     * 0: 勋章
     * 1: 资料卡
     * 2: 排名靠前
     * 3: 进场通知
     * 4: 专属聊天气泡
     * 5: 麦位框
     * 6: 贵族专属礼物
     * 7: 专属房间背景
     * 8: 坐骑
     * 9: 直播房
     * 10: 房间主题
     * 11: 锁房间
     * 12: 激活广播
     * 13: 修改国家
     * 14: 防踢
     * 15: 防禁言
     * 16: 上传房间背景
     * 17: 奖励钻石
     * 18: 发送图片
     * 19: 更多麦位
     * 20: 副房主
     * 21: 隐藏访客身份
     * 22: 飘屏装扮
     * 23: 专属表情
     */
    public static final int FEATURE_MEDAL = 0;
    public static final int FEATURE_PROFILE_CARD = 1;
    public static final int FEATURE_TOP_RANKING = 2;
    public static final int FEATURE_ENTRY_NOTIFICATION = 3;
    public static final int FEATURE_EXCLUSIVE_CHAT_BUBBLE = 4;
    public static final int FEATURE_MIC_FRAME = 5;
    public static final int FEATURE_NOBLE_EXCLUSIVE_GIFT = 6;
    public static final int FEATURE_EXCLUSIVE_ROOM_BACKGROUND = 7;
    public static final int FEATURE_MOUNT = 8;
    public static final int FEATURE_LIVE_ROOM = 9;
    public static final int FEATURE_ROOM_THEME = 10;
    public static final int FEATURE_LOCK_ROOM = 11;
    public static final int FEATURE_ACTIVATE_BROADCAST = 12;
    public static final int FEATURE_MODIFY_COUNTRY = 13;
    public static final int FEATURE_ANTI_KICK = 14;
    public static final int FEATURE_ANTI_MUTE = 15;
    public static final int FEATURE_UPLOAD_ROOM_BACKGROUND = 16;
    public static final int FEATURE_REWARD_DIAMONDS = 17;
    public static final int FEATURE_SEND_IMAGES = 18;
    public static final int FEATURE_MORE_MIC_SEATS = 19;
    public static final int FEATURE_DEPUTY_ROOM_OWNER = 20;
    public static final int FEATURE_HIDE_VISITOR_IDENTITY = 21;
    public static final int FEATURE_FLOATING_SCREEN_DECORATION = 22;
    public static final int FEATURE_EXCLUSIVE_EMOJI = 23;

    public static final Map<Integer, String> VIP_ICON_MAP = new HashMap<>();
    public static final Map<Integer, String> VIP_NAME_MAP = new HashMap<>();


    static {
        VIP_ICON_MAP.put(1, "https://cdn3.qmovies.tv/youstar/op_1748227058_VIP1.webp");
        VIP_ICON_MAP.put(2, "https://cdn3.qmovies.tv/youstar/op_1748227058_VIP2.webp");
        VIP_ICON_MAP.put(3, "https://cdn3.qmovies.tv/youstar/op_1748227058_VIP3.webp");
        VIP_ICON_MAP.put(4, "https://cdn3.qmovies.tv/youstar/op_1748227058_VIP4.webp");
        VIP_ICON_MAP.put(5, "https://cloudcdn.qmovies.tv/vipResource/op_1749029218_VIP5.webp");
        VIP_ICON_MAP.put(6, "https://cdn3.qmovies.tv/youstar/op_1748227059_VIP6.webp");
        VIP_ICON_MAP.put(10, "https://cdn3.qmovies.tv/youstar/op_1748227058_queen.webp");

        VIP_NAME_MAP.put(1, "VIP1");
        VIP_NAME_MAP.put(2, "VIP2");
        VIP_NAME_MAP.put(3, "VIP3");
        VIP_NAME_MAP.put(4, "VIP4");
        VIP_NAME_MAP.put(5, "Prince");
        VIP_NAME_MAP.put(6, "King");
        VIP_NAME_MAP.put(10, "Queen");
    }



}
