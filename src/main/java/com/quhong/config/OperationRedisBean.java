package com.quhong.config;

import com.quhong.redis.BaseRedisBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

@Configuration
public class OperationRedisBean extends BaseRedisBean {
    public static final String OPERATION_TOKEN = "operation_token"; // redis1_6384 db4
    public static final String MANAGE_ROLE = "manage_role_bean"; // redis1_6384 db4


    @Bean(name = OPERATION_TOKEN)
    public StringRedisTemplate getOperationToken() {
        return createBean("operation_token", null);
    }

    @Bean(name = MANAGE_ROLE)
    public StringRedisTemplate getManageRoleBean() {
        return createBean("manage_role", null);
    }
}
