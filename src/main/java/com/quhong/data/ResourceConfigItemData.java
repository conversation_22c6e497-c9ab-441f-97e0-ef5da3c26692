package com.quhong.data;


public class ResourceConfigItemData {
    private String name; // 资源名称
    private String nameAr; // 阿语资源名称
    private String desc; // 资源描述
    private String descAr; // 资源描述
    private String type; // 资源类型 room_medal,info_card,room_text_frame,voice_wave,room_theme
    private int level; // 最小需要等级
    private String icon; // 资源url
    private String link; // 弹窗资源url
    private String link_bg; // type为4时，背景url

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getDescAr() {
        return descAr;
    }

    public void setDescAr(String descAr) {
        this.descAr = descAr;
    }

    public String getLink_bg() {
        return link_bg;
    }

    public void setLink_bg(String link_bg) {
        this.link_bg = link_bg;
    }
}
