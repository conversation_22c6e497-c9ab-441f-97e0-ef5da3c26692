package com.quhong.constant;

import com.quhong.enums.HttpCode;

public class VipHttpCode extends HttpCode {
    public static final HttpCode NOT_ENOUGH_MONEY = new HttpCode(50, "Not enough money, please charge and try again");
    public static final HttpCode NOT_BUY_VIP = new HttpCode(61, "you can not buy this vip, only for queen");
    public static final HttpCode NOT_BUY_LOWER_VIP = new HttpCode(62, "you can not buy lower level vip");
    public static final HttpCode VIP_CARD_ALREADY_ACTIVATED = new HttpCode(63, "VIP card already activated or expired");
    public static final HttpCode VIP_CARD_EXPIRED = new HttpCode(64, "VIP card has expired");
    public static final HttpCode VIP_DOWN_GRADE = new HttpCode(1001, "vip_down_grade");
    public static final HttpCode USER_NOT_EXIST = new HttpCode(1002, "user_not_exist");
    public static final HttpCode NOT_FIND_VIP_CONFIG = new HttpCode(1003, "not_find_vip_config");
    public static final HttpCode FEMALE_NOT_BUY_VIP = new HttpCode(1004, "female_not_buy_vip");
    public static final HttpCode MALE_NOT_BUY_VIP = new HttpCode(1005, "male_not_buy_vip");
    public static final HttpCode UPDATE_APP_VIP = new HttpCode(1006, "For better experience, please update YouStar in the store.", "من أجل الحصول على تجربة أفضل ، يرجى تحديث YouStar في المتجر");

}
