package com.quhong.room.processors;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.RoomMsgNewEvent;
import com.quhong.cache.CacheMap;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.UserLevelTaskData;
import com.quhong.enums.*;
import com.quhong.mongo.dao.UserMonitorDao;
import com.quhong.msg.ErrorNotificationPushMsg;
import com.quhong.msg.MarsCacheMsg;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.MsgInfoObject;
import com.quhong.msg.room.SendRoomMsg;
import com.quhong.msg.room.SendRoomPushMsg;
import com.quhong.mysql.dao.RoomMessageDao;
import com.quhong.mysql.data.RoomMessageData;
import com.quhong.net.cache.MsgCache;
import com.quhong.net.sender.PlayerMsgSender;
import com.quhong.net.sender.RoomMsgSender;
import com.quhong.player.cache.Player;
import com.quhong.player.cache.PlayerCacheMap;
import com.quhong.redis.BlockRedis;
import com.quhong.redis.DataRedisBean;
import com.quhong.room.data.RoomActorData;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.room.rooms.Room;
import com.quhong.task.TaskFactory;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
public class RoomMsgProcessor extends AbstractRoomProcessor {
    private static final Logger logger = LoggerFactory.getLogger(RoomMsgProcessor.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);
    private String expireDate = "";

    private static final int TEXT_LIMIT = 500;

    private static final int NONE = 0;
    private static final int TEXT_MSG = 1;
    private static final int REPEAT = 2;

    @Autowired
    private RoomMsgSender roomMsgSender;
    @Autowired
    private PlayerMsgSender playerMsgSender;
    @Autowired
    private RoomMessageDao roomMessageDao;
    @Autowired
    private PlayerCacheMap playerCacheMap;
    @Autowired
    private UserMonitorDao userMonitorDao;
    @Autowired
    private EnterRoomProcessor enterRoomProcessor;
    @Autowired
    private DailyTaskService dailyTaskService;
    @Autowired
    private MicFrameRedis micFrameRedis;
    @Autowired
    private RoomTextContent roomTextContent;
    @Autowired
    private UserLevelTaskService userLevelTaskService;
    @Resource
    private BlockRedis blockRedis;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private WrapMsgSender wrapMsgSender;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    private CacheMap<String, Integer> forbiddenCacheMap;


    public RoomMsgProcessor() {
        forbiddenCacheMap = new CacheMap<>(5 * 60 * 1000);
    }

    @PostConstruct
    public void postInit() {
        forbiddenCacheMap.start();
    }

    public boolean isForbidden(String roomId, String uid) {
        String key = getForbidden(roomId, uid);
        Integer forbiddenTime = forbiddenCacheMap.getData(key);
        if (forbiddenTime == null) {
            forbiddenTime = micFrameRedis.getForbidTime(roomId, uid);
            forbiddenCacheMap.cacheData(key, forbiddenTime);
        }
        return forbiddenTime > DateHelper.getNowSeconds();
    }

    public void removeForbiddenCache(String roomId, String uid) {
        forbiddenCacheMap.remove(getForbidden(roomId, uid));
    }

    private String getForbidden(String roomId, String uid) {
        return roomId + "_" + uid;
    }

    public void sendTextMsgToAll(Room room, SendRoomMsg msg, Integer version) {
        RoomActorData roomActorData = room.getActorMap().get(msg.getFromUid());
        if (roomActorData == null) {
            enterRoomProcessor.enterRoomFromMsg(room, msg.getFromUid(), msg.getCmd());
            roomActorData = room.getActorMap().get(msg.getFromUid());
            if (roomActorData == null) {
                logger.error("send text msg error. roomActorData is null. roomId={} fromUid={}", room.getRoomId(), msg.getFromUid());
                return;
            }
        }
        final RoomActorData paramRoomActorData = roomActorData;
        roomActorData.addTask(new Task() {
            @Override
            protected void execute() {
                checkAndSendTextMsg(room, paramRoomActorData, msg, version);
            }
        });
    }

    private void checkAndSendTextMsg(Room room, RoomActorData roomActorData, SendRoomMsg msg, Integer version) {
        // 检查是否被冻结
        if (checkFreeze(room, roomActorData)) {
            sendTextError(room, roomActorData, msg, false);
            return;
        }
        // 检查是否禁言
        if (isForbidden(room.getRoomId(), roomActorData.getUid())) {
            logger.error("user has been forbidden. roomId={} uid={}", room.getRoomId(), roomActorData.getUid());
            sendTextError(room, roomActorData, msg, true);
            return;
        }
        if (checkBlock(roomActorData)) {
            logger.error("user has been blocked. roomId={} uid={}", room.getRoomId(), roomActorData.getUid());
            return;
        }
//        // 检查脏词
//        TextDetectData detectData = textDetection.detect(msg.getContent(), "SA");
//        if(detectData != null && detectData.isIs_hit()){
//            logger.info("send msg contain dirty text. can not send text msg. content={} roomId={} uid={}", msg.getContent(), room.getRoomId(), roomActorData.getUid());
//            return;
//        }
        doSendTextMsgToAll(room, msg, version);
    }

    private void sendTextError(Room room, RoomActorData roomActorData, SendRoomMsg msg, boolean forbidden) {
        ErrorNotificationPushMsg pushMsg = new ErrorNotificationPushMsg();
        pushMsg.getProtoHeader().setRoomID(room.getRoomId());
        pushMsg.setReason(ErrorPushEnum.ERROR_REASON_ROOM_MSG_FREEZE);
        pushMsg.setShow_type(ErrorPushEnum.SHOW_TYPE_PROMPT);
        if (msg.getSlang() == SLangType.ARABIC) {
            pushMsg.setContent(forbidden ? "مالك الغرفة من إرسال الرسائل النصية." : "\uD83E\uDD76فشل في الارسال، حسابك مجمد.");
        } else {
            pushMsg.setContent(forbidden ? "The room administrator has banned you" : "Failed to send, your account has been frozen.\uD83E\uDD76");
        }
        logger.info("sendTextError msg to client. roomId={} uid={} forbidden={}", room.getRoomId(), roomActorData.getUid(), false);
        playerMsgSender.sendMsg(roomActorData.getUid(), pushMsg);
    }

    private boolean checkFreeze(Room room, RoomActorData roomActorData) {
        if (roomActorData.getFreeze() == null) {
            roomActorData.setFreeze(userMonitorDao.isFreezeInRoom(roomActorData.getUid()));
        }
        if (Boolean.TRUE.equals(roomActorData.getFreeze())) {
            return true;
        }
        return false;
    }

    private void doSendTextMsgToAll(Room room, SendRoomMsg msg, Integer version) {
        if (msg.getMsgType() == MsgType.TEXT && msg.getContent().length() > TEXT_LIMIT) {
            logger.info("msg content length great then {}. do not send. uid={} roomId={}", TEXT_LIMIT, msg.getUname(), room.getRoomId());
            return;
        }
        String fromUid = msg.getUid();
        // 任务记录
        dailyTaskService.sendRoomText(fromUid);
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(fromUid, room.getRoomId(), "", "", CommonMqTaskConstant.SEND_ROOM_MSG, 1));
        // 公屏发言5次+5，每日上限+10。每发送5条消息增加5经验值，包含emoji表情消息、文字消息、图片消息。
        userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(fromUid, UserLevelConstant.SEND_ROOM_MSG));
        MsgInfoObject infoObject = new MsgInfoObject();
        infoObject.setFromUid(fromUid);
        infoObject.setContent(msg.getContent());
        infoObject.setMsgType(msg.getMsgType());
        infoObject.setMsgCategory(msg.getMsgCategory());
        infoObject.setUname(msg.getUname());
        infoObject.setIsRepeatMsg(msg.getIsRepeatMsg());
        roomTextContent.fillTextMsg(room.getRoomId(), fromUid, infoObject);

        SendRoomPushMsg pushMsg = new SendRoomPushMsg();
        pushMsg.add(infoObject);
        logger.info("send push text msg to room. fromUid={} msgType={} content={} msgCategory={} roomId={} isRepeatMsg={}", fromUid, infoObject.getMsgType(), infoObject.getContent(), infoObject.getMsgCategory(), room.getRoomId(), infoObject.getIsRepeatMsg());

        int msgType = (msg.getMsgType() == MsgType.TEXT || msg.getMsgType() == MsgType.AT_MSG) ? TEXT_MSG : msg.getMsgType();
        sendToAll(room, fromUid, pushMsg, msgType, false, version);
    }

    /**
     * 发送消息给房间内所有人,不包含自己
     *
     * @param room    room
     * @param fromUid 发送者
     * @param msg     msg
     * @param version 版本号
     */
    public void sendMsgToAll(Room room, String fromUid, MarsServerMsg msg, Integer version) {
        sendToAll(room, fromUid, msg, NONE, false, version);
    }

    /**
     * 发送消息给房间内所有人
     *
     * @param room      room
     * @param fromUid   发送者
     * @param msg       消息
     * @param containMe 是否包含发送者
     * @param version   版本号
     */
    public void sendMsgToAll(Room room, String fromUid, MarsServerMsg msg, boolean containMe, Integer version) {
        sendToAll(room, fromUid, msg, NONE, containMe, version);
    }

    public void sendRepeatedMsgToAll(Room room, String fromUid, MarsServerMsg msg, Integer version) {
        sendToAll(room, fromUid, msg, REPEAT, false, version);
    }

    public void sendRepeatedMsgToAll(Room room, String fromUid, MarsServerMsg msg, boolean containMe, Integer version) {
        sendToAll(room, fromUid, msg, REPEAT, containMe, version);
    }

    private void sendToAll(Room room, String fromUid, MarsServerMsg msg, int sendType, boolean containMe, Integer version) {
        msg.getProtoHeader().setRoomID(room.getRoomId());
        msg.getProtoHeader().setFromUid(fromUid);
        room.getMsgTaskQueue(fromUid).add(new Task() {
            @Override
            protected void execute() {
                if (sendType == TEXT_MSG) {
                    doSendTextMsg(room, fromUid, msg);
                } else {
                    doSendMsg(room, fromUid, msg, sendType, containMe, version);
                }
            }
        });
    }

    public void sendLudoMsg(Room room, MarsCacheMsg msg, Set<String> uidSet) {
        room.getMsgTaskQueue(null).add(new Task() {
            @Override
            protected void execute() {
                msg.setRoomId(room.getRoomId());
                msg.getProtoHeader().setResponseAck(true);
                if (!room.getActorMap().containsKey(msg.getFromUid())) {
                    uidSet.remove(msg.getFromUid());
                }
                logger.info("send ludo msg to room. room.actors.size={} uidSetSize={} cmd={} msgId={} roomId={}", room.getActorMap().size(), uidSet.size(), msg.getCmd(), msg.getMsgId(), msg.getRoomId());
                wrapMsgSender.sendMsg(uidSet, msg, true);
            }
        });
    }

    public void sendRepeatedLimitMsgToAll(Room room, String fromUid, MarsServerMsg msg, boolean containMe) {
        sendLimitToAll(room, fromUid, msg, REPEAT, containMe);
    }

    private void sendLimitToAll(Room room, String fromUid, MarsServerMsg msg, int sendType, boolean containMe) {
        msg.getProtoHeader().setRoomID(room.getRoomId());
        msg.getProtoHeader().setFromUid(fromUid);
        room.getMsgTaskQueue(fromUid).add(new Task() {
            @Override
            protected void execute() {
                doSendLimitMsg(room, fromUid, msg, sendType, containMe);
            }
        });
    }

    /**
     * 发送房间消息
     *
     * @param version 大于0为版本控制，-1只发送给语聊房间，-2只发送给游戏房间
     */
    protected void doSendMsg(Room room, String fromUid, MarsServerMsg msg, int sendType, boolean containMe, Integer version) {
        if (null != version) {
            if (-1 == version && !RoomUtils.isVoiceRoom(room.getRoomId())) {
                return;
            }
            if (-2 == version && !RoomUtils.isGameRoom(room.getRoomId())) {
                return;
            }
        }
        // 是否重复发消息
        boolean repeated = sendType == REPEAT;
        msg.setRoomId(room.getRoomId());
        msg.getProtoHeader().setResponseAck(repeated);
        byte[] body = msg.toBody();
        msgLogger.info("push msg to room. actors={} cmd={} msgId={} fromUid={} roomId={}",
                room.getActorMap().size(), msg.getCmd(), msg.getMsgId(), msg.getFromUid(), msg.getRoomId());
        Set<String> uidSet = new HashSet<>();
        MarsCacheMsg cacheMsg = MsgCache.createCacheMsg(fromUid, msg, body, repeated);
        for (RoomActorData actorData : room.getActorMap().values()) {
            if ((!containMe) && actorData.getUid().equals(fromUid)) {
                continue;
            }
            if (versionCheck(version, actorData.getActorData(), msg.getCmd())) {
                continue;
            }
            uidSet.add(actorData.getUid());
        }
        wrapMsgSender.sendMsg(uidSet, cacheMsg, repeated);
    }

    private boolean versionCheck(Integer version, ActorData actorData, int cmd) {
        if (null == version || null == actorData || 0 == actorData.getVersion_code()) {
            return false;
        }
        if (version < 0) {
            return false;
        }
        if (!AppVersionUtils.versionCheck(version, actorData.getVersion_code())) {
            logger.info("skip send msg, cmd={} uid={} version={} actorVersion={}", cmd, actorData.getUid(), version, actorData.getVersion_code());
            return true;
        }
        return false;
    }

    protected void doSendLimitMsg(Room room, String fromUid, MarsServerMsg msg, int sendType, boolean containMe) {
        // 是否重复发消息
        boolean repeated = sendType == REPEAT;
        msg.setRoomId(room.getRoomId());
        msg.getProtoHeader().setResponseAck(repeated);
        byte[] body = msg.toBody();
        logger.info("send limit push msg to room. room.actors.size={} cmd={} msgId={} fromUid={} roomId={}", room.getActorMap().size(), msg.getCmd(), msg.getMsgId(), msg.getFromUid(), msg.getRoomId());
        MarsCacheMsg cacheMsg = MsgCache.createCacheMsg(fromUid, msg, body, repeated);
        Set<String> uidSet = new HashSet<>();
        for (RoomActorData actorData : room.getActorMap().values()) {
            if ((!containMe) && actorData.getUid().equals(fromUid)) {
                continue;
            }
            uidSet.add(actorData.getUid());
        }
        wrapMsgSender.sendMsg(uidSet, cacheMsg, repeated);
    }

    protected void doSendTextMsg(Room room, String fromUid, MarsServerMsg msg) {
        msg.setRoomId(room.getRoomId());
        msg.getProtoHeader().setResponseAck(false);
        SendRoomPushMsg newMsg = (SendRoomPushMsg) msg;
        roomMsgSender.cacheMsg(null, newMsg);
        logger.info("send push text msg to room. cmd={} msgId={} fromUid={} roomId={}", msg.getCmd(), msg.getMsgId(), msg.getFromUid(), msg.getRoomId());
        // 房间重复消息非第一条也要发给发送者自己
        boolean notContainMe = newMsg.getMsgList().get(0).getIsRepeatMsg() == 0 || newMsg.getMsgList().get(0).getIsRepeatMsg() == 2;
        Set<String> uidSet = new HashSet<>();
        MarsCacheMsg cacheMsg = MsgCache.createCacheMsg(fromUid, msg, newMsg.toBody(), false);
        for (RoomActorData actorData : room.getActorMap().values()) {
            if (notContainMe && actorData.getUid().equals(fromUid)) {
                continue;
            }
            uidSet.add(actorData.getUid());
        }
        wrapMsgSender.sendMsg(uidSet, cacheMsg, false);
        final int paramSendCount = uidSet.size();
        TaskFactory.getFactory().addDb(new Task() {
            @Override
            protected void execute() {
                Player fromPlayer = null;
                if (!StringUtils.isEmpty(fromUid)) {
                    fromPlayer = playerCacheMap.getPlayer(fromUid);
                }
                // 写入db
                saveToDB(msg, room.getRoomId(), fromPlayer, paramSendCount, paramSendCount);
            }
        });
    }

    private void saveToDB(MarsServerMsg msg, String roomId, Player fromPlayer, int sendCount, int userCount) {
        try {
            RoomMessageData data = new RoomMessageData();
            if (msg instanceof SendRoomPushMsg) {
                SendRoomPushMsg sMsg = (SendRoomPushMsg) msg;
                if (sMsg.getMsgList().size() == 0) {
                    return;
                }
                MsgInfoObject infoObject = sMsg.getMsgList().get(0);
                data.setMsgCategory(infoObject.getMsgCategory());
                data.setMsgType(infoObject.getMsgType());
                data.setMsgBody(infoObject.getContent());
            }
            data.setRoomId(roomId);
            data.setMsgId(msg.getMsgId());
            data.setSendUserCount(sendCount);
            data.setUserCount(userCount);
            data.setCtime(DateHelper.getNowSeconds());
            if (fromPlayer != null) {
                data.setFromUid(fromPlayer.getUid());
                data.setFromOs(fromPlayer.getOs());
            } else {
                data.setFromUid("");
                data.setFromOs(ClientOS.NONE);
            }
            roomMessageDao.insert(data);
            roomMsgNewEvent(data);
        } catch (Exception e) {
            logger.error("room msg save to db error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    /**
     * 房间消息事件埋点，每个用户每个房间每天只上报一次
     */
    private void roomMsgNewEvent(RoomMessageData data) {
        if (ObjectUtils.isEmpty(data.getFromUid()) || ObjectUtils.isEmpty(data.getRoomId())) {
            return;
        }
        String nowDay = DateHelper.ARABIAN.formatDateInDay();
        String key = String.format("set:roomMsg:%s", nowDay);
        String value = data.getRoomId() + "-" + data.getFromUid();
        if (Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, value))) {
            return;
        }
        redisTemplate.opsForSet().add(key, value);
        if (!nowDay.equals(expireDate)) {
            redisTemplate.expire(key, 2, TimeUnit.DAYS);
            expireDate = nowDay;
        }
        RoomMsgNewEvent event = new RoomMsgNewEvent();
        event.setUid(data.getFromUid());
        event.setRoom_id(data.getRoomId());
        event.setMsg_type(data.getMsgType());
        event.setSend_user_count(data.getSendUserCount());
        event.setRoom_user_count(data.getUserCount());
        event.setMsg_id(String.valueOf(data.getMsgId()));
        event.setFrom_os(data.getFromOs());
        event.setFrom_uid(data.getFromUid());
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    /**
     * 是否被禁止
     *
     * @param actorData
     * @return
     */
    private boolean checkBlock(RoomActorData actorData) {
        String tn_id = actorData.getActorData().getTn_id();
        String blockTime = blockRedis.checkBlock(tn_id, BlockTnConstant.BLOCK_ROOM_FILE);
        return StringUtils.hasLength(blockTime);
    }
}
