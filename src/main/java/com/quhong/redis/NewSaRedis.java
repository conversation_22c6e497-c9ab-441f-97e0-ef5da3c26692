package com.quhong.redis;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class NewSaRedis extends AbstractFeaturedRedis {

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    @Override
    public StringRedisTemplate getRedisTemplate() {
        return redisTemplate;
    }

    @Override
    public String getRedisListKey() {
        return "list:newSaMoment";
    }
}
