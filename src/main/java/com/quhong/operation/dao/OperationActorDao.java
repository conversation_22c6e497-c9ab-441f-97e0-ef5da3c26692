package com.quhong.operation.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.ServerType;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.constant.AppPackageConstant;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mongobean.ActorStat;
import com.quhong.operation.share.mongobean.BeautifulRidChangeLogOP;
import com.quhong.operation.utils.MongoUtils;
import com.quhong.operation.utils.StringUtil;
import com.quhong.redis.PartyGirlRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/19
 */
@Component
public class OperationActorDao {

    private final static Logger logger = LoggerFactory.getLogger(OperationActorDao.class);

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemp;
    @Resource
    private PartyGirlRedis partyGirlRedis;
    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    /**
     * 通过多渠道获取actor信息
     *
     * @param rid actor的rid，也可能是靓号，或者过期的靓号
     * @return 用户信息
     */
    public ApiResult<Actor> getActorByRidOrBeforeRid(Integer rid) {
        ApiResult<Actor> result = new ApiResult<>();
        if (null == rid || 1 > rid) {
            return result.error("rid is invalid");
        }
        ApiResult<Actor> apiResult = getActorByRid(rid);
        Actor actor = apiResult.getData();
        // 如果不存在，则尝试获取其他
        if (null == actor) {
            logger.info("rid={} not exist actor", rid);
            // 从靓号记录中获取
            Query query = new Query(Criteria.where("before_rid").is(rid));
            BeautifulRidChangeLogOP ridLog = mongoTemp.findOne(query, BeautifulRidChangeLogOP.class);
            if (null != ridLog && null != ridLog.getUid()) {
                query = new Query(Criteria.where("_id").is(ridLog.getUid()));
                actor = getOneActorByQuery(query);
            } else {
                logger.info("rid={} isn't beautiful rid", rid);
                return result.error("rid=" + rid + " not exist");
            }
        }
//        logger.info("rid={} result actor:{}", rid, actor);
        return result.ok(actor);
    }

    /**
     * 通过rid直接获取用户信息
     *
     * @param rid rid
     * @return actor
     */
    public ApiResult<Actor> getActorByRid(Integer rid) {
        ApiResult<Actor> result = new ApiResult<>();
        if (null == rid || 1 > rid) {
            return result.error("rid is invalid");
        }
        Query query = new Query(Criteria.where("rid").is(rid));
        Actor actor = getOneActorByQuery(query);
        return result.ok(actor);
    }

    public ApiResult<Actor> getUidByStrRid(String rid) {
        ApiResult<Actor> result = new ApiResult<>();
        if (StringUtils.isEmpty(result)) {
            return result.error("rid is invalid");
        }
        Criteria criteria = new Criteria();
        if (com.alibaba.druid.util.StringUtils.isNumber(rid)) {
            int intRid = 0;
            try {
                intRid = Integer.parseInt(rid);
            } catch (NumberFormatException ignore) { }
            criteria.orOperator(Criteria.where("alpha_rid").is(rid), Criteria.where("rid").is(intRid), Criteria.where("originalRid").is(intRid));
        } else {
            criteria = Criteria.where("alpha_rid").is(rid);
        }
        Actor actor = getOneActorByQuery(new Query(criteria));
        return result.ok(actor);
    }

    /**
     * 通过rid直接获取用户信息
     *
     * @param rid rid
     * @return uid
     */
    public ApiResult<Actor> getUidByRid(Integer rid) {
        ApiResult<Actor> result = new ApiResult<>();
        if (null == rid || 1 > rid) {
            logger.info("rid is invalid");
            return result.error("rid is invalid");
        }
        Query query = new Query(Criteria.where("rid").is(rid));
        query.fields().include("_id");
        query.fields().include("beans");
        Actor actor = getOneActorByQuery(query);
        logger.info(query.toString());
        if (null == actor || null == actor.get_id()) {
            logger.info("actor is null");
            return result.error("rid is invalid");
        }
        return result.ok(actor);
    }

    /**
     * 通过uid获取用户信息
     *
     * @param uid uid
     * @return actor
     */
    public ApiResult<Actor> getActorByUid(String uid) {
        ApiResult<Actor> result = new ApiResult<>();
        if (StringUtil.isEmptyOrBlank(uid)) {
            return result.error("uid can not be empty");
        }
        return result.ok(getActor(uid));
    }

    public Actor getActor(String uid) {
        return getOneActorByQuery(new Query(Criteria.where("_id").is(uid)));
    }

    /**
     * 过滤用户
     *
     * @param gender 男1、女2、PartyGirl3、-1全部
     * @param app    app包名
     * @param os     1表示ios,0表示安卓 不传则查全部
     */
    public Set<String> filterActors(Set<String> uidSet, Integer gender, String app, int os, List<Integer> versionCodeList) {
        if (null != gender && (gender == 2 || gender == 3)) {
            Set<String> allPartyGirl = partyGirlRedis.getAllPartyGirl();
            if (gender == 3) {
                uidSet = allPartyGirl;
            } else {
                uidSet.removeAll(allPartyGirl);
            }
        }
        Criteria criteria = Criteria.where("_id").in(uidSet);
        Query query = new Query(criteria);
        query.fields().include("_id");
        if (!StringUtils.isEmpty(app)) {
            if (!app.contains(",")) {
                query.addCriteria(Criteria.where("app_package_name").is(app));
            } else {
                query.addCriteria(Criteria.where("app_package_name").in(app.split(",")));
            }
        }
        if (null != gender && -1 != gender && 3 != gender) {
            query.addCriteria(Criteria.where("fb_gender").is(gender));
        }
        if (!CollectionUtils.isEmpty(versionCodeList)) {
            if (versionCodeList.size() == 1) {
                query.addCriteria(Criteria.where("version_code").is(versionCodeList.get(0)));
            } else {
                query.addCriteria(Criteria.where("version_code").in(versionCodeList));
            }
        }
        if (1 == os) {
            query.addCriteria(Criteria.where("os").is(String.valueOf(1)));
        } else if (0 == os) {
            query.addCriteria(Criteria.where("os").ne(String.valueOf(1)));
        }
        List<ActorStat> actors = mongoTemp.find(query, ActorStat.class);
        logger.info("filter actors gender={} app={} version_code={} before={} after={}", gender, app, versionCodeList, uidSet.size(), actors.size());
        return actors.stream().map(actor -> actor.get_id().toString()).collect(Collectors.toSet());
    }

    public Set<String> getActors(int start, int end, String channel, String app, int os, int gender) {
        Criteria criteria = MongoUtils.setCriteriaBy_idRange(start, end);
        Query query = new Query(criteria);
        query.fields().include("_id");
        if (!StringUtils.isEmpty(app)) {
            if (!app.contains(",")) {
                query.addCriteria(Criteria.where("app_package_name").is(app));
            } else {
                query.addCriteria(Criteria.where("app_package_name").in(app.split(",")));
            }
        }
        if (-1 != gender && 3 != gender) {
            query.addCriteria(Criteria.where("fb_gender").is(gender));
        }
        if (1 == os) {
            query.addCriteria(Criteria.where("os").is(String.valueOf(1)));
        } else if (0 == os) {
            query.addCriteria(Criteria.where("os").ne(String.valueOf(1)));
        }
        if (!StringUtils.isEmpty(channel)) {
            query.addCriteria(Criteria.where("channel").is(channel));
        }
        List<ActorStat> actors = mongoTemp.find(query, ActorStat.class);
        return actors.stream().map(actor -> actor.get_id().toString()).collect(Collectors.toSet());
    }

    /**
     * 获取唯一设备的用户数量
     */
    public int distinctActorByTnId(Set<String> uidSet) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return 0;
        }
        Criteria criteria = Criteria.where("_id").in(uidSet);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("tn_id").count().as("count"),
                Aggregation.project().andExclude("_id")
        );
        List<Map> list = mongoTemp.aggregate(aggregation, "actor", Map.class).getMappedResults();
        if (list.size() == 0) {
            return 0;
        }
        int count = list.size();
        logger.info("distinctActorByTnId before={} after={}", uidSet.size(), count);
        return count;
    }

    /**
     * 统计一段时间内新增的用户
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param os        1表示ios,0表示安卓 不传则查全部
     * @return 某段时间内新增的用户
     */
    public ApiResult<Integer> totalNewActor(Integer startTime, Integer endTime, String os) {
        logger.info("in totalNewActor");
        ApiResult<Integer> result = new ApiResult<>();
        Query query = MongoUtils.setQueryBy_idRange(startTime, endTime);
        if (null == query) query = new Query();
        if ("1".equals(os)) {
            query.addCriteria(Criteria.where("os").is("1"));
        } else if ("0".equals(os)) {
            query.addCriteria(Criteria.where("os").ne("1"));
        }
        List<Actor> list = getActorByQuery(query);
        logger.info("startTime={} to endTime={} added actor {} ", startTime, endTime, list.size());
        return result.ok(list.size());
    }

    /**
     * 统计一段时间内新增的用户
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param os        1 ios，0 安卓
     * @param gender    性别 1男，2女
     * @return 某段时间内新增的用户
     */
    @Cacheable(value = "reports", key = "targetClass + methodName + #p0 + #p1 + #p2 + #p3",
            condition = "#endTime<T(com.quhong.core.utils.DateHelper).DEFAULT.getTodayStartTime()/1000", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public Set<String> totalNewActorUidSet(Integer startTime, Integer endTime, int os, int gender) {
        List<Actor> list = totalNewActorList(startTime, endTime, os, gender);
        return list.stream().map(Actor -> Actor.get_id().toString()).collect(Collectors.toSet());
    }

    /**
     * 统计一段时间内新增的用户
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param os        1 ios，0 安卓
     * @param gender    性别 1男，2女
     * @param app       1Youstar、2Youstar Pro、-1total
     * @return 某段时间内新增的用户
     */
    public Set<String> totalNewActorUidSet(Integer startTime, Integer endTime, int os, int gender, int app, List<Integer> versionCodeList) {
        Query query = MongoUtils.setQueryBy_idRange(startTime, endTime);
        if (null == query) {
            query = new Query();
        }
        if (1 == os) {
            query.addCriteria(Criteria.where("os").is(String.valueOf(os)));
        } else if (0 == os) {
            query.addCriteria(Criteria.where("os").ne(String.valueOf(os)));
        }
        if (1 == gender) {
            query.addCriteria(Criteria.where("fb_gender").is(gender));
        } else if (2 == gender) {
            query.addCriteria(Criteria.where("fb_gender").is(gender));
        }
        // ios目前没有pro版本，跳过筛选
        if (1 != os) {
            if (1 == app) {
                query.addCriteria(Criteria.where("app_package_name").is("in.dradhanus.liveher"));
            } else if (2 == app) {
                query.addCriteria(Criteria.where("app_package_name").is("com.youstar.android.lite"));
            } else if (3 == app) {
                query.addCriteria(Criteria.where("app_package_name").is("com.stonemobile.youstar"));
            } else if (AppPackageConstant.YOUSTAR == app) {
                query.addCriteria(Criteria.where("app_package_name").in("in.dradhanus.liveher", "com.stonemobile.youstar"));
            }
        }
        if (!CollectionUtils.isEmpty(versionCodeList)) {
            if (versionCodeList.size() == 1) {
                query.addCriteria(Criteria.where("version_code").is(versionCodeList.get(0)));
            } else {
                query.addCriteria(Criteria.where("version_code").in(versionCodeList));
            }
        }
        List<Actor> list = getActorByQuery(query);
        return list.stream().map(Actor -> Actor.get_id().toString()).collect(Collectors.toSet());
    }

    /**
     * 统计一段时间内新增的华为用户
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @return 某段时间内新增的华为用户
     */
    @Cacheable(value = "reports", key = "targetClass + methodName + #p0 + #p1 + #p2 + #p3",
            condition = "#endTime<T(com.quhong.core.utils.DateHelper).DEFAULT.getTodayStartTime()/1000", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public Set<String> huaWeiNewActorUidSet(Integer startTime, Integer endTime, int os, int gender) {
        Query query = MongoUtils.setQueryBy_idRange(startTime, endTime);
        if (null == query) {
            query = new Query();
        }
        query.addCriteria(Criteria.where("channel").is("huawei"));
        if (1 == os) {
            query.addCriteria(Criteria.where("os").is(String.valueOf(os)));
        } else if (0 == os) {
            query.addCriteria(Criteria.where("os").ne(String.valueOf(os)));
        }
        if (1 == gender) {
            query.addCriteria(Criteria.where("fb_gender").is(gender));
        } else if (2 == gender) {
            query.addCriteria(Criteria.where("fb_gender").is(gender));
        }
        List<Actor> list = getActorByQuery(query);
        return list.stream().map(Actor -> Actor.get_id().toString()).collect(Collectors.toSet());
    }

    /**
     * 统计一段时间内新增的用户
     *
     * @param startTime 开始时间
     * @param endTime   结尾时间
     * @param os        1 ios，0 安卓
     * @param gender    性别 1男，2女
     * @return 某段时间内新增的用户
     */
    public List<Actor> totalNewActorList(Integer startTime, Integer endTime, int os, int gender) {
        Query query = MongoUtils.setQueryBy_idRange(startTime, endTime);
        if (null == query) {
            query = new Query();
        }
        if (1 == os) {
            query.addCriteria(Criteria.where("os").is(String.valueOf(os)));
        } else if (0 == os) {
            query.addCriteria(Criteria.where("os").ne(String.valueOf(os)));
        }
        if (1 == gender) {
            query.addCriteria(Criteria.where("fb_gender").is(gender));
        } else if (2 == gender) {
            query.addCriteria(Criteria.where("fb_gender").is(gender));
        }
        return getActorByQuery(query);
    }

    /**
     * 从mongo中获取数据
     *
     * @param query 过滤条件
     * @return actor
     */
    private Actor getOneActorByQuery(Query query) {
        Actor actor = mongoTemp.findOne(query, Actor.class);
        if (null == actor || null == actor.get_id()) return null;
        return actor;
    }

    /**
     * 从mongo中获取数据
     *
     * @param query 过滤条件
     * @return actor集合
     */
    private List<Actor> getActorByQuery(Query query) {
        List<Actor> list = mongoTemp.find(query, Actor.class);
        if (CollectionUtils.isEmpty(list)) return new ArrayList<>();
        return list;
    }

    /**
     * 根据性别查找用户数据
     *
     * @param gender 性别 1 是男性2 是女性
     */
    public List<Actor> getActorByGender(int gender, Set<String> uidSet) {
        Criteria criteria = Criteria.where("fb_gender").is(gender);
        if (!CollectionUtils.isEmpty(uidSet)) {
            criteria.and("_id").in(uidSet);
        }
        Query query = new Query(criteria);
        return mongoTemp.find(query, Actor.class);
    }

    /**
     * 获得actor的json字符串
     *
     * @param uid actor的_id
     * @return actor的json字符串
     */
    private String findActorJSONFromDB(String uid) {
        try {
            Query query = new Query(Criteria.where("_id").is(uid));
            String jsonStr = "";
            boolean test = !ServerType.PRODUCT.equals(ServerConfig.getServerType());
            if (test) {
                jsonStr = mongoTemplate.findOne(query, String.class, "actor");
            } else {
                jsonStr = mongoTemp.findOne(query, String.class, "actor");
            }

            if (StringUtils.isEmpty(jsonStr)) {
                return null;
            }
            return jsonStr;
        } catch (Exception e) {
            logger.error("get actor from mongodb. {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 根据性别过滤结果
     *
     * @param gender 性别 1男，2女
     * @param uidSet uidSet
     * @return 根据性别过滤结果
     */
    public Set<String> filterActorByUidSet(String gender, Set<String> uidSet) {
        List<Actor> actorByGender = getActorByGender(Integer.parseInt(gender), uidSet);
        return actorByGender.stream().map(Actor -> Actor.get_id().toString()).collect(Collectors.toSet());
    }

    /**
     * 获取留存用户
     *
     * @param dayTimeData
     * @param os
     * @param uidSet
     * @return
     */
    public List<ActorData> remainActors(DayTimeData dayTimeData, int os, Set<String> uidSet) {
        Criteria criteria = Criteria.where("last_login.login_time").gt(dayTimeData.getTime()).lt(dayTimeData.getEndTime());
        if (1 == os) {
            criteria = criteria.and("os").is(String.valueOf(os));
        } else if (0 == os) {
            criteria = criteria.and("os").ne(String.valueOf(os));
        }
        if (!CollectionUtils.isEmpty(uidSet)) {
            criteria = criteria.and("_id").in(uidSet);
        }
        List<MongoActorData> actors = mongoTemp.find(new Query(criteria), MongoActorData.class);
        List<ActorData> list = new ArrayList<>();
        for (MongoActorData actor : actors) {
            ActorData actorData = new ActorData();
            actor.copyTo(actorData);
            list.add(actorData);
        }
        return list;
    }

    /***
     * 过滤os用户
     * @param os
     * @param uidSet
     * @return
     */
    public Set<String> filterOSUser(int os, Set<String> uidSet) {
        if (os == -1 || CollectionUtils.isEmpty(uidSet)) {
            return uidSet;
        }
        Criteria criteria = Criteria.where("_id").in(uidSet);
        if (1 == os) {
            criteria = criteria.and("os").is(String.valueOf(os));
        } else if (0 == os) {
            criteria = criteria.and("os").ne(String.valueOf(os));
        }
        Query query = new Query(criteria);
        query.fields().include("_id");
        List<MongoActorData> actors = mongoTemp.find(query, MongoActorData.class);
        Set<String> osUidSet = new HashSet<>();
        for (MongoActorData actor : actors) {
            osUidSet.add(actor.get_id().toString());
        }
        return osUidSet;
    }
}
