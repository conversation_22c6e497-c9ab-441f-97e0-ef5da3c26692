package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.SpringUtils;
import com.quhong.data.ActorData;
import com.quhong.enums.BlockTnConstant;
import com.quhong.enums.ServerType;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.PhoneAccountDao;
import com.quhong.mongo.dao.UserMonitorDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.monitor.BaseMonitorSender;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.mysql.mapper.ustar.GiftMapper;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.country.CountryQuery;
import com.quhong.operation.dao.*;
import com.quhong.operation.enums.PtgSendBeansWeekEnum;
import com.quhong.operation.share.data.CountryData;
import com.quhong.operation.share.el.MoneyDetailES;
import com.quhong.operation.share.mongobean.Actor;
import com.quhong.operation.share.mysql.SendBeanLog;
import com.quhong.operation.share.vo.BlockUserVO;
import com.quhong.operation.share.vo.FristRechargeUserVO;
import com.quhong.operation.share.vo.UnblockUserVO;
import com.quhong.operation.utils.Country;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.EmailUtils;
import com.quhong.operation.utils.ExcelUtils;
import com.quhong.redis.BlockRedis;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.PartyGirlRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import com.quhong.mongo.data.UserMonitorData;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/10
 */
@Service
public class ScheduledServer {

    private final static Logger logger = LoggerFactory.getLogger(ScheduledServer.class);

    private final static Set<String> PTG_TN_ID_SET = new HashSet<>();

    @Autowired
    private BeansManagerServer beansManagerServer;
    @Autowired
    private MoneyToolServer moneyToolServer;
    @Autowired
    private OperationActorDao operationActorDao;
    @Autowired
    private BlockedServer blockedServer;
    @Autowired
    private SendBeansLogDao sendBeansLogDao;
    @Autowired
    private MicTimeDao micTimeDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private PtgSendBeansFailedDao ptgSendBeansFailedDao;
    @Resource
    private GiftMapper giftMapper;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private PartygirlNewDao partygirlNewDao;
    @Resource
    private PartyGirlRedis partyGirlRedis;
    @Resource
    private PtgSendBeansDao ptgSendBeansDao;
    @Resource
    private RoomUserOnlineDao roomUserOnlineDao;
    @Resource
    private UserRechargeRecordDao userRechargeRecordDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private PhoneAccountDao phoneAccountDao;
    @Autowired
    private CountryQuery countryQuery;
    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainRedis;
    @Resource
    private FirebaseLoginSuccessDao firebaseLoginSuccessDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private UserMonitorDao userMonitorDao;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;
    @Resource
    private AdminUserDao adminUserDao;

    private final Integer OPERATION_SEND_BEANS_TYPE = 5;
    private final String ADMIN_DIAMONDS_DAILY_TITLE = "admin diamonds daily";
    private final String PARTY_GIRL_TITLE = "admin charge for party girl";
    private final String PARTY_GIRL_DESC = "Party Girl support";
    private final int PARTY_GIRL_BEAN = 4000;
    public static final String GIFT_SEND_NUM = "giftSendNum";

    private final Integer TYPE_NOT_SEND_BEANS = 0; // 暂停打钻
    private final Integer TYPE_REMOVE_PTG = 1; // 移除PTG


    /**
     * 每天管理打钻
     * 每天北京时间下午7点执行
     * 线上服务器是utc时间的所以时间要减八个小时
     */
    // @Scheduled(cron = "${everyday.cron}")
    public void everyday() {
        logger.info("===begin everyday send bean===");
        long startTime = System.currentTimeMillis();
        try {
            List<String> errorList = new ArrayList<>();
            Map<String, List<String>> listMap = ExcelUtils.readExcel("everydayPath");
            int ridSumNum = 0;
            int beanSumNum = 0;
            for (String header : listMap.keySet()) {
                List<String> list = listMap.get(header);
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                if (!header.startsWith("rids:")) {
                    logger.info("header not start with 'rids:' header={}", header);
                    errorList.add("header not start with 'rids:', header=" + header);
                    continue;
                }
                String beanNum = header.substring("rids:".length());
                int bean;
                try {
                    bean = Integer.parseInt(beanNum);
                    ridSumNum += list.size();
                    beanSumNum += bean * list.size();
                } catch (Exception e) {
                    logger.error("parse header bean value exception header={}", e.getMessage());
                    errorList.add("parse header bean value exception header=" + header);
                    continue;
                }
                ApiResult<List<String>> result =
                        beansManagerServer.batchSendBean(list, bean, ADMIN_DIAMONDS_DAILY_TITLE, "");
                if (!result.isOK()) {
                    errorList.add("send beans fail, ridList=" + list);
                    logger.error("send beans fail, ridList={}", list);
                }
            }
            //打钻告警群
            BaseMonitorSender monitorSender = SpringUtils.getBean(MonitorSender.class);
            if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
                monitorSender.info("diamonds", "everyday打钻", "人数 =" + ridSumNum + ",钻石总数 =" + beanSumNum);
            }
            logger.info("{} send bean finish error list={}",
                    ADMIN_DIAMONDS_DAILY_TITLE, JSON.toJSONString(errorList));
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
        }
        long diff = System.currentTimeMillis() - startTime;
        String date = DateHelper.ARABIAN.dateToStr(new Date());
        logger.info("{} {} send bean finish elapsed time {} millis", date, ADMIN_DIAMONDS_DAILY_TITLE, diff);
    }

    /**
     * partyGirl每天打钻定时任务
     * 每天北京时间下午5点执行
     * 线上服务器是utc时间的所以时间要减八个小时
     */
    // @Scheduled(cron = "${partyGirl.cron}")
    public void partyGirl() {
        logger.info("===begin party girl send bean===");
        long startTime = System.currentTimeMillis();
        int weekOfDate = DateHelper.ARABIAN.getWeekOfDate(new Date()) + 1;
        String date = DateHelper.ARABIAN.dateToStr(new Date());
        try {
            List<PtgSendBeansData> list = ptgSendBeansDao.findList(weekOfDate, 1);
            if (CollectionUtils.isEmpty(list)) {
                logger.error("get party girl send beans list is empty, sheet={} is empty", PtgSendBeansWeekEnum.getNameByCode(weekOfDate));
                return;
            }
            // 打钻，并记录失败名单
            Set<Integer> failRidSet = new HashSet<>();
            Set<Integer> ridSet = list.stream().map(PtgSendBeansData::getRid).collect(Collectors.toSet());
            Set<String> uidSet = new HashSet<>();
            for (Integer rid : ridSet) {
                if (!newPartyGirlSendBean(rid, uidSet)) {
                    failRidSet.add(rid);
                }
            }
            int noInRoomTimeNum = 0;
            int inRoomTimeOneHour = 0;
            try {
                int nowTime = DateHelper.ARABIAN.currentTimeZoneSeconds();
                Map<String, Integer> totalInRoomTimeMap = roomUserOnlineDao.userOnRoomTimeByUidSet(nowTime - 7 * 24 * 60 * 60, nowTime, uidSet);
                for (String uid : uidSet) {
                    Integer inRoomTime = totalInRoomTimeMap.get(uid);
                    if (inRoomTime == null || inRoomTime == 0) {
                        noInRoomTimeNum++;
                        continue;
                    }
                    if (inRoomTime <= 60 * 60) {
                        inRoomTimeOneHour++;
                    }
                }
            } catch (Exception e) {
                logger.error("{}", e.getMessage(), e);
            }
            logger.info("{} {} send bean finish, fail rid list={}", PtgSendBeansWeekEnum.getNameByCode(weekOfDate), PARTY_GIRL_TITLE, failRidSet);
            //打钻告警群
            BaseMonitorSender monitorSender = SpringUtils.getBean(MonitorSender.class);
            if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
                // monitorSender.info("diamonds", "partyGirl每天打钻", "人数 =" + (ridSet.size() - failRidSet.size()) + ",钻石总数 =" + (ridSet.size() - failRidSet.size()) * PARTY_GIRL_BEAN);
                monitorSender.info("diamonds", "partyGirl每天打钻", "人数 =" + (ridSet.size() - failRidSet.size()) + ",钻石总数 =" + (ridSet.size() - failRidSet.size()) * PARTY_GIRL_BEAN + ",其中一周内在房间总时长为0的人数 =" + noInRoomTimeNum + ",在房间总时长小于1小时的人数 =" + inRoomTimeOneHour);
            }
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
        }
        PTG_TN_ID_SET.clear();
        long diff = System.currentTimeMillis() - startTime;
        logger.info("{} {} send bean finish elapsed time {} millis", date, PARTY_GIRL_TITLE, diff);
    }

    /**
     * party girl 打钻方法(斋月打钻)
     *
     * @param rid rid
     * @return true:成功; false:失败;
     */
    private boolean newPartyGirlSendBean(int rid, Set<String> uidSet) {
        SendBeanLog log = new SendBeanLog();
        try {
            // 获取用户信息
            String uid;
            boolean test = !ServerType.PRODUCT.equals(ServerConfig.getServerType());
            if (test) {
                ActorData actorData = actorDao.getActorByRid(rid);
                if (null == actorData) {
                    logger.error("test rid={} user is not existed or invalid", rid);
                    return false;
                }
                uid = actorData.getUid();
            } else {
                ApiResult<Actor> result = operationActorDao.getActorByRid(rid);
                //没有找到数据，返回打钻失败
                if (!result.isOK() || null == result.getData()) {
                    logger.error("actor not exist rid = {} date = {}", rid, DateHelper.ARABIAN.dateToStr(new Date()));
                    savePtgSendBeansFailed(rid, TYPE_NOT_SEND_BEANS, "找不到该账号");
                    return false;
                }
                //如果是男用户就过滤,系统自动停止打钻,移除PTG
                if (result.getData().getFbGender().equals("1")) {
                    logger.error("actor gender can not is man rid = {} date = {}", rid, DateHelper.ARABIAN.dateToStr(new Date()));
                    savePtgSendBeansFailed(rid, TYPE_REMOVE_PTG, "PTG账号性别为男用户");
                    removePtg(result.getData().getUid(), result.getData().getTn_id());
                    return false;
                }
                // 账户余额累计大于50K，系统停止打钻
                if (result.getData().getBeans() > 50000) {
                    logger.error("The cumulative account balance is greater than 50K. rid = {} date = {} beans = {}", rid, DateHelper.ARABIAN.dateToStr(new Date()), result.getData().getBeans());
                    savePtgSendBeansFailed(rid, TYPE_NOT_SEND_BEANS, "账户余额累计大于50K");
                    return false;
                }
                // PTG账号登录的设备被封号，系统自动停止打钻，移除PTG
                String blockTime = blockRedis.checkBlock(result.getData().getTn_id(), BlockTnConstant.BLOCK_LOGIN);
                if (!StringUtils.isEmpty(blockTime)) {
                    logger.error("device is blocked. rid = {} date = {} ", rid, DateHelper.ARABIAN.dateToStr(new Date()));
                    savePtgSendBeansFailed(rid, TYPE_REMOVE_PTG, "PTG账号登录的设备被封");
                    removePtg(result.getData().getUid(), result.getData().getTn_id());
                    return false;
                }
                // PTG账号被封，系统自动停止打钻，移除PTG
                if (result.getData().getValid() == 0) {
                    logger.error("Account is blocked. rid = {} date = {} ", rid, DateHelper.ARABIAN.dateToStr(new Date()));
                    savePtgSendBeansFailed(rid, TYPE_REMOVE_PTG, "PTG账号被封");
                    removePtg(result.getData().getUid(), result.getData().getTn_id());
                    return false;
                }
                // 同一设备的PTG账号，系统停止打钻
                if (PTG_TN_ID_SET.contains(result.getData().getTn_id())) {
                    logger.error("Multiple accounts exist on the same device. rid = {} date = {} ", rid, DateHelper.ARABIAN.dateToStr(new Date()));
                    savePtgSendBeansFailed(rid, TYPE_NOT_SEND_BEANS, "同一设备存在多个PTG账号");
                    return false;
                }
                uid = result.getData().get_id().toString();
                PTG_TN_ID_SET.add(result.getData().getTn_id());
            }
            log.setUid(uid);
            //开始发送红包
            MoneyDetailES moneyDetail = new MoneyDetailES(
                    uid, PARTY_GIRL_BEAN, PARTY_GIRL_TITLE, PARTY_GIRL_DESC, OPERATION_SEND_BEANS_TYPE);
            ApiResult<Long> sendResult = moneyToolServer.chargeBeansByUid(moneyDetail);
            if (sendResult.isOK() && sendResult.getData() > 0) {
                logger.info("admin send free beans success;rid = {} uid = {};", rid, uid);
            }
            log.setSendBean(PARTY_GIRL_BEAN);
            log.setCtime(System.currentTimeMillis() / 1000L);
            // 插入log数据
            sendBeansLogDao.insertSendBeanLog(log);
            uidSet.add(uid);
            // 打钻成功返回true
            if (sendResult.isOK() && sendResult.getData() > 0) {
                return true;
            }
            logger.error("send beans log insert one failed in db data = {}", log.toString());
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
        }
        return false;
    }

    /**
     * 移除ptg
     */
    private void removePtg(String uid, String tnId) {
        partygirlNewDao.deleteByUid(uid);
        partyGirlRedis.removePartyGirl(uid, tnId);
    }

    private void savePtgSendBeansFailed(int rid, int type, String remark) {
        PtgSendBeansFailedData data = new PtgSendBeansFailedData();
        data.setRid(rid);
        data.setType(type);
        data.setRemark(remark);
        data.setCtime(DateHelper.ARABIAN.currentTimeZoneSeconds());
        ptgSendBeansFailedDao.insert(data);
    }



    /**
     * 北京时间8：01定时发送当天封禁用户信息
     */
    // @Scheduled(cron = "0 0/5 * * * ?")
    @Scheduled(cron = "0 01 0 * * ?")
    public void sendBlockAccountEmail() {
        Date yesterday = DateHelper.ARABIAN.dateAddDay(new Date(), -1);
        long startTime = DateHelper.ARABIAN.setStartTime(yesterday) / 1000L;
        long endTime = startTime + (24 * 60 * 60);
        logger.info("start sendBlockAccountEmail start={} end={} ", startTime, endTime);

        // 获取昨天封号数据
        ApiResult<List<BlockUserVO>> result =
                blockedServer.blockedList((int) startTime, (int) endTime, null);
        String dateStr = DateHelper.ARABIAN.dateToStr(new Date());
        if (!result.isOK()) {
            logger.error("{} Get block account fail, cause of failure {}", dateStr, result.getMsg());
            return;
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            logger.error("{} No access block account!", dateStr);
            return;
        }
        //过滤充值用户名单
        List<BlockUserVO> payBlockUserList = new ArrayList<>();
        for (BlockUserVO blockUserVO : result.getData()) {
            BigDecimal totalChargeMoney = blockUserVO.getTotalChargeMoney();
            if (totalChargeMoney != null && totalChargeMoney.compareTo(new BigDecimal(0)) > 0) {
                payBlockUserList.add(blockUserVO);
            }
        }

        if (CollectionUtils.isEmpty(payBlockUserList)) {
            logger.error("send block account email error. pay user is empty. start={} end={}", startTime, endTime);
            return;
        }

        String[] titleArr = new String[]{"ID", "昵称", "性别", "国家", "平台", "用户等级", "VIP等级", "注册时间",
                "最近登陆时间", "充值用户", "累计充值美金", "处理方式", "处理时间", "处理人", "封禁时长", "封禁原因"};

        // 整理发送表格
        String emailContent = getEmailContent(titleArr, payBlockUserList);
        Map<String, String> receiveAccountMap = new HashMap<>();
//        receiveAccountMap.put("<EMAIL>", "laiyongqi");
        if (ServerConfig.isProduct()) {
            receiveAccountMap.put("<EMAIL>", "baijun");
            receiveAccountMap.put("<EMAIL>", "markchen");
//            receiveAccountMap.put("<EMAIL>", "maoyule");
            receiveAccountMap.put("<EMAIL>", "tedysong");
//            receiveAccountMap.put("<EMAIL>", "ziroon");
//            receiveAccountMap.put("<EMAIL>", "ali");
            receiveAccountMap.put("<EMAIL>", "xiejianliang");
            // receiveAccountMap.put("<EMAIL>", "wenmiaofang");
        }
        // 发送邮件
        EmailUtils.sendMail("YOUSTAR · 封充值用户", emailContent, receiveAccountMap);
        logger.info("{} send block account email finish!", dateStr);
    }

    /**
     * 北京时间5：15定时发送即将解封的充值用户信息（剩余2天解封时）
     */
    @Scheduled(cron = "0 15 21 * * ?")
    public void sendUnblockAccountEmail() {
        String dateStr = DateHelper.ARABIAN.dateToStr(new Date());
        logger.info("start sendUnblockAccountEmail at {}", dateStr);

        try {
            // 计算2天后的时间戳
            int twoDaysLater = DateHelper.ARABIAN.currentTimeZoneSeconds() + (int) TimeUnit.DAYS.toSeconds(2);
            int threeDaysLater = twoDaysLater + (int) TimeUnit.DAYS.toSeconds(1); // 允许一天的误差范围

            // 查询user_monitor表中即将在2天后解封的用户

            List<UserMonitorData> unblockUsers = userMonitorDao.findDataByReleaseAt(twoDaysLater, threeDaysLater);

            if (CollectionUtils.isEmpty(unblockUsers)) {
                logger.info("sendUnblockAccountEmail: No users to unblock in 2 days");
                return;
            }

            // 过滤出累计充值≥300美金的用户
            List<UnblockUserVO> qualifiedUsers = new ArrayList<>();
            for (UserMonitorData userMonitor : unblockUsers) {
                ApiResult<Actor> actorResult = blockedServer.getActorInfoByRidOrUid(null, userMonitor.getUid());
                if (!actorResult.isOK()) {
                    continue;
                }
                Actor actor = actorResult.getData();
                if (actor == null) {
                    logger.warn("Cannot find actor info for uid: {}", userMonitor.getUid());
                    continue;
                }
                String uid = actor.get_id().toString();
                // 查询用户累计充值金额
                BigDecimal totalCharge = BigDecimal.ZERO;

                ActorPayExternalData actorPayExternalData = actorPayExternalDao.selectOne(uid);
                if (actorPayExternalData != null) {
                    totalCharge = actorPayExternalData.getRechargeMoney() != null ? actorPayExternalData.getRechargeMoney() : new BigDecimal("0");
                }


                // 只处理累计充值≥300美金的用户
                if (totalCharge.compareTo(new BigDecimal("300")) >= 0) {
                    UnblockUserVO vo = new UnblockUserVO();
                    fillUnblockUserInfo(vo, actor, userMonitor, totalCharge);
                    qualifiedUsers.add(vo);
                }
            }

            if (CollectionUtils.isEmpty(qualifiedUsers)) {
                logger.info("sendUnblockAccountEmail: No qualified users (charge ≥ $300) to unblock in 2 days");
                return;
            }

            // 邮件标题（删除"充值用户"，增加"解封时间"）
            String[] titleArr = new String[]{"ID", "昵称", "性别", "国家", "平台", "用户等级", "VIP等级", "注册时间",
                    "最近登陆时间", "累计充值美金", "处理方式", "处理时间", "处理人", "封禁时长", "封禁原因", "解封时间"};

            // 整理发送表格
            String emailContent = getUnblockEmailContent(titleArr, qualifiedUsers);
            Map<String, String> receiveAccountMap = new HashMap<>();

            if (ServerConfig.isProduct()) {
                receiveAccountMap.put("<EMAIL>", "baijun");
                receiveAccountMap.put("<EMAIL>", "markchen");
                receiveAccountMap.put("<EMAIL>", "xiejianliang");
            } else {
//                receiveAccountMap.put("<EMAIL>", "lijun");
                receiveAccountMap.put("<EMAIL>", "guoguangsheng");
            }

            // 发送邮件
            EmailUtils.sendMail("YOUSTAR · 即将解封充值用户通知", emailContent, receiveAccountMap);
            logger.info("{} send unblock account email finish! Total users: {}", dateStr, qualifiedUsers.size());

        } catch (Exception e) {
            logger.error("sendUnblockAccountEmail error: {}", e.getMessage(), e);
        }
    }

    /**
     * 每日00:00(GMT+3时间) 定时发送当天首充用户信息
     */
    // @Scheduled(cron = "0 0/5 * * * ?")
    @Scheduled(cron = "15 45 00 * * ?")
    public void sendFirstRechargeAccountEmail() {
        Date yesterday = DateHelper.ARABIAN.dateAddDay(new Date(), -1);
        int startTime = (int) (DateHelper.ARABIAN.setStartTime(yesterday) / 1000);
        int endTime = startTime + (24 * 60 * 60);
        logger.info("start sendFirstRechargeAccountEmail start={} end={} ", startTime, endTime);
        String dateStr = DateHelper.ARABIAN.dateToStr(new Date());
        //获取昨天首充用户的数据
        List<UserRechargeRecordData> firstChargeDataList = userRechargeRecordDao.getFirstChargeDataList(startTime, endTime);
        if (CollectionUtils.isEmpty(firstChargeDataList)) {
            logger.error("send first recharge account email error. first recharge user is empty. start={} end={}", startTime, endTime);
            return;
        }
        // 过滤有历史充值记录的用户
        firstChargeDataList.removeIf(recordData -> rechargeDailyInfoDao.getHistoryRechargeTotalNum(recordData.getUid(), startTime) > 0);
        Set<String> uidSet = firstChargeDataList.stream().map(UserRechargeRecordData::getUid).collect(Collectors.toSet());
        Map<String, Integer> inRoomTimeMap = roomUserOnlineDao.userOnRoomTimeByUidSet(startTime, endTime, uidSet);
        Map<String, Integer> upMicTimeMap = micTimeDao.getUpMicStat(startTime, endTime, Collections.emptyList(), new ArrayList<>(uidSet));
        List<FristRechargeUserVO> list = new ArrayList<>();
        for (UserRechargeRecordData rechargeRecordData : firstChargeDataList) {
            Actor actor = getActorInfoByUid(rechargeRecordData.getUid());
            if (actor == null) {
                logger.error("can not find actor data. uid={}", rechargeRecordData.getUid());
                continue;
            }
            FristRechargeUserVO vo = new FristRechargeUserVO();
            fillActorInfo(vo, actor);
            fillRechargeInfo(vo, rechargeRecordData.getRechargeTime(), rechargeRecordData.getUid(), startTime, endTime);
            fillOtherInfo(vo, rechargeRecordData.getUid(), startTime, endTime, inRoomTimeMap, upMicTimeMap);
            list.add(vo);
        }
        if (CollectionUtils.isEmpty(list)) {
            logger.error("send first recharge account email error. first recharge user is empty. start={} end={}", startTime, endTime);
            return;
        }
        Comparator<FristRechargeUserVO> rechargeAmountAsc = Comparator.comparing(o -> o.getRechargeAmount().multiply(new BigDecimal("100")).intValue());
        // 报表按充值金额降序排列
        list.sort(rechargeAmountAsc.reversed());
        String[] titleArr = new String[]{"User ID", "Name", "Country", "Gender", "Registration time(GMT+3)", "First recharge time(GMT+3)", "Recharge amount", "Recharge times",
                "Recharge channel", "Contact", "Time spent on rooms", "Time spent on Mic", "which room id spend most time ", "User source"};
        // 整理发送表格
        String emailContent = getFirstRechargeEmailContent(titleArr, list);
        Map<String, String> receiveAccountMap = new HashMap<>();
//        receiveAccountMap.put("<EMAIL>", "laiyongqi");
        if (ServerConfig.isProduct()) {
            receiveAccountMap.put("<EMAIL>", "tedysong");
            receiveAccountMap.put("<EMAIL>", "markchen");
            receiveAccountMap.put("<EMAIL>", "xiejianliang");
            receiveAccountMap.put("<EMAIL>", "baijun");
        }
        // 发送邮件
        EmailUtils.sendMail("First recharge users", emailContent, receiveAccountMap);
        logger.info("{} send block account email finish!", dateStr);

    }

    private void fillActorInfo(FristRechargeUserVO vo, Actor actor) {
        vo.setUid(actor.getUid());
        vo.setRid(actor.getRid());
        vo.setName(actor.getName());
        //通过ip查找国家
        String ip = actor.getIp();
        if (!StringUtils.isEmpty(ip)) {
            CountryData countryData = countryQuery.find(ip);
            vo.setCountry(countryData != null ? countryData.getCountry() : "");
        }
        if (StringUtils.isEmpty(vo.getCountry())) {
            vo.setCountry(actor.getCountry() != null ? actor.getCountry() : "");
        }
        vo.setGender("1".equals(actor.getFbGender()) ? "Male" : "Female");
        vo.setRegistrationTime(DateHelper.ARABIAN.datetimeToStr(new Date(actor.get_id().getTimestamp() * 1000L)));
        List<String> contactList = new ArrayList<>();
        String userPhone = phoneAccountDao.getUserPhone(actor.getUid());
        if (!StringUtils.isEmpty(userPhone)) {
            contactList.add(userPhone);
        }
        if (!StringUtils.isEmpty(actor.getEmail())) {
            contactList.add(actor.getEmail());
        }
        vo.setContact(String.join(",", contactList));
        FirebaseLoginSuccessData loginSuccessData = firebaseLoginSuccessDao.selectFirstOneByUid(actor.getUid());
        vo.setSource(loginSuccessData == null || StringUtils.isEmpty(loginSuccessData.getFirebaseCampaign()) || "(direct)".equals(loginSuccessData.getFirebaseCampaign()) ? "organic" : loginSuccessData.getFirebaseCampaign());
    }

    private void fillRechargeInfo(FristRechargeUserVO vo, int firstRechargeTime, String uid, int startTime, int endTime) {
        List<RechargeDailyInfoData> userRechargeInfoList = rechargeDailyInfoDao.getUserRechargeInfoList(uid, startTime, endTime);
        BigDecimal rechargeAmount = new BigDecimal("0");
        int rechargeTimes = 0;
        Set<String> rechargeChannelSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(userRechargeInfoList)) {
            for (RechargeDailyInfoData data : userRechargeInfoList) {
                rechargeTimes++;
                rechargeAmount = rechargeAmount.add(data.getRechargeMoney());
                rechargeChannelSet.add(getRechargeChannel(data));
            }
        }
        vo.setFirstRechargeTime(DateHelper.ARABIAN.datetimeToStr(new Date(firstRechargeTime * 1000L)));
        vo.setRechargeAmount(rechargeAmount);
        vo.setStrRechargeAmount("$" + rechargeAmount.toString());
        vo.setRechargeChannel(String.join(",", rechargeChannelSet));
        vo.setRechargeTimes(rechargeTimes);
    }

    private void fillOtherInfo(FristRechargeUserVO vo, String uid, int startTime, int endTime, Map<String, Integer> inRoomTimeMap, Map<String, Integer> upMicTimeMap) {
        int inRoomTime = inRoomTimeMap.get(uid) != null ? inRoomTimeMap.get(uid) : 0;
        vo.setInRoomTime(inRoomTime / 60 + " Mins");
        int upMicTime = upMicTimeMap.get(uid) != null ? upMicTimeMap.get(uid) : 0;
        vo.setOnMicTime(upMicTime / 60 + " Mins");
        vo.setLongestStayRoomRid("");
        String roomId = roomUserOnlineDao.getUserLongestStayRoomId(uid, startTime, endTime);
        if (StringUtils.hasLength(roomId)) {
            Actor roomOwner = getActorInfoByUid(RoomUtils.getRoomHostId(roomId));
            if (roomOwner == null) {
                logger.error("can not find actor data. uid={}", RoomUtils.getRoomHostId(roomId));
            } else {
                vo.setLongestStayRoomRid(roomOwner.getRid() + "");
            }
        }
    }

    private String getRechargeChannel(RechargeDailyInfoData data) {
        String rechargeChannel = "";
        switch (data.getPayType()) {
            case 1:
                rechargeChannel = "Google Pay";
                break;
            case 2:
                rechargeChannel = "Apple Pay";
                break;
            case 3:
                rechargeChannel = "Tap Pay";
                break;
            case 4:
                rechargeChannel = "Admin Pay";
                break;
            case 5:
                rechargeChannel = "Jollychic Charge";
                break;
            case 6:
                rechargeChannel = "Huawei Pay";
                break;
            case 7:
                rechargeChannel = "OPay Charge";
                break;
            case 8:
                rechargeChannel = data.getSubType();
                break;
            default:
                break;
        }
        return rechargeChannel;
    }

    /**
     * 整理发送内容
     *
     * @param titleArr 表格标题
     * @param list     表格内容
     * @return 发送内容字符串
     */
    private String getEmailContent(String[] titleArr, List<BlockUserVO> list) {
        StringBuilder content = new StringBuilder("<html><head></head><body>");
        content.append("<table border=\"5\" style=\"border:solid 1px #E8F2F9;font-size=14px;;font-size:18px;\">");

        // 设置标题行
        content.append("<tr style=\"background-color: #428BCA; color:#ffffff\">");
        for (String t : titleArr) {
            content.append("<th>").append(t).append("</th>");
        }
        content.append("</tr>");

        // 内容填充
        for (BlockUserVO vo : list) {
            if (null == vo) {
                continue;
            }
            content.append("<tr>");

            content.append("<td>").append(vo.getRid()).append("</td>");
            content.append("<td>").append(vo.getName()).append("</td>");
            content.append("<td>").append(vo.getGender()).append("</td>");
            content.append("<td>").append(vo.getCountry()).append("</td>");
            content.append("<td>").append(vo.getTerrace()).append("</td>");
            content.append("<td>").append(vo.getUlvl()).append("</td>");
            content.append("<td>").append(null == vo.getVipLevel() ? 0 : vo.getVipLevel()).append("</td>");
            content.append("<td>").append(vo.getRegisterDate()).append("</td>");
            content.append("<td>").append(vo.getLastLogin()).append("</td>");
            content.append("<td>").append(vo.getChargeChannel()).append("</td>");
            if (vo.getTotalChargeMoney().compareTo(new BigDecimal(200)) > 0) {
                content.append("<td><font color=\"red\">").append(vo.getTotalChargeMoney()).append("</font></td>");
            } else {
                content.append("<td>").append(vo.getTotalChargeMoney()).append("</td>");
            }
            content.append("<td>").append(vo.getOperation()).append("</td>");
            content.append("<td>").append(vo.getOptDate()).append("</td>");
            content.append("<td>").append(vo.getOptUser()).append("</td>");
            content.append("<td>").append(null == vo.getBlockTime() ? "" : vo.getBlockTime()).append("</td>");
            content.append("<td>").append(vo.getBlockReason()).append("</td>");


            content.append("</tr>");
        }

        // 收尾
        content.append("</table>");
        content.append("</body></html>");

        return content.toString();
    }

    /**
     * 整理发送内容
     *
     * @param titleArr 表格标题
     * @param list     表格内容
     * @return 发送内容字符串
     */
    private String getFirstRechargeEmailContent(String[] titleArr, List<FristRechargeUserVO> list) {
        StringBuilder content = new StringBuilder("<html><head></head><body>");
        content.append("<table border=\"5\" style=\"border:solid 1px #E8F2F9;font-size=14px;;font-size:18px;\">");
        // 设置标题行
        content.append("<tr style=\"background-color: #428BCA; color:#ffffff\">");
        for (String t : titleArr) {
            content.append("<th>").append(t).append("</th>");
        }
        content.append("</tr>");
        // 内容填充
        for (FristRechargeUserVO vo : list) {
            if (null == vo) {
                continue;
            }
            content.append("<tr>");
            content.append("<td>").append(vo.getRid()).append("</td>");
            content.append("<td>").append(vo.getName()).append("</td>");
            content.append("<td>").append(vo.getCountry()).append("</td>");
            content.append("<td>").append(vo.getGender()).append("</td>");
            content.append("<td>").append(vo.getRegistrationTime()).append("</td>");
            content.append("<td>").append(vo.getFirstRechargeTime()).append("</td>");
            if (vo.getRechargeAmount().compareTo(new BigDecimal(200)) > 0) {
                content.append("<td><font color=\"red\">").append(vo.getStrRechargeAmount()).append("</font></td>");
            } else {
                content.append("<td>").append(vo.getStrRechargeAmount()).append("</td>");
            }
            content.append("<td>").append(null == vo.getRechargeTimes() ? 0 : vo.getRechargeTimes()).append("</td>");
            content.append("<td>").append(vo.getRechargeChannel()).append("</td>");
            content.append("<td>").append(vo.getContact()).append("</td>");
            content.append("<td>").append(null == vo.getInRoomTime() ? 0 : vo.getInRoomTime()).append("</td>");
            content.append("<td>").append(null == vo.getOnMicTime() ? 0 : vo.getOnMicTime()).append("</td>");
            content.append("<td>").append(vo.getLongestStayRoomRid()).append("</td>");
            content.append("<td>").append(vo.getSource()).append("</td>");
            content.append("</tr>");
        }
        // 收尾
        content.append("</table>");
        content.append("</body></html>");
        return content.toString();
    }

    public List<Integer> getCelebrityList() {
        QueryWrapper<GiftData> query = new QueryWrapper<>();
        query.eq("status", 1).eq("gptype", 3);
        List<GiftData> giftDataList = giftMapper.selectList(query);
        List<Integer> giftList = new ArrayList<>();

        if (giftDataList != null) {
            for (GiftData gift : giftDataList) {
                giftList.add(gift.getRid());
            }
        }

        return giftList;

    }

    /**
     * 通过uid获取用户信息
     *
     * @param uid userId
     * @return actor
     */
    private Actor getActorInfoByUid(String uid) {
        boolean test = !ServerType.PRODUCT.equals(ServerConfig.getServerType());
        Actor actor = null;
        if (test) {
            MongoActorData actorData = actorDao.findActorDataFromDB(uid);
            if (actorData != null) {
                actor = new Actor();
                BeanUtils.copyProperties(actorData, actor);
                actor.setFbGender(actorData.getFb_gender() + "");
                actor.setUid(actorData.get_id().toString());
            }
        } else {
            actor = operationActorDao.getActor(uid);
        }
        return actor;
    }

    public void updateCelebrityGift(int giftId, int forder) {
        GiftData giftData = giftDao.selectOne(giftId);
        if (giftData == null) {
            return;
        }
        giftData.setForder(forder);
        giftDao.updateOne(giftData);
    }


    public static class CelebrityData {
        private int _id;
        private int count;

        public int get_id() {
            return _id;
        }

        public void set_id(int _id) {
            this._id = _id;
        }

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }
    }

    /**
     * 每天北京时间 05:01执行
     */
    @Scheduled(cron = "0 01 21 * * ?")
    public void celebrityGiftRank() {
        logger.info("start celebrityGiftRank.");
        // 查询条件
        try {
            List<Integer> giftList = getCelebrityList();
            Criteria criteria = Criteria.where("giftId").in(giftList);
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group("giftId").sum("giftNum").as("count"),
                    Aggregation.sort(Sort.by(Sort.Order.desc("count")))
            );
            List<CelebrityData> result = mongoTemplate.aggregate(aggregation, GIFT_SEND_NUM,
                    CelebrityData.class).getMappedResults();
            for (int i = 0; i < result.size(); i++) {
                CelebrityData countData = result.get(i);
                int giftId = countData.get_id();
                updateCelebrityGift(giftId, i);
            }
            mainRedis.opsForValue().increment("str:panel:version");
            logger.info("finish celebrityGiftRank. size={}", giftList.size());
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
        }
    }

    /**
     * 填充解封用户信息
     */
    private void fillUnblockUserInfo(UnblockUserVO vo, Actor actor, UserMonitorData userMonitor, BigDecimal totalCharge) {
        vo.setRid(actor.getRid());
        vo.setUid(actor.get_id().toString());
        vo.setName(actor.getName());
        vo.setGender("1".equals(actor.getFbGender()) ? "男" : "女");

        // 通过ip查找国家
        String ip = actor.getIp();
        if (!StringUtils.isEmpty(ip)) {
            CountryData countryData = countryQuery.find(ip);
            vo.setCountry(countryData != null ? countryData.getCountry() : "");
        }
        if (StringUtils.isEmpty(vo.getCountry())) {
            vo.setCountry(Country.getCountryName(actor.getCountry()));
        }

        vo.setTerrace("1".equals(actor.getOs()) ? "IOS" : "Android"); // 使用os字段表示平台
        vo.setUlvl(actor.getUlvl());
        vo.setVipLevel(actor.getVlvl()); // 使用vlvl字段表示VIP等级
        vo.setRegisterDate(DateHelper.ARABIAN.datetimeToStr(new Date(actor.get_id().getTimestamp() * 1000L)));
        vo.setLastLogin(actor.getLastLogin() != null && actor.getLastLogin().getLoginTime() != null ?
                DateHelper.ARABIAN.datetimeToStr(new Date(actor.getLastLogin().getLoginTime() * 1000L)) : "");
        vo.setTotalChargeMoney(totalCharge);
        vo.setOperation("封禁");
        vo.setOptDate(DateHelper.ARABIAN.datetimeToStr(new Date(userMonitor.getOpt_time() * 1000L)));
        if (!StringUtils.isEmpty(userMonitor.getOperator())) {
            ApiResult<String> result = adminUserDao.getAdminUserNameByUid(userMonitor.getOperator());
            if (result.isOK() && null != result.getData()) {
                vo.setOptUser(result.getData());
            } else {
                vo.setOptUser("unknown"); // 这里可以根据实际情况修改
            }
        }else {
            vo.setOptUser("unknown");
        }

        vo.setBlockReason(userMonitor.getReason());
        if ("1".equals(userMonitor.getBlock_term())) {
            vo.setBlockTime("24小时");
        } else if ("2".equals(userMonitor.getBlock_term())) {
            vo.setBlockTime("7天");
        } else if ("4".equals(userMonitor.getBlock_term())) {
            vo.setBlockTime("7天");
        } else if ("5".equals(userMonitor.getBlock_term())) {
            vo.setBlockTime("30天");
        } else {
            vo.setBlockTime("永久");
        }
        vo.setUnblockTime(DateHelper.ARABIAN.datetimeToStr(new Date(userMonitor.getRelease_at() * 1000L)));
    }

    /**
     * 生成解封邮件内容
     */
    private String getUnblockEmailContent(String[] titleArr, List<UnblockUserVO> list) {
        StringBuilder content = new StringBuilder("<html><head></head><body>");
        content.append("<table border=\"5\" style=\"border:solid 1px #E8F2F9;font-size=14px;;font-size:18px;\">");

        // 设置标题行
        content.append("<tr style=\"background-color: #428BCA; color:#ffffff\">");
        for (String t : titleArr) {
            content.append("<th>").append(t).append("</th>");
        }
        content.append("</tr>");

        // 内容填充
        for (UnblockUserVO vo : list) {
            if (null == vo) {
                continue;
            }
            content.append("<tr>");

            content.append("<td>").append(vo.getRid()).append("</td>");
            content.append("<td>").append(vo.getName()).append("</td>");
            content.append("<td>").append(vo.getGender()).append("</td>");
            content.append("<td>").append(vo.getCountry()).append("</td>");
            content.append("<td>").append(vo.getTerrace()).append("</td>");
            content.append("<td>").append(vo.getUlvl()).append("</td>");
            content.append("<td>").append(null == vo.getVipLevel() ? 0 : vo.getVipLevel()).append("</td>");
            content.append("<td>").append(vo.getRegisterDate()).append("</td>");
            content.append("<td>").append(vo.getLastLogin()).append("</td>");

            // 突出显示高充值金额
            if (vo.getTotalChargeMoney().compareTo(new BigDecimal(500)) > 0) {
                content.append("<td><font color=\"red\">$").append(vo.getTotalChargeMoney()).append("</font></td>");
            } else {
                content.append("<td>$").append(vo.getTotalChargeMoney()).append("</td>");
            }

            content.append("<td>").append(vo.getOperation()).append("</td>");
            content.append("<td>").append(vo.getOptDate()).append("</td>");
            content.append("<td>").append(vo.getOptUser()).append("</td>");
            content.append("<td>").append(null == vo.getBlockTime() ? "" : vo.getBlockTime()).append("</td>");
            content.append("<td>").append(vo.getBlockReason()).append("</td>");
            content.append("<td><font color=\"green\">").append(vo.getUnblockTime()).append("</font></td>");

            content.append("</tr>");
        }

        // 收尾
        content.append("</table>");
        content.append("</body></html>");

        return content.toString();
    }
}
