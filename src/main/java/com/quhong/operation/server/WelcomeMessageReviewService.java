package com.quhong.operation.server;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.data.ActorData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.WelcomeMessageReviewDao;
import com.quhong.mysql.data.WelcomeMessageReviewData;
import com.quhong.operation.share.condition.WelcomeMessageCondition;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.share.vo.OpWelcomeMessageReviewVO;
import com.quhong.operation.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 房间欢迎消息审核操作服务类
 *
 * <AUTHOR>
 * @date 2025/8/25 16:18
 */
@Service
public class WelcomeMessageReviewService {
    private static final Logger logger = LoggerFactory.getLogger(WelcomeMessageReviewService.class);
    @Resource
    private WelcomeMessageReviewDao welcomeMessageReviewDao;
    @Resource
    private ActorDao actorDao;

    /**
     * 分页获取欢迎消息审核数据
     */
    public PageResultVO<OpWelcomeMessageReviewVO> selectPageList(WelcomeMessageCondition condition) {
        IPage<WelcomeMessageReviewData> pageList = welcomeMessageReviewDao.selectPageList(condition.getPage(), condition.getPageSize(), condition);
        List<OpWelcomeMessageReviewVO> voList = new ArrayList<>();

        return new PageResultVO<>(pageList.getTotal(), voList);
    }


    /**
     * 插入欢迎消息审核数据
     */
    public void insert(WelcomeMessageReviewData data) {
        welcomeMessageReviewDao.insert(data);
    }

    /**
     * 批量插入欢迎消息审核数据
     */
    public void batchInsert(String roomId, List<WelcomeMessageReviewData> insertList) {
        welcomeMessageReviewDao.batchInsert(roomId, insertList);
    }

    /**
     * 修改审核状态
     */
    public void updateReviewAction(String operatorUid, WelcomeMessageCondition condition) {
        checkParam(condition);
        welcomeMessageReviewDao.updateReviewAction(condition.getRoomId(), condition.getId(), operatorUid,
                condition.getReviewAction(), condition.getRejectReason());
    }

    /**
     * 更新欢迎消息
     */
    public void updateContent(String roomId, Integer id, String messageContent) {
        welcomeMessageReviewDao.updateContent(roomId, id, messageContent);
    }

    /**
     * 删除欢迎消息
     */
    public void delete(String roomId, Integer id) {
        welcomeMessageReviewDao.delete(roomId, id);
    }


    private void checkParam(WelcomeMessageCondition condition) {

        if (null == condition.getId()) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "id不能为空");
        }
        if (null == condition.getReviewAction()) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "审核状态不能为空");
        }
    }


}
