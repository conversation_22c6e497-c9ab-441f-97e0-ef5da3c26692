package com.quhong.operation.server;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mysql.dao.VipConfigDao;
import com.quhong.mysql.data.VipConfigData;
import com.quhong.mysql.data.VipConfigMeta;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * VIP配置服务
 */
@Service
public class VipConfigService {
    private static final Logger logger = LoggerFactory.getLogger(VipConfigService.class);

    @Resource
    private VipConfigDao vipConfigDao;

    /**
     * VIP配置列表查询
     */
    public PageResultVO<VipConfigData> list(BaseCondition condition) {
        PageResultVO<VipConfigData> pageVO = new PageResultVO<>();
        String search = condition.getSearch();
        int page = condition.getPage() == null ? 1 : condition.getPage();
        int pageSize = condition.getPageSize() == null ? 30 : condition.getPageSize();
        IPage<VipConfigData> pageData = vipConfigDao.selectPageList(search, page, pageSize);
        pageVO.setList(pageData.getRecords());
        pageVO.setTotal(pageData.getTotal());
        return pageVO;
    }

    /**
     * 新增VIP配置
     */
    public void addData(VipConfigData dto) {
        // 参数校验
        validateVipConfigData(dto);
        VipConfigData data = new VipConfigData();
        BeanUtils.copyProperties(dto, data);
        data.setMtime(DateHelper.getNowSeconds());
        data.setCtime(DateHelper.getNowSeconds());
        vipConfigDao.insert(data);
    }

    /**
     * 校验VIP配置数据
     */
    private void validateVipConfigData(VipConfigData dto) {
        // 校验VIP等级
        if (dto.getVipLevel() == null || dto.getVipLevel() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "VIP等级必须大于0");
        }

        // 校验价格
        if (dto.getPrice() == null || dto.getPrice() < 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "价格不能为负数");
        }

        // 校验有效天数
        if (dto.getValidDay() == null || dto.getValidDay() <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "有效天数必须大于0");
        }

        // 校验资源key
        if (StringUtils.isEmpty(dto.getResourceKey())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "资源key不能为空");
        }

        // 校验VIP礼物数量
        if (dto.getVipGift() < 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "VIP礼物数量不能小于0");
        }

        // 校验特权字段
        validatePrivilegeField(dto.getPrivilege());
    }

    /**
     * 校验特权字段
     * 使用VipConfigMeta类进行校验
     */
    private void validatePrivilegeField(String privilegeJson) {
        if (StringUtils.isEmpty(privilegeJson)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "特权配置不能为空");
        }

        // 直接将JSON字符串转换为VipConfigMeta对象列表
        List<VipConfigMeta> metaList = JSON.parseArray(privilegeJson, VipConfigMeta.class);
        if (CollectionUtils.isEmpty(metaList)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "特权配置不能为空");
        }
        for(VipConfigMeta meta : metaList){
            validateVipConfigMeta(meta);
        }
    }

    /**
     * 校验VipConfigMeta对象
     */
    private void validateVipConfigMeta(VipConfigMeta meta) {
        // 校验property字段（0:个人特权 1:房间特权）
        if (meta.getProperty() != 0 && meta.getProperty() != 1) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "特权项的property字段必须为0或1");
        }

        // 校验标题
        if (StringUtils.isEmpty(meta.getTitleEn())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(),"特权项的英文标题不能为空");
        }

        if (StringUtils.isEmpty(meta.getTitleAr())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "特权项的阿拉伯语标题不能为空");
        }

        // 这些类型需要预览图
        if (StringUtils.isEmpty(meta.getPreview())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "特权项preview预览图不能为空");
        }
    }

    /**
     * 更新VIP配置
     */
    public void updateData(VipConfigData dto) {
        if (dto.getId() == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "ID不能为空");
        }

        VipConfigData data = vipConfigDao.selectOne(dto.getId());
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "数据不存在");
        }

        // 参数校验
        validateVipConfigData(dto);
        BeanUtils.copyProperties(dto, data);
        data.setMtime(DateHelper.getNowSeconds());
        vipConfigDao.update(data);
    }
}
