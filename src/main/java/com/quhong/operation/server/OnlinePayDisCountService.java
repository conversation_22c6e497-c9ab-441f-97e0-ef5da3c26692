package com.quhong.operation.server;

import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.monitor.MonitorSender;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.OnlineDisCountVO;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.PayRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class OnlinePayDisCountService {
    private static final Logger logger = LoggerFactory.getLogger(OnlinePayDisCountService.class);
    private static final String ONLINE_DISCOUNT_KEY = PayRedis.ONLINE_DISCOUNT_KEY;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    protected StringRedisTemplate clusterTemplate;

    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ManagerDao managerDao;



    public OnlineDisCountVO getConfigData(){

        String value = clusterTemplate.opsForValue().get(ONLINE_DISCOUNT_KEY);
        OnlineDisCountVO vo = new OnlineDisCountVO();
        if(value != null){
            String[] timeArr = value.split("-");
            vo.setStartTime(Integer.parseInt(timeArr[0]));
            vo.setEndTime(Integer.parseInt(timeArr[1]));
            vo.setDiscount(Float.parseFloat(timeArr[2]));
            vo.setStatus(Integer.parseInt(timeArr[3]));
        }
        return vo;
    }


    public void updateData(String uid, OnlineDisCountVO dto) {

        int startTime = dto.getStartTime();
        int endTime = dto.getEndTime();
        float discount = dto.getDiscount();
        int status = dto.getStatus();

        if(discount < 0 || discount > 1){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        String discountFormat = String.format("%s-%s-%s-%s", startTime, endTime, discount, status);
        clusterTemplate.opsForValue().set(ONLINE_DISCOUNT_KEY, discountFormat);

        String startFormat = DateHelper.ARABIAN.formatDateTime(new Date(startTime * 1000L));
        String endFormat = DateHelper.ARABIAN.formatDateTime(new Date(endTime * 1000L));
        String statusSwitch = status == 1 ? "开": "关";
        Manager manager = managerDao.getDataByUid(uid);

        String warnFormat = String.format("操作者:%s \n 【GMT+3沙特时间】开始时间: %s, 结束时间: %s, 折扣率: %s, 开关状态: %s", manager.getAccount(), startFormat, endFormat, discount, statusSwitch);
        if(ServerConfig.isProduct()){
            monitorSender.info("diamonds", "线上支付优惠配置更新", warnFormat);
        }else {
            monitorSender.info("ustar_java_exception", "线上支付优惠配置更新", warnFormat);
        }




    }

}
