package com.quhong.service;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.userMonitor.UserMonitorRedis;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class UserMonitorService {

    @Resource
    private UserMonitorRedis userMonitorRedis;

    @Cacheable(value = "isBan", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public boolean isBan(String uid) {
        return userMonitorRedis.isMember(uid);
    }
}
