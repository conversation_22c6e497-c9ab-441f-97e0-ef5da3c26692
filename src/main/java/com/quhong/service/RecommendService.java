package com.quhong.service;

import com.quhong.config.AsyncConfig;
import com.quhong.constant.MomentConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.hash.BloomFilter;
import com.quhong.mongo.dao.MomentDao;
import com.quhong.mongo.data.MomentData;
import com.quhong.redis.*;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
public class RecommendService {
    private static final Logger logger = LoggerFactory.getLogger(RecommendService.class);
    private static final int RECOMMEND_SIZE = 20;
    public static final String SA_RECOMMEND_KEY = "SA";
    private static final int LEVEL1_SIZE = (int) (RECOMMEND_SIZE * 0.2);
    private static final int LEVEL2_SIZE = (int) (RECOMMEND_SIZE * 0.3);
    private static final int LEVEL3_SIZE = (int) (RECOMMEND_SIZE * 0.5);

    @Resource
    private MomentDao momentDao;
    @Resource
    private RecommendRedis recommendRedis;
    @Resource
    private MomentRobotService momentRobotService;
    @Resource
    private FeaturedRedis featuredRedis;
    @Resource
    private MomentTopicFeaturedRedis momentTopicFeaturedRedis;
    @Resource
    private FeaturedSaRedis featuredSaRedis;
    @Resource
    private NewSaRedis newSaRedis;

    public void buildRecommendMomentList() {
        List<MomentData> last3DaysMomentList = momentDao.getLast3DaysMomentList();
        logger.info("start build recommend moment. size={}", last3DaysMomentList.size());
        List<String> scoreAList = new ArrayList<>();
        List<String> scoreBList = new ArrayList<>();
        List<String> scoreCList = new ArrayList<>();
        for (MomentData momentData : last3DaysMomentList) {
            int score = calculateScore(momentData);
            if (0 == score) {
                continue;
            }
            if (score > 70) {
                scoreAList.add(momentData.get_id().toString());
            } else if (score > 20) {
                scoreBList.add(momentData.get_id().toString());
            } else {
                scoreCList.add(momentData.get_id().toString());
            }
        }
        recommendRedis.addRecommendList(scoreAList, 1);
        recommendRedis.addRecommendList(scoreBList, 2);
        recommendRedis.addRecommendList(scoreCList, 3);
    }

    @Async(AsyncConfig.ASYNC_TASK)
    public void buildRecommendMoment(MomentData momentData) {
        int score = calculateScore(momentData);
        String mid = momentData.get_id().toString();
//        recommendRedis.addRecommend(mid, score > 70 ? 1 : score > 20 ? 2 : 3);

        // 沙特新帖子推荐
        String countryCode = ActorUtils.getUpperCaseCountryCode(momentData.getCountry());
        if (SA_RECOMMEND_KEY.equals(countryCode)) {
            newSaRedis.addMoment(momentData);
        }
        if (score > 20) {
            // 机器人自动点赞
            momentRobotService.addAutoLike(mid);
            // 添加到Featured列表中
            featuredRedis.addMoment(momentData);

            // 沙特Featured推荐
            if (SA_RECOMMEND_KEY.equals(countryCode)) {
                featuredSaRedis.addMoment(momentData);
            }
            if (momentData.getTopicId() > 0) {
                momentTopicFeaturedRedis.addMoment(momentData, momentData.getTopicId());
            }
            // 限制条件新增：当单一用户1min内连续发布动态≥2条时，只展示此用户1min内最后一条动态内容，均以2min作为刷新节点
            // 内容展示优先级：同时段内多条动态时，图文内容＞图片内容＞文本内容＞文本带链接
            List<MomentData> recentlyMomentList = momentDao.findRecentlyMomentList(momentData.getUid(), DateHelper.getNowSeconds() - 120);
            if (recentlyMomentList.size() >= 2) {
                for (MomentData data : recentlyMomentList) {
                    if (!ObjectUtils.isEmpty(data.getImgs()) && !ObjectUtils.isEmpty(data.getText())) {
                        // 借用reports字段排序
                        data.setReports(1);
                    } else if (!ObjectUtils.isEmpty(data.getImgs())) {
                        data.setReports(2);
                    } else if (!ObjectUtils.isEmpty(data.getText()) && null == data.getQuote()) {
                        data.setReports(3);
                    } else if (!ObjectUtils.isEmpty(data.getText()) && null != data.getQuote()) {
                        data.setReports(4);
                    } else {
                        data.setReports(Integer.MAX_VALUE - data.getC_time());
                    }
                }
                recentlyMomentList.sort(Comparator.comparing(MomentData::getReports).thenComparing(Comparator.comparing(MomentData::getC_time).reversed()));
                for (int i = 1; i < recentlyMomentList.size(); i++) {
                    MomentData data = recentlyMomentList.get(i);
                    featuredRedis.deleteMoment(featuredRedis.getValue(data));
                    if (SA_RECOMMEND_KEY.equals(countryCode)) {
                        featuredSaRedis.deleteMoment(featuredSaRedis.getValue(data));
                    }
                    logger.info("delete high-frequency moments content. mid={} uid={}", data.get_id().toString(), data.getUid());
                }
            }
        }
    }

    /**
     * <a href="https://www.tapd.cn/20792731/prong/stories/view/1120792731001010683">朋友圈推荐规则</a>
     */
    public int calculateScore(MomentData momentData) {
        int score = 0;
        if (null != momentData.getQuote()) {
            // 转发自己或他人的动态：0分
            if (momentData.getQuote().getType() == MomentConstant.QUOTE_REPOST) {
                score += 0;
            } else if (momentData.getQuote().getType() == MomentConstant.QUOTE_YOUTUBE_LINK) {
                // 带视频内容：40分
                score += 40;
            } else if (momentData.getQuote().getType() == MomentConstant.QUOTE_LINK) {
                // 带链接内容：30分
                score += 30;
            } else if (momentData.getQuote().getType() == MomentConstant.QUOTE_SHARE_ROOM
                    || momentData.getQuote().getType() == MomentConstant.QUOTE_SHARE_ACTIVITY) {
                // 带房间内容：20分
                score += 20;
            }
        }
        // 文本内容
        if (null != momentData.getText()) {
            int count = momentData.getText().length();
            score += (count > 600 ? 100 : count > 250 ? 60 : count > 180 ? 60 : count > 80 ? 40 : count > 20 ? 20 : 0);
        }
        // 图片
        if (null != momentData.getImgs()) {
            int count = momentData.getImgs().size();
            score += (count >= 6 ? 100 : count >= 4 ? 80 : count >= 2 ? 60 : count == 1 ? 40 : 0);
        }
        return score;
    }

    /**
     * 用户进入推荐列表，每次拉取10条动态内容。10条动态评级数量分成：20% A级、30% B级、50% C级随机排序。
     */
    public List<String> getRecommendList(String uid, Set<String> topMidSet) {
        BloomFilter<String> bf = recommendRedis.getRecommendBloomFilter(uid);
        List<String> resultList = new ArrayList<>(RECOMMEND_SIZE);
        fillRecommendList(resultList, bf, 1, LEVEL1_SIZE, topMidSet, uid);
        fillRecommendList(resultList, bf, 2, LEVEL2_SIZE + (LEVEL1_SIZE - resultList.size()), topMidSet, uid);
        fillRecommendList(resultList, bf, 3, LEVEL3_SIZE + (LEVEL1_SIZE + LEVEL2_SIZE - resultList.size()), topMidSet, uid);
        bf.putAll(resultList);
        Collections.shuffle(resultList);
        return resultList;
    }

    private void fillRecommendList(List<String> resultList, BloomFilter<String> bf, int level, int size, Set<String> topMidSet, String uid) {
        int fillCount = 0;
        List<String> levelList = recommendRedis.getRecommendList(level);
        for (String mid : levelList) {
            if (fillCount >= size) {
                break;
            }
            if (!topMidSet.contains(mid) && !bf.mightContain(mid)) {
                resultList.add(mid);
                fillCount++;
            }
        }
        logger.info("fill recommend list uid={} level={} fillCount={}", uid, level, fillCount);
    }
}
