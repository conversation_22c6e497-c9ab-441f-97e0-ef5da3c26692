package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.VipInfoEvent;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.VipFeatureConstant;
import com.quhong.constant.VipHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.UserLevelTaskData;
import com.quhong.data.dto.BubbleLoadDTO;
import com.quhong.data.dto.VipBuyDTO;
import com.quhong.data.dto.VipDTO;
import com.quhong.data.vo.*;
import com.quhong.dto.VipV2ChargeDTO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.enums.UserLevelConstant;
import com.quhong.exception.CommonException;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.msg.obj.RidInfoObject;
import com.quhong.msg.room.RoomQueenVIPActiveMsg;
import com.quhong.mysql.dao.VipConfigDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.VipConfigData;
import com.quhong.mysql.data.VipInfoData;
import com.quhong.mysql.data.VipUserInfoData;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.AppVersionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class VipService {

    private static final Logger logger = LoggerFactory.getLogger(VipService.class);

    private static final List<Integer> VIP_GOLD_LIST = Arrays.asList(1000, 1500, 2000, 2000, 2500, 2500);
    private static final List<Integer> FREE_CALL_TIME_LIST = Arrays.asList(5, 8, 10, 10, 15, 30);
    private static final List<Integer> VIP_GIFT_LIST = Arrays.asList(3, 5, 5, 7, 7, 9);
    private static final List<Integer> VIP_PRICE_NEW = Arrays.asList(1200, 6000, 25000, 60000, 150000, 400000);
    private static final List<Integer> VIP_PRICE_RENEW = Arrays.asList(1200, 6000, 25000, 60000, 150000, 400000);
    private static final List<Integer> VIP_DIAMOND_LIST = Arrays.asList(0, 0, 0, 10000, 20000, 40000);
    private static final Integer VIP_LEVEL_LIMIT = 7;
    private static final List<Integer> VIP_LEVEL_LIST = Arrays.asList(1, 2, 3, 4, 5, 6);

    private static final List<Integer> VIP_WEN_ZI_QI_PAO = Arrays.asList(1, 1, 2, 3, 4, 5, 5, 5, 5, 5, 5);
    private static final List<Integer> VIP_ZI_LIAO_KA = Arrays.asList(1, 2, 3, 4, 4, 5, 5, 5, 5, 5, 5);
    private static final List<Integer> VIP_TOU_XIANG = Arrays.asList(0, 1, 2, 3, 4, 5, 5, 5, 5, 5, 5);
    private static final List<Integer> VIP_MAI_WEI = Arrays.asList(0, 0, 1, 2, 3, 4, 4, 4, 4, 4, 4);
    private static final List<Integer> VIP_HUI_ZHANG = Arrays.asList(1, 1, 1, 2, 2, 2, 3, 3, 4, 4, 4);
    private static final List<Integer> VIP_JIN_FANG_TE_XIAO = Arrays.asList(0, 1, 2, 3, 4, 4, 4, 4, 4, 4, 4);

    private static final List<Integer> BUBBLE_DENSITY = Arrays.asList(1, 2, 3);

    private static final List<Integer> INNER_VIP_LEVEL_LIST = Arrays.asList(1, 2, 3, 4, 5, 6, 10);

    private static final String BUY_VIP_TITLE = "Charge Vip";
    private static final int BUY_VIP_MONEY_TYPE = 100;
    private static final int VIP_GIVE_MONEY_TYPE = 104;
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final int BUY_VIP_EXPIRE_DAY = 30;
    private static final int BUY_VIP_BUBBLE_DAY = 30;
    private static final int BUY_VIP_MIC_DAY = 30;
    private static final int BUY_VIP_JOIN_DAY = 30;
    private static final int BUY_VIP_SCREEN_DAY = 30;
    private static final int BUY_VIP_ROOM_LOCK = 30;
    private static final String VIP_OFFICIAL_TITLE_EN = "Successfully opened %s membership";
    private static final String VIP_OFFICIAL_TITLE_AR = "تم فتح عضوية %s بنجاح";
    private static final String VIP_OFFICIAL_RENEW_TITLE_EN = "Successfully renewed %s membership";
    private static final String VIP_OFFICIAL_RENEW_TITLE_AR = "تم جديد العضوية %s نجاح";
    private static final String VIP_OFFICIAL_BODY_EN = "Congratulations on your becoming a YouStar honorable %s member!";
    private static final String VIP_OFFICIAL_BODY_AR = "تهانينا على كونك عضوًا مشرفًا ل %s في يوستار!";
    private static final String VIP_OFFICIAL_ACTION_EN = "Check the privileges";
    private static final String VIP_OFFICIAL_ACTION_AR = "تحقق من الامتيازات";
    private static final String VIP_OFFICIAL_1_EN = "Tycoon 1";
    private static final String VIP_OFFICIAL_1_AR = "تميز 1";
    private static final String VIP_OFFICIAL_2_EN = "Tycoon 2";
    private static final String VIP_OFFICIAL_2_AR = "تميز 2";
    private static final String VIP_OFFICIAL_3_EN = "Tycoon 3";
    private static final String VIP_OFFICIAL_3_AR = "تميز 3";
    private static final String VIP_OFFICIAL_4_EN = "Tycoon 4";
    private static final String VIP_OFFICIAL_4_AR = "تميز 4";
    private static final String VIP_OFFICIAL_5_EN = "Prince";
    private static final String VIP_OFFICIAL_5_AR = "الأمير";
    private static final String VIP_OFFICIAL_6_EN = "King";
    private static final String VIP_OFFICIAL_6_AR = "الملك";

    // 女王vip相关
    private static final int QUEEN_VIP_PRICE = 130000;
    private static final int QUEEN_VIP_LEVEL = 10;
    private static final List<Integer> QUEEN_NOT_IN_VIP = Arrays.asList(5, 6);
    private static final String CUSTOMER_SERVICE = ServerConfig.isProduct() ? "https://static.youstar.live/customer_service/" : "https://test2.qmovies.tv/customer_service/";
    private static final String PRIVILEGE_DISPLAY = ServerConfig.isProduct() ? "https://static.youstar.live/privilege_display/" : "https://test2.qmovies.tv/privilege_display/";

    private static final String QUEEN_OFFICIAL_TITLE_EN = "Successful activate the Queen membership";
    private static final String QUEEN_OFFICIAL_TITLE_AR = "تم تفعيل عضوية الملكة بنجاح";
    private static final String QUEEN_OFFICIAL_RENEW_TITLE_EN = "Successful Renewal the Queen membership";
    private static final String QUEEN_OFFICIAL_RENEW_TITLE_AR = "تم تجديد عضوية الملكة بنجاح";
    private static final String QUEEN_OFFICIAL_BODY_EN = "Congratulations to you to become a queen member of the Youstar and enjoy 18 membership privileges.";
    private static final String QUEEN_OFFICIAL_BODY_AR = "مبروك لك على أن تصبح عضوًا ملكة في يوستار وتتمتع ب 18 امتيازات عضوية .";
    private static final String QUEEN_OFFICIAL_ACTION_EN = "Check the privileges";
    private static final String QUEEN_OFFICIAL_ACTION_AR = "تحقق من الامتيازات";
    private static final String QUEEN_OFFICIAL_PICTURE = "https://cdn3.qmovies.tv/youstar/queen_offical.png";


    // 气泡适配配置
    private static final Map<Integer, List<String>> BUBBLE_ADAPTER = new HashMap<>();

    @PostConstruct
    public void postInit() {
        if(ServerConfig.isProduct()){
            BUBBLE_ADAPTER.put(71, Arrays.asList("https://cdn3.qmovies.tv/youstar/op_sys_1684757419_angle_test04_bl.png", "https://cdn3.qmovies.tv/youstar/op_sys_1684757419_angle_test04_br.png"));
            BUBBLE_ADAPTER.put(77, Arrays.asList("https://cdn3.qmovies.tv/youstar/op_sys_1684757419_sign_male_left_v826.png", "https://cdn3.qmovies.tv/youstar/op_sys_1684757419_sign_male_right_v826.png"));
            BUBBLE_ADAPTER.put(78, Arrays.asList("https://cdn3.qmovies.tv/youstar/op_sys_1684753340_sign_famale_left_v826_2.png", "https://cdn3.qmovies.tv/youstar/op_sys_1684753340_sign_famale_right_v826_2.png"));
        }else {
            BUBBLE_ADAPTER.put(70, Arrays.asList("https://cdn3.qmovies.tv/youstar/op_sys_1684757419_angle_test04_bl.png", "https://cdn3.qmovies.tv/youstar/op_sys_1684757419_angle_test04_br.png"));
            BUBBLE_ADAPTER.put(77, Arrays.asList("https://cdn3.qmovies.tv/youstar/op_sys_1684757419_sign_male_left_v826.png", "https://cdn3.qmovies.tv/youstar/op_sys_1684757419_sign_male_right_v826.png"));
            BUBBLE_ADAPTER.put(78, Arrays.asList("https://cdn3.qmovies.tv/youstar/op_sys_1684753340_sign_famale_left_v826_2.png", "https://cdn3.qmovies.tv/youstar/op_sys_1684753340_sign_famale_right_v826_2.png"));
        }

    }


    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private JoinSourceDao joinSourceDao;
    @Resource
    private JoinCartonDao joinCartonDao;
    @Resource
    private BuddleSourceDao buddleSourceDao;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private VipAwardDao vipAwardDao;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    protected UserLevelTaskService levelTaskService;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    protected RoomWebSender roomWebSender;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private BubbleDao bubbleDao;
    @Resource
    private VipMicFrameDao vipMicFrameDao;
    @Resource
    private FloatScreenDao floatScreenDao;
    @Resource
    private VipV2Service vipV2Service;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private VipConfigDao vipConfigDao;

    private void fillVipInfo(String uid, VipShowPageVO vo){
        VipInfoData vipInfo = vipInfoDao.findVipInfo(uid);
        int currentTime = DateHelper.getNowSeconds();

        if(vipInfo != null){

            int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
            int leftTime = vipEndTime - currentTime;

            int vipBuyTime = (int) (vipInfo.getVipBuyTime().getTime() / 1000);

            if(leftTime > 0){
                vo.setVip_level(vipInfo.getVipLevel());
                vo.setVip_buy_time(vipBuyTime);
                vo.setVip_end_time(vipEndTime);
                int leftDays = leftTime / 86400;
                vo.setLeftdays(leftDays);
                vo.setPopwindow(leftDays == 3 || leftDays == 1 ? 1: 0);

                VipShowPageVO.OtherInfo otherInfo = new VipShowPageVO.OtherInfo();
                int vIndex = vipInfo.getVipLevel() - 1;

                otherInfo.setHuizhang(VIP_WEN_ZI_QI_PAO.get(vIndex));
                otherInfo.setZiliaoka(VIP_ZI_LIAO_KA.get(vIndex));
                otherInfo.setTouxiang(VIP_TOU_XIANG.get(vIndex));
                otherInfo.setMaiwei(VIP_MAI_WEI.get(vIndex));
                otherInfo.setHuizhang(VIP_HUI_ZHANG.get(vIndex));
                otherInfo.setTanchu(VIP_JIN_FANG_TE_XIAO.get(vIndex));
                vo.setOther(otherInfo);

            }
        }else {
            vo.setVip_level(0);
        }
    }

    /**
     * vip信息页
     */

    public VipShowPageVO getVipShowPage(VipDTO dto){

        String userId = dto.getUid();
        ActorData userInfo = actorDao.getActorDataFromCache(userId);

        VipShowPageVO vo = new VipShowPageVO();
        int currentTime = DateHelper.getNowSeconds();

        vo.setGold_list(VIP_GOLD_LIST);
        vo.setFree_call_list(FREE_CALL_TIME_LIST);
        vo.setGift_list(VIP_GIFT_LIST);
        vo.setVip_price_new(VIP_PRICE_NEW);
        vo.setVip_price_renew(VIP_PRICE_RENEW);
        vo.setVip_diamond_give(VIP_DIAMOND_LIST);
        vo.setVip_level_limit(VIP_LEVEL_LIMIT-1);
        vo.setNow(currentTime);
        vo.setGender(userInfo.getFb_gender() == 1? 1 : 2);

        fillVipInfo(userId, vo);

        return vo;

    }

    /**
     * 购买vip
     */
    // public VipBuyVO buyVip(VipBuyDTO dto){
    //     int buyVipLevel = dto.getVlvlbuy();
    //     if(!VIP_LEVEL_LIST.contains(buyVipLevel)){
    //         throw new CommonException(HttpCode.PARAM_ERROR);
    //     }
    //
    //     String userId = dto.getUid();
    //     VipV2BuyDTO vipV2BuyDTO = new VipV2BuyDTO();
    //     BeanUtils.copyProperties(dto, vipV2BuyDTO);
    //     vipV2BuyDTO.setVipLevel(buyVipLevel);
    //     VipV2BuyVO vipV2BuyVO = vipV2Service.vipBuy(vipV2BuyDTO);
    //     // officialMsgPush(userId, buyVipLevel, userInfo.getSlang(), sameLevel);
    //
    //     VipBuyVO vo = new VipBuyVO();
    //     vo.setVip_level(buyVipLevel);
    //     vo.setVip_buy_time(vipV2BuyVO.getBuyTime());
    //     vo.setVip_end_time(vipV2BuyVO.getExpireTime());
    //     vo.setVip_time_total(0);
    //     vo.setNow(vipV2BuyVO.getNow());
    //     fillVOWearRide(userId, vo);
    //     return vo;
    // }

    /**
     * 867之前的购买vip
     */
    public VipBuyVO buyVip(VipBuyDTO dto){
        int buyVipLevel = dto.getVlvlbuy();
        if(!VIP_LEVEL_LIST.contains(buyVipLevel)){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        String userId = dto.getUid();
        long vipUpgrade = actorConfigDao.getLongUserConfig(userId, ActorConfigDao.VIP_UPGRADE, 0);
        if (vipUpgrade > 0) {
            throw new CommonException(VipHttpCode.UPDATE_APP_VIP);
        }
        ActorData userInfo = actorDao.getActorDataFromCache(userId);
        // 性别校验：女性不可购买VIP5、VIP6，男性不可购买Queen VIP
        if (userInfo != null) {
            int userGender = userInfo.getFb_gender(); // 1: 男性  2: 女性
            if (userGender == 2 && (buyVipLevel == 5 || buyVipLevel == 6)) {
                // 女性不可购买VIP5、VIP6
                throw new CommonException(VipHttpCode.NOT_BUY_VIP, "Female users cannot purchase VIP5 or VIP6");
            }
            if (userGender == 1 && buyVipLevel == 10) {
                // 男性不可购买Queen VIP
                throw new CommonException(VipHttpCode.NOT_BUY_VIP, "Male users cannot purchase Queen VIP");
            }
        }

        int vipLevelIndex = buyVipLevel - 1;
        int currentTime = DateHelper.getNowSeconds();
        int giveVipGift = VIP_GIFT_LIST.get(vipLevelIndex);
        int goldTotal = VIP_GOLD_LIST.get(vipLevelIndex);
        boolean sameLevel = false;
        int vipEndTime = 0;
        int newVipEndTime;
        int newVipBuyTime;
        int vipTotal = 1;
        int leftTime = 0;
        int currentVipLevel = 0;
        Date currentDate = new Date();

        VipInfoData vipInfo = vipInfoDao.findVipInfo(userId);

        if(vipInfo != null){
            vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
            leftTime = vipEndTime - currentTime;
            currentVipLevel = vipInfo.getVipLevel();
            if(leftTime > 0 && buyVipLevel < currentVipLevel){
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
        }


        // 1、 扣费
        String recordDesc = "charge vip" + buyVipLevel;
        int costMoney = VIP_PRICE_NEW.get(vipLevelIndex);
        distributionService.deductDiamondsReward(userId, BUY_VIP_MONEY_TYPE, costMoney, BUY_VIP_TITLE, recordDesc);


        // vip信息修改
        synchronized (stringPool.intern(VipFeatureConstant.VIP_ACTIVE_KEY + userId)) {

            if(vipInfo != null) {

                if(leftTime > 0 && currentVipLevel == buyVipLevel){
                    newVipEndTime = vipEndTime + BUY_VIP_EXPIRE_DAY * 86400;
                    newVipBuyTime = (int) (vipInfo.getVipBuyTime().getTime()  / 1000);
                    sameLevel = true;

                }else {

                    newVipEndTime = currentTime + BUY_VIP_EXPIRE_DAY * 86400;
                    newVipBuyTime = currentTime;
                }



                vipTotal = vipInfo.getVipTimeTotal() + 1;
                int giftRemain = vipInfo.getVipGiftRemain();
                int curRemain = giftRemain + giveVipGift;
                Date vipEndDate = new Timestamp(newVipEndTime * 1000L);
                Date vipBuyDate = new Timestamp(newVipBuyTime * 1000L);


                logger.info("vipInfo exist info uid: {}, vipEndTime:{}, vipTotal: {}, giftRemain:{}, curRemain:{}", userId, vipEndTime, vipTotal, giftRemain, curRemain);
                vipInfoDao.updateVipInfo(userId, curRemain, vipBuyDate, vipEndDate, vipTotal, buyVipLevel);

            }else {
                newVipEndTime = currentTime + BUY_VIP_EXPIRE_DAY * 86400;
                newVipBuyTime = currentTime;
                Date vipEndDate = new Timestamp(newVipEndTime * 1000L);
                Date vipBuyDate = new Timestamp(newVipBuyTime * 1000L);

                logger.info("vipInfo not find info uid: {}, vipEndTime:{}, vipTotal: {}", userId, vipEndTime, vipTotal);
                vipInfoDao.insertVipInfo(userId, buyVipLevel, vipTotal, vipBuyDate, vipEndDate, giveVipGift, goldTotal, currentDate);

            }

            vipInfoDao.setVipLevelRedis(userId, buyVipLevel, newVipEndTime);
        }

        // 3、相关奖励下发
        boolean finalSameLevel = sameLevel;
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                distributeVipAward(userId, buyVipLevel, finalSameLevel, true);
            }
        });



        // 4、事件埋点
        vipEventRecord(userId, buyVipLevel, newVipBuyTime, newVipEndTime, costMoney);
        officialMsgPush(userId, buyVipLevel, userInfo.getSlang(), sameLevel);

        VipBuyVO vo = new VipBuyVO();

        vo.setVip_level(buyVipLevel);
        vo.setVip_buy_time(newVipBuyTime);
        vo.setVip_end_time(newVipEndTime);
        vo.setVip_time_total(vipTotal);
        vo.setNow(currentTime);
        fillVOWearRide(userId, vo);
        return vo;

    }


    private void fillVOWearRide(String uid, VipBuyVO vo){
        JoinCartonData joinData = joinCartonDao.findData(uid, 1);
        if(joinData != null){
            JoinSourceData joinSource = joinSourceDao.getSourceData(joinData.getJoin_carton_id());
            if(joinSource != null){
                VipBuyVO.WearRide wearRide = new VipBuyVO.WearRide();
                wearRide.setId(joinSource.getJoin_carton_id());
                wearRide.setUrl(joinSource.getSource_url());
                wearRide.setSource_type(joinSource.getSource_type());
                wearRide.setMd5(joinSource.getSource_md5());
                vo.setWearRide(wearRide);
            }

        }
    }


    // 3、vip相关奖励下发
    private void distributeVipAward(String uid, int buyVipLevel, boolean sameLevel, boolean diamondFlag){
        VipAwardData vipAward = vipAwardDao.findAwardByVipLevel(buyVipLevel);
        if(vipAward == null){
            return;
        }

        int giveDiamond = vipAward.getGiveDiamond();   // 赠送钻石数
        int invisible = vipAward.getInvisible();     // 是否可以隐身访问
        int giveGift = vipAward.getGiveGift();     // 赠送礼物数
        int roomLock = vipAward.getRoomLock();     // 是否赠送房间锁
        List<VipAwardData.SourceInfo> bubbleList = vipAward.getBubbleList();     // 赠送气泡id
        List<VipAwardData.SourceInfo> micList = vipAward.getMicList();     // 赠送麦位框id
        List<VipAwardData.SourceInfo> joinList = vipAward.getJoinList();     // 赠送坐骑id
        List<VipAwardData.SourceInfo> screenList = vipAward.getScreenList();     // 赠送浮萍id

        List<Integer> bubbleRemoveList = vipAward.getBubbleRemoveList();
        List<Integer> micRemoveList = vipAward.getMicRemoveList();
        List<Integer> joinRemoveList = vipAward.getJoinRemoveList();
        List<Integer> screenRemoveList = vipAward.getScreenRemoveList();

        if(giveDiamond > 0 && diamondFlag){
            String title = "Charge Vip";
            String recordDesc = "charge vip" + buyVipLevel;
            distributionService.sendDiamondsReward(uid, VIP_GIVE_MONEY_TYPE, giveDiamond, title, recordDesc);
        }

        if(invisible == 1){
            actorDao.updateGeneralConf(uid, "invisible", 1);
        }

        if(giveGift > 0){
            vipInfoDao.incRedisVipGiftRemain(uid, giveGift);
        }

        if(roomLock == 1){
            distributionService.sendResourceReward(uid, 0, BaseDataResourcesConstant.TYPE_ROOM_LOCK,
                    BaseDataResourcesConstant.ACTION_GET, BUY_VIP_ROOM_LOCK, 0, "buy vip");
        }


        // 先移除的资源
        if(bubbleRemoveList != null && bubbleRemoveList.size() > 0){
            for (Integer sourceId : bubbleRemoveList) {
                BubbleData existData = bubbleDao.findData(uid, sourceId);
                if(existData == null){
                    continue;
                }
                distributionService.sendResourceReward(uid, sourceId, BaseDataResourcesConstant.TYPE_BUDDLE,
                        BaseDataResourcesConstant.ACTION_DELETE, 0, 0, "buy vip");
            }
        }


        if(micRemoveList != null && micRemoveList.size() > 0){
            for (Integer sourceId : micRemoveList) {
                VipMicFrameData existData = vipMicFrameDao.findData(uid, sourceId);
                if(existData == null){
                    continue;
                }
                distributionService.sendResourceReward(uid, sourceId, BaseDataResourcesConstant.TYPE_MIC,
                        BaseDataResourcesConstant.ACTION_DELETE, 0, 0, "buy vip");
            }
        }

        if(joinRemoveList != null && joinRemoveList.size() > 0 && !sameLevel){
            for (Integer sourceId : joinRemoveList) {
                JoinCartonData existData = joinCartonDao.findCartoonData(uid, sourceId);
                if(existData == null){
                    continue;
                }
                distributionService.sendResourceReward(uid, sourceId, BaseDataResourcesConstant.TYPE_RIDE,
                        BaseDataResourcesConstant.ACTION_DELETE, 0, 0, "buy vip");
            }
        }

        if(screenRemoveList != null && screenRemoveList.size() > 0){
            for (Integer sourceId : screenRemoveList) {
                FloatScreenData existData = floatScreenDao.findData(uid, sourceId);
                if(existData == null){
                    continue;
                }
                distributionService.sendResourceReward(uid, sourceId, BaseDataResourcesConstant.TYPE_FLOAT_SCREEN,
                        BaseDataResourcesConstant.ACTION_DELETE, 0, 0, "buy vip");
            }
        }


        // 再下发资源

        if(bubbleList != null && bubbleList.size() > 0){
            for (VipAwardData.SourceInfo sourceInfo : bubbleList) {
                int action = sourceInfo.getAutoWear() == 1 ? BaseDataResourcesConstant.ACTION_GET_WEAR : BaseDataResourcesConstant.ACTION_GET;
                distributionService.sendResourceReward(uid, sourceInfo.getSourceId(), BaseDataResourcesConstant.TYPE_BUDDLE,
                        action, BUY_VIP_BUBBLE_DAY, 0, "buy vip");
            }
        }

        if(micList != null && micList.size() > 0){
            for (VipAwardData.SourceInfo sourceInfo : micList) {
                int action = sourceInfo.getAutoWear() == 1 ? BaseDataResourcesConstant.ACTION_GET_WEAR : BaseDataResourcesConstant.ACTION_GET;
                distributionService.sendResourceReward(uid, sourceInfo.getSourceId(), BaseDataResourcesConstant.TYPE_MIC,
                        action, BUY_VIP_MIC_DAY, 0, "buy vip");
            }
        }

        if(joinList != null && joinList.size() > 0){
            for (VipAwardData.SourceInfo sourceInfo : joinList) {
                int action = sourceInfo.getAutoWear() == 1 ? BaseDataResourcesConstant.ACTION_GET_WEAR : BaseDataResourcesConstant.ACTION_GET;
                distributionService.sendResourceReward(uid, sourceInfo.getSourceId(), BaseDataResourcesConstant.TYPE_RIDE,
                        action, BUY_VIP_JOIN_DAY, 0, "buy vip");
            }
        }

        if(screenList != null && screenList.size() > 0){
            for (VipAwardData.SourceInfo sourceInfo : screenList) {
                int action = sourceInfo.getAutoWear() == 1 ? BaseDataResourcesConstant.ACTION_GET_WEAR : BaseDataResourcesConstant.ACTION_GET;
                distributionService.sendResourceReward(uid, sourceInfo.getSourceId(), BaseDataResourcesConstant.TYPE_FLOAT_SCREEN,
                        action, BUY_VIP_SCREEN_DAY, 0, "buy vip");
            }
        }

    }

    // 事件埋点
    private void vipEventRecord(String uid, int vipLevel, int vipBuyTime, int vipEndTime, int vipCost){
        VipInfoEvent event = new VipInfoEvent();
        event.setUid(uid);
        event.setVip_level(vipLevel);
        event.setVip_buy_time(vipBuyTime);
        event.setVip_end_time(vipEndTime);
        event.setVip_diamonds_cost(vipCost);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));

        levelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UserLevelConstant.RECHARGE_VIP, Math.abs(vipCost)));
    }

    // 官方消息
    private void officialMsgPush(String uid, int vipLevel, int slang, boolean renew) {

        try {
            String title;
            String body;
            String action;
            String picture;
            int actionType = vipLevel == QUEEN_VIP_LEVEL? 8 : 9;

            if(vipLevel == QUEEN_VIP_LEVEL){
                if(renew){
                    title = slang == SLangType.ARABIC ? QUEEN_OFFICIAL_RENEW_TITLE_AR : QUEEN_OFFICIAL_RENEW_TITLE_EN;
                }else {
                    title = slang == SLangType.ARABIC ? QUEEN_OFFICIAL_TITLE_AR : QUEEN_OFFICIAL_TITLE_EN;
                }

                body = slang == SLangType.ARABIC ? QUEEN_OFFICIAL_BODY_AR : QUEEN_OFFICIAL_BODY_EN;
                action = slang == SLangType.ARABIC ? QUEEN_OFFICIAL_ACTION_AR : QUEEN_OFFICIAL_ACTION_EN;
                picture = QUEEN_OFFICIAL_PICTURE;

            }else {

                String vipDesc;

                switch (vipLevel){
                    case 1:
                        vipDesc = slang == SLangType.ARABIC?VIP_OFFICIAL_1_AR : VIP_OFFICIAL_1_EN;
                        break;
                    case 2:
                        vipDesc = slang == SLangType.ARABIC?VIP_OFFICIAL_2_AR : VIP_OFFICIAL_2_EN;
                        break;
                    case 3:
                        vipDesc = slang == SLangType.ARABIC?VIP_OFFICIAL_3_AR : VIP_OFFICIAL_3_EN;
                        break;
                    case 4:
                        vipDesc = slang == SLangType.ARABIC?VIP_OFFICIAL_4_AR : VIP_OFFICIAL_4_EN;
                        break;
                    case 5:
                        vipDesc = slang == SLangType.ARABIC?VIP_OFFICIAL_5_AR : VIP_OFFICIAL_5_EN;
                        break;
                    default:
                        vipDesc = slang == SLangType.ARABIC?VIP_OFFICIAL_6_AR : VIP_OFFICIAL_6_EN;
                }

                if(renew){
                    title = slang == SLangType.ARABIC ? VIP_OFFICIAL_RENEW_TITLE_AR : VIP_OFFICIAL_RENEW_TITLE_EN;
                }else {
                    title = slang == SLangType.ARABIC ? VIP_OFFICIAL_TITLE_AR : VIP_OFFICIAL_TITLE_EN;

                }

                title = String.format(title, vipDesc);
                body = slang == SLangType.ARABIC ? VIP_OFFICIAL_BODY_AR : VIP_OFFICIAL_BODY_EN;
                body = String.format(body, vipDesc);
                action = slang == SLangType.ARABIC ? VIP_OFFICIAL_ACTION_AR : VIP_OFFICIAL_ACTION_EN;
                picture = String.format("https://cdn3.qmovies.tv/youstar/banner_tycoon%s.png", vipLevel);

            }


            OfficialData officialData = new OfficialData();
            officialData.setSubTitle("");
            officialData.setTo_uid(uid);
            officialData.setCtime(DateHelper.getNowSeconds());
            officialData.setAtype(actionType);
            officialData.setTitle(title);
            officialData.setBody(body);
            officialData.setAct(action);
            officialData.setPicture(picture);
            officialDao.save(officialData);
            if (officialData.get_id() != null) {
                noticeNewDao.save(new NoticeNewData(uid, officialData.get_id().toString()));

                OfficialPushMsg msg = new OfficialPushMsg();
                msg.setTitle(officialData.getTitle());
                msg.setBody(officialData.getBody());
                roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
            }
        }catch (Exception e){
            logger.error("officialMsgPush error: {}", e.getMessage());
        }
    }


    /**
     * 女王vip信息页
     */

    private void fillQueenVipInfo(String uid, VipQueenShowVO vo){
        VipInfoData vipInfo = vipInfoDao.findVipInfo(uid);
        int currentTime = DateHelper.getNowSeconds();
        vo.setNow(currentTime);

        if(vipInfo != null){

            int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
            int leftTime = vipEndTime - currentTime;

            int vipBuyTime = (int) (vipInfo.getVipBuyTime().getTime() / 1000);

            if(leftTime > 0){
                vo.setVip_level(vipInfo.getVipLevel());
                vo.setVip_buy_time(vipBuyTime);
                vo.setVip_end_time(vipEndTime);
                int leftDays = leftTime / 86400;
                vo.setLeftdays(leftDays);
                vo.setPopwindow(leftDays == 3 || leftDays == 1 ? 1: 0);
            }
        }else {
            vo.setVip_level(0);
        }

        vo.setQueen_vip(vo.getVip_level() == QUEEN_VIP_LEVEL? 1: 0);

    }

    public VipQueenShowVO getQueenVipShowPage(VipDTO dto){

        String userId = dto.getUid();
        ActorData userInfo = actorDao.getActorDataFromCache(userId);

        VipQueenShowVO vo = new VipQueenShowVO();
        int currentTime = DateHelper.getNowSeconds();

        vo.setVip_price_new(QUEEN_VIP_PRICE);
        vo.setVip_price_renew(QUEEN_VIP_PRICE);
        vo.setNow(currentTime);
        vo.setGender(userInfo.getFb_gender() == 1? 1 : 2);

        fillQueenVipInfo(userId, vo);
        vo.setCustomerService(CUSTOMER_SERVICE);
        vo.setPrivilegeDisplay(PRIVILEGE_DISPLAY);

        return vo;

    }


    /**
     * 购买女王vip
     */
    // public VipBuyVO vipBuyQueen(VipDTO dto){
    //     String userId = dto.getUid();
    //
    //     ActorData userInfo = actorDao.getActorData(userId);
    //     if(userInfo.getFb_gender() == 1){
    //         throw new CommonException(HttpCode.PARAM_ERROR);
    //     }
    //     VipV2BuyDTO vipV2BuyDTO = new VipV2BuyDTO();
    //     BeanUtils.copyProperties(dto, vipV2BuyDTO);
    //     vipV2BuyDTO.setVipLevel(QUEEN_VIP_LEVEL);
    //     VipV2BuyVO vipV2BuyVO = vipV2Service.vipBuy(vipV2BuyDTO);
    //     // officialMsgPush(userId, QUEEN_VIP_LEVEL, userInfo.getSlang(), sameLevel);
    //     queenRoomMsg(userInfo);
    //
    //     VipBuyVO vo = new VipBuyVO();
    //     vo.setVip_level(vipV2BuyVO.getVipLevel());
    //     vo.setVip_buy_time(vipV2BuyVO.getBuyTime());
    //     vo.setVip_end_time(vipV2BuyVO.getExpireTime());
    //     vo.setVip_time_total(0);
    //     vo.setNow(vipV2BuyVO.getNow());
    //     fillVOWearRide(userId, vo);
    //     return vo;
    //
    // }


    public VipBuyVO vipBuyQueen(VipDTO dto){
        String userId = dto.getUid();

        ActorData userInfo = actorDao.getActorData(userId);
        if(userInfo.getFb_gender() == 1){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        long vipUpgrade = actorConfigDao.getLongUserConfig(userId, ActorConfigDao.VIP_UPGRADE, 0);
        if (vipUpgrade > 0) {
            throw new CommonException(VipHttpCode.UPDATE_APP_VIP);
        }
        int currentTime = DateHelper.getNowSeconds();
        boolean sameLevel = false;
        int vipEndTime = 0;
        int newVipEndTime;
        int newVipBuyTime;
        int vipTotal = 1;
        int leftTime = 0;
        int currentVipLevel = 0;
        Date currentDate = new Date();

        VipInfoData vipInfo = vipInfoDao.findVipInfo(userId);
        if(vipInfo != null) {
            vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
            leftTime = vipEndTime - currentTime;
            currentVipLevel = vipInfo.getVipLevel();
            if(leftTime > 0 &&  QUEEN_NOT_IN_VIP.contains(currentVipLevel)){
                throw new CommonException(HttpCode.PARAM_ERROR);
            }

            if(leftTime > 0 && QUEEN_VIP_LEVEL < currentVipLevel){
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
        }


        VipBuyVO vo = new VipBuyVO();
        // 1、 扣费
        String recordDesc = "charge queen vip";
        distributionService.deductDiamondsReward(userId, BUY_VIP_MONEY_TYPE, QUEEN_VIP_PRICE, BUY_VIP_TITLE, recordDesc);

        // 2、修改vip信息
        synchronized (stringPool.intern(VipFeatureConstant.VIP_ACTIVE_KEY + userId)) {
            if(vipInfo != null) {
                if(leftTime > 0 && currentVipLevel == QUEEN_VIP_LEVEL){
                    newVipEndTime = vipEndTime + BUY_VIP_EXPIRE_DAY * 86400;
                    newVipBuyTime = (int) (vipInfo.getVipBuyTime().getTime()  / 1000);
                    sameLevel = true;
                }else {

                    newVipEndTime = currentTime + BUY_VIP_EXPIRE_DAY * 86400;
                    newVipBuyTime = currentTime;
                }

                vipTotal = vipInfo.getVipTimeTotal() + 1;
                int giftRemain = vipInfo.getVipGiftRemain();
                Date vipEndDate = new Timestamp(newVipEndTime * 1000L);
                Date vipBuyDate = new Timestamp(newVipBuyTime * 1000L);
                vipInfoDao.updateVipInfo(userId, giftRemain, vipBuyDate, vipEndDate, vipTotal, QUEEN_VIP_LEVEL);

            }else {
                newVipEndTime = currentTime + BUY_VIP_EXPIRE_DAY * 86400;
                newVipBuyTime = currentTime;
                Date vipEndDate = new Timestamp(newVipEndTime * 1000L);
                Date vipBuyDate = new Timestamp(newVipBuyTime * 1000L);
                vipInfoDao.insertVipInfo(userId, QUEEN_VIP_LEVEL, vipTotal, vipBuyDate, vipEndDate, 0, 0, currentDate);
            }

            vipInfoDao.setVipLevelRedis(userId, QUEEN_VIP_LEVEL, newVipEndTime);
        }

        // 3、相关奖励下发

        boolean finalSameLevel = sameLevel;
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                distributeVipAward(userId, QUEEN_VIP_LEVEL, finalSameLevel, true);
            }
        });

        // 4、事件埋点
        vipEventRecord(userId, QUEEN_VIP_LEVEL, newVipBuyTime, newVipEndTime, QUEEN_VIP_PRICE);
        officialMsgPush(userId, QUEEN_VIP_LEVEL, userInfo.getSlang(), sameLevel);
        queenRoomMsg(userInfo);

        vo.setVip_level(QUEEN_VIP_LEVEL);
        vo.setVip_buy_time(newVipBuyTime);
        vo.setVip_end_time(newVipEndTime);
        vo.setVip_time_total(vipTotal);
        vo.setNow(currentTime);
        fillVOWearRide(userId, vo);
        return vo;
    }


    private void queenRoomMsg(ActorData userInfo){
        String userId = userInfo.getUid();
        String roomId = roomPlayerRedis.getActorRoomStatus(userId);
        String fillRoomId = roomId == null? "all": "all_" + roomId;

        RoomQueenVIPActiveMsg activeMsg = new RoomQueenVIPActiveMsg();
        activeMsg.setUid(userId);
        activeMsg.setName(userInfo.getName());
        activeMsg.setRid(String.valueOf(userInfo.getRid()));
        activeMsg.setRidInfo(new RidInfoObject(userInfo.getRidData()));
        activeMsg.setHead(ImageUrlGenerator.generateRoomUserUrl(userInfo.getHead()));
        activeMsg.setTitle(String.format("Congratulations to %s for activating the Queen privilege.", userInfo.getName()));
        activeMsg.setTitle_ar(String.format("ألف مبروك لـ %s لتفعيل امتياز الملكة.", userInfo.getName()));
        roomWebSender.sendRoomWebMsg(fillRoomId, userId, activeMsg, true);
    }


    /**
     * vip 购买内部服务
     */
    private void verifyParam(String uid, int buyVipLevel, ActorData userInfo, VipUserInfoData vipInfo){
        if(!INNER_VIP_LEVEL_LIST.contains(buyVipLevel)){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        if(userInfo == null){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        int userGender = userInfo.getFb_gender();  // 1: 男  2: 女
        if(buyVipLevel == QUEEN_VIP_LEVEL && userGender == 1){
            throw new CommonException(VipHttpCode.NOT_BUY_VIP);
        }

        int currentTime = DateHelper.getNowSeconds();
        if(vipInfo != null){
            int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
            int leftTime = vipEndTime - currentTime;
            int currentVipLevel = vipInfo.getVipLevel();
            if(leftTime > 0){
                if ((currentVipLevel <= VipFeatureConstant.VIP_LEVEL_6 && buyVipLevel < currentVipLevel) || (currentVipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN && buyVipLevel <= VipFeatureConstant.VIP_LEVEL_4)) {
                    throw new CommonException(VipHttpCode.VIP_DOWN_GRADE, currentVipLevel);
                }
            }
        }
    }


    public void vipCharge(VipV2ChargeDTO dto){
        int chargeVipLevel = dto.getVipLevel();
        String userId = dto.getUid();
        ActorData userInfo = actorDao.getActorData(userId);
        VipUserInfoData vipInfo = vipInfoDao.findVipInfoByUid(userId);

        verifyParam(userId, chargeVipLevel, userInfo, vipInfo);
        int currentTime = DateHelper.getNowSeconds();
        Map<Integer, VipConfigData> vipLevelConfigMap = vipConfigDao.selectList().stream().collect(Collectors.toMap(VipConfigData::getVipLevel, Function.identity()));
        VipConfigData vipConfig = vipLevelConfigMap.get(chargeVipLevel);
        if(vipConfig == null){
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        boolean sameLevel = false;
        int giveVipGift = vipConfig.getVipGift();

        int newVipEndTime;
        int newVipBuyTime;
        int vipTotal = 1;
        int currentVipLevel = 0;
        Date currentDate = new Date();

        // vip信息修改
        synchronized (stringPool.intern(VipFeatureConstant.VIP_ACTIVE_KEY + userId)) {
            if(vipInfo != null) {
                int curRemain = giveVipGift;
                int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
                int leftTime = vipEndTime - currentTime;
                if(leftTime > 0 && currentVipLevel == chargeVipLevel){
                    newVipEndTime = vipEndTime + BUY_VIP_EXPIRE_DAY * 86400;
                    newVipBuyTime = (int) (vipInfo.getVipBuyTime().getTime()  / 1000);
                    sameLevel = true;
                    curRemain += vipInfo.getVipGiftRemain();
                }else {
                    newVipEndTime = currentTime + BUY_VIP_EXPIRE_DAY * 86400;
                    newVipBuyTime = currentTime;
                }
                vipInfo.setVipEndTime(new Date(newVipEndTime * 1000L));
                vipInfo.setVipBuyTime(new Date(newVipBuyTime * 1000L));
                vipInfo.setVipTimeTotal(vipTotal);
                vipInfo.setVipLevel(chargeVipLevel);
                vipInfo.setVipSource(VipFeatureConstant.SOURCE_ADMIN_GIVE_VIP);
                vipInfo.setVipGiftRemain(curRemain);
                vipInfo.setVipExpireStatus(0);
                vipInfoDao.update(vipInfo);
                logger.info("vipInfo exist info uid: {}, vipEndTime:{}, vipTotal: {}, curRemain:{}", userId, vipEndTime, vipTotal, curRemain);
            }else {
                newVipEndTime = currentTime + BUY_VIP_EXPIRE_DAY * 86400;
                newVipBuyTime = currentTime;
                vipInfo = new VipUserInfoData();
                vipInfo.setUid(userId);
                vipInfo.setVipLevel(chargeVipLevel);
                vipInfo.setVipTimeTotal(vipTotal);
                vipInfo.setVipBuyTime(new Date(currentTime * 1000L));
                vipInfo.setVipEndTime(new Date(newVipEndTime * 1000L));
                vipInfo.setVipGiftUsed(0);
                vipInfo.setVipGoldTotal(0);
                vipInfo.setVipExpireStatus(0);
                vipInfo.setVipGiftRemain(giveVipGift);
                vipInfo.setVipSource(VipFeatureConstant.SOURCE_ADMIN_GIVE_VIP);
                vipInfo.setLastBenefitGetTime(currentDate);
                vipInfo.setCtime(currentDate);
                vipInfo.setMtime(currentDate);
                logger.info("Creating new VIP info via card activation - uid: {}, vipEndTime: {}, vipTotal: {}", userId, newVipEndTime, vipTotal);
                vipInfoDao.insert(vipInfo);
            }
            vipInfoDao.setVipLevelRedis(userId, chargeVipLevel, newVipEndTime);
        }

        // 相关奖励下发
        boolean finalSameLevel = sameLevel;
        boolean diamondFlag = dto.getFreeDiamond() > 1;
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                distributeVipAward(userId, chargeVipLevel, finalSameLevel, diamondFlag);
            }
        });
        // 事件埋点
        vipEventRecord(userId, chargeVipLevel, newVipBuyTime, newVipEndTime, 0);
        officialMsgPush(userId, chargeVipLevel, userInfo.getSlang(), sameLevel);
    }

    /**
     * 资源动画下载
     */
    @Cacheable(value = "joinResourceDownload", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public JoinResourceVO joinResourceDownload(VipDTO dto){
        JoinResourceVO vo = new JoinResourceVO();
        List<JoinResourceVO.JoinInfo> joinList = new ArrayList<>();

        List<JoinSourceData> joinSourceList = joinSourceDao.getValidJoinSource();
        for (JoinSourceData join : joinSourceList) {
            JoinResourceVO.JoinInfo joinInfo = new JoinResourceVO.JoinInfo();
            joinInfo.setJoin_carton_id(join.getJoin_carton_id());
            joinInfo.setJoin_icon(join.getJoin_icon());
            joinInfo.setName(join.getName());
            joinInfo.setSource_url(dto.getOs() == 0? join.getSource_url() : join.getIos_url());
            joinInfo.setSource_md5(dto.getOs() == 0? join.getSource_md5() : join.getIos_md5());
            joinInfo.setHot(join.getHot());
            joinInfo.setSource_type(join.getSource_type());
            joinList.add(joinInfo);
        }

        vo.setList(joinList);
        return vo;
    }

    /**
     * 气泡资源下载
     */
    @Cacheable(value = "bubbleDownload", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public BubbleVO bubbleDownload(BubbleLoadDTO dto){
        BubbleVO vo = new BubbleVO();
        List<BubbleVO.BubbleInfo> bubbleList = new ArrayList<>();

        List<BuddleSourceData> bubbleSourceList = buddleSourceDao.getValidBubbleSource();
        for (BuddleSourceData bubble : bubbleSourceList) {
            BubbleVO.BubbleInfo bubbleInfo = new BubbleVO.BubbleInfo();
            bubbleInfo.setBuddle_id(bubble.getBuddle_id());
            bubbleInfo.setBuddle_color(bubble.getBuddle_color());

            List<String> adapterList = BUBBLE_ADAPTER.get(bubble.getBuddle_id());

            if(AppVersionUtils.versionCheck(845, dto) && adapterList != null ){
                bubbleInfo.setBuddle_tl(bubble.getBuddle_tl());
                bubbleInfo.setBuddle_tr(bubble.getBuddle_tr());
                bubbleInfo.setBuddle_bl(adapterList.get(0));
                bubbleInfo.setBuddle_br(adapterList.get(1));

            }else {
                bubbleInfo.setBuddle_tl(bubble.getBuddle_tl());
                bubbleInfo.setBuddle_tr(bubble.getBuddle_tr());
                bubbleInfo.setBuddle_bl(bubble.getBuddle_bl());
                bubbleInfo.setBuddle_br(bubble.getBuddle_br());
            }

            bubbleInfo.setTop(bubble.getTop());
            bubbleInfo.setBottom(bubble.getBottom());
            bubbleInfo.setLeft(bubble.getLeft());
            bubbleInfo.setRight(bubble.getRight());

            if(dto.getOs() == 0){
                if(BUBBLE_DENSITY.contains(dto.getDensity())){
                    bubbleInfo.setSource_url(bubble.getAndroid_buddle_source_1x());
                } else if (dto.getDensity() == 4) {
                    bubbleInfo.setSource_url(bubble.getAndroid_buddle_source());

                } else if (dto.getDensity() == 5) {
                    bubbleInfo.setSource_url(bubble.getAndroid_buddle_source_3x());
                }
            }else {
                bubbleInfo.setSource_url(bubble.getIos_buddle_source());
                bubbleInfo.setSource_url_2x(bubble.getIos_buddle_source_2x());
                bubbleInfo.setSource_url_3x(bubble.getIos_buddle_source_3x());
            }

            bubbleList.add(bubbleInfo);

        }

        vo.setList(bubbleList);
        return vo;
    }

}
