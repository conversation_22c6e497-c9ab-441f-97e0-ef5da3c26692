package com.quhong.service;

import com.quhong.config.AsyncConfig;
import com.quhong.enums.ApiResult;
import com.quhong.feign.IFriendService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FollowDao;
import com.quhong.mongo.data.MomentData;
import com.quhong.msg.room.MomentsFriendWhatNewMsg;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.MomentShowRedis;
import com.quhong.redis.TopMomentRedis;
import com.quhong.room.RoomWebSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service
public class TimelineService {
    private static final Logger logger = LoggerFactory.getLogger(TimelineService.class);

    @Resource
    private ActorDao actorDao;
    @Resource
    private IFriendService iFriendService;
    @Resource
    private FollowDao followDao;
    @Resource
    protected RoomWebSender roomWebSender;
    @Resource(name = DataRedisBean.ACTIVITY)
    private StringRedisTemplate redisTemplate;
    @Resource
    private MomentShowRedis momentShowRedis;
    @Resource
    private RecommendService recommendService;
    @Resource
    private TopMomentRedis topMomentRedis;
    @Resource
    private NearbyService nearbyService;

    private String getTimelineKey(String uid) {
        return "list:momentTimeline:" + uid;
    }

    public List<String> getTimeline(String uid, int start, int end) {
        try {
            return redisTemplate.opsForList().range(getTimelineKey(uid), start, end);
        } catch (Exception e) {
            logger.error("get timeline error, start={} end={}", start, end, e);
            return Collections.emptyList();
        }
    }

    public void deleteTimeline(String uid, String mid) {
        try {
            logger.info("deleteTimeline uid={} mid={}", uid, mid);
            redisTemplate.opsForList().remove(getTimelineKey(uid), 1, mid);
        } catch (Exception e) {
            logger.error("deleteTimeline error, mid={}", mid, e);
        }
    }

    public void cleanTimeline(String uid, int end) {
        try {
            logger.info("cleanTimeline uid={} end={}", uid, end);
            redisTemplate.opsForList().trim(getTimelineKey(uid), 0, end);
        } catch (Exception e) {
            logger.error("cleanTimeline error, uid={}", uid, e);
        }
    }

    public int getTimelineSize(String uid) {
        try {
            Long size = redisTemplate.opsForList().size(getTimelineKey(uid));
            return null == size ? 0 : size.intValue();
        } catch (Exception e) {
            logger.error("getTimelineSize error, uid={}", uid, e);
            return 0;
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    public void makeTimeline(String uid, MomentData momentData, boolean isPublic) {
        String mid = momentData.get_id().toString();
        Set<String> timelineAidSet = new HashSet<>();
        timelineAidSet.add(uid);
        // 好友列表
        ApiResult<Set<String>> apiResult = iFriendService.getFriendSet(uid);
        if (apiResult.isOk()) {
            int sendCount = 0;
            Set<String> friendSet = apiResult.getData();
            for (String aid : friendSet) {
                // 限制400个好友
                if (sendCount++ < 400) {
                    timelineAidSet.add(aid);
                }
            }
        }
        if (isPublic) {
            // follow我的
            timelineAidSet.addAll(followDao.getFollowedUidSet(uid));
            // 发送到广场中
            momentShowRedis.addMoment(momentData);
            // 发送到推荐池中
            recommendService.buildRecommendMoment(momentData);
            // 置顶用户处理
            topMomentRedis.addTopMomentByAccount(uid, mid);
            // 附近推荐
            nearbyService.handleNearbyMomentScore(momentData);
        }
        makeRedisTimeline(timelineAidSet, mid);
        incrBatchMomentUnread(timelineAidSet);
        // 发送朋友圈未读消息
        ApiResult<Set<String>> allOnlineSet = iFriendService.getAllOnlineSet();
        if (allOnlineSet.isOk()) {
            Set<String> onlineSet = allOnlineSet.getData();
            for (String aid : timelineAidSet) {
                if (onlineSet.contains(aid)) {
                    MomentsFriendWhatNewMsg msg = new MomentsFriendWhatNewMsg();
                    msg.setUnread(1);
                    roomWebSender.sendPlayerWebMsg("", aid, aid, msg, false);
                }
            }
        }
        logger.info("finish make timeline uid={} mid={} size={}", uid, mid, timelineAidSet.size());
    }

    @SuppressWarnings("all")
    public void makeRedisTimeline(Set<String> timelineAidSet, String mid) {
        try {
            if (timelineAidSet.isEmpty()) {
                return;
            }
            RedisSerializer<String> keySerializer = (RedisSerializer<String>) redisTemplate.getKeySerializer();
            byte[] value = keySerializer.serialize(mid);
            long seconds = TimeUnit.DAYS.toSeconds(45);
            redisTemplate.executePipelined((RedisCallback<Object>) pipeLine -> {
                try {
                    for (String aid : timelineAidSet) {
                        byte[] key = keySerializer.serialize(getTimelineKey(aid));
                        pipeLine.lPush(key, value);
                        // 最多300条
                        pipeLine.lTrim(key, 0, 300);
                        pipeLine.expire(key, seconds);
                    }
                } catch (Exception e) {
                    logger.error("makeRedisTimeline error mid={}", mid, e);
                }
                return null;
            });
        } catch (Exception e) {
            logger.error("makeRedisTimeline error mid={}", mid, e);
        }
    }

    public void incrBatchMomentUnread(Set<String> uidSet) {
        int pageSize = 500;
        if (uidSet.size() > pageSize) {
            Set<String> subSet = new HashSet<>();
            for (String uid : uidSet) {
                subSet.add(uid);
                if (subSet.size() >= pageSize) {
                    actorDao.incrMomentUnread(subSet);
                    subSet.clear();
                }
            }
            if (!subSet.isEmpty()) {
                actorDao.incrMomentUnread(subSet);
            }
        }
    }

}
