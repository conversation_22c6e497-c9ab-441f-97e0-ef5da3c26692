package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.TaskRankConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.RechargeInfo;
import com.quhong.data.SendGiftData;
import com.quhong.data.bo.TaskRankMsgBO;
import com.quhong.data.bo.TaskRankTemplateBO;
import com.quhong.data.vo.TaskRankTemplateVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.LogType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.ActivityHandler;
import com.quhong.handler.ActivityRechargeHandler;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.TaskRankTemplateDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mongo.data.TaskRankTemplateData;
import com.quhong.mysql.dao.RoomEventDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.mysql.data.UserResourceData;
import com.quhong.redis.TaskRankRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.MatchUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TaskRankTemplateService implements ActivityHandler, ActivityRechargeHandler, TaskMsgHandler {

    private static final Logger logger = LoggerFactory.getLogger(TaskRankTemplateService.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    public static final String TITLE_DAILY_TASK = "Activity Template-%s-daily task";
    public static final String TITLE_PERIOD_TASK = "Activity Template-%s-milestone task";
    public static final String TITLE_RANK_TASK = "Activity Template-%s-rank task";
    @Resource
    private TaskRankTemplateDao taskRankTemplateDao;
    @Resource
    private TaskRankTemplateService taskRankTemplateService;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private TaskRankRedis taskRankRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private RoomEventDao roomEventDao;


    /**
     * 获取正在运行的冲榜活动
     */
    public List<TaskRankTemplateData> getTaskRankTemplateActivities() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<TaskRankTemplateData> activityList = taskRankTemplateService.getTaskRankTemplateActivitiesFromCache();
        return activityList.stream().filter(a -> a.getEndTime() > nowSeconds && a.getStartTime() <= nowSeconds).collect(Collectors.toList());
    }

    /**
     * 获取正在运行的冲榜活动
     */
    @Cacheable(value = "getTaskRankTemplateAllMapFromCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public TaskRankTemplateBO getTaskRankTemplateAllMapFromCache() {
        List<TaskRankTemplateData> activityList = taskRankTemplateService.getTaskRankTemplateActivities();
        TaskRankTemplateBO bo = new TaskRankTemplateBO();
        bo.setDailyTaskMap(new HashMap<>());
        bo.setPeriodTaskMap(new HashMap<>());
        bo.setRankTaskMap(new HashMap<>());
        Map<String, Integer> keyMaxProcess = new HashMap<>();

        for (TaskRankTemplateData activity : activityList) {
            TaskRankTemplateBO.TaskRankConfig taskRankConfig = new TaskRankTemplateBO.TaskRankConfig();
            taskRankConfig.setActivityId(activity.getActivityId());
            taskRankConfig.setTestStatus(activity.getTestStatus());
            taskRankConfig.setStartTime(activity.getStartTime());
            taskRankConfig.setEndTime(activity.getEndTime());
            taskRankConfig.setCountryFilter(activity.getCountry());
            taskRankConfig.setGenderFilter(activity.getGender());
            taskRankConfig.setGiftIdFilterList(CollectionUtils.isEmpty(activity.getGiftList()) ? Collections.emptyList() : activity.getGiftList().stream().map(TaskRankTemplateData.ActivityGift::getGiftId).collect(Collectors.toList()));

            // 填充 dailyTaskMap
            if (activity.isDailyTaskEnable() && !CollectionUtils.isEmpty(activity.getDailyTaskList())) {
                activity.getDailyTaskList().forEach(taskConfig -> {
                    TaskRankTemplateBO.TaskRankConfig dailyTaskRankConfig = new TaskRankTemplateBO.TaskRankConfig();
                    BeanUtils.copyProperties(taskRankConfig, dailyTaskRankConfig);
                    dailyTaskRankConfig.setTaskRankKey(taskConfig.getTaskRankKey());
                    dailyTaskRankConfig.setTotalProcess(taskConfig.getTotalProcess());
                    dailyTaskRankConfig.setExtraParam(taskConfig.getExtraParam());
                    String acKey = getActivityTaskKey(taskRankConfig.getActivityId(), 0, taskConfig.getTaskRankKey(), taskConfig.getExtraParam());

                    keyMaxProcess.put(acKey, Math.max(keyMaxProcess.getOrDefault(acKey, 0), taskConfig.getTotalProcess()));
                    bo.getDailyTaskMap().computeIfAbsent(dailyTaskRankConfig.getTaskRankKey(), k -> new ArrayList<>()).add(dailyTaskRankConfig);
                });
            }

            // 填充 periodTaskMap
            if (activity.isPeriodTaskEnable() && !CollectionUtils.isEmpty(activity.getPeriodTaskList())) {
                activity.getPeriodTaskList().forEach(taskConfig -> {
                    TaskRankTemplateBO.TaskRankConfig periodTaskRankConfig = new TaskRankTemplateBO.TaskRankConfig();
                    BeanUtils.copyProperties(taskRankConfig, periodTaskRankConfig);
                    periodTaskRankConfig.setTaskRankKey(taskConfig.getTaskRankKey());
                    periodTaskRankConfig.setTotalProcess(taskConfig.getTotalProcess());
                    periodTaskRankConfig.setExtraParam(taskConfig.getExtraParam());
                    String acKey = getActivityTaskKey(taskRankConfig.getActivityId(), 1, taskConfig.getTaskRankKey(), taskConfig.getExtraParam());
                    keyMaxProcess.put(acKey, Math.max(keyMaxProcess.getOrDefault(acKey, 0), taskConfig.getTotalProcess()));
                    bo.getPeriodTaskMap().computeIfAbsent(periodTaskRankConfig.getTaskRankKey(), k -> new ArrayList<>()).add(periodTaskRankConfig);
                });
            }

            // 填充 rankTaskMap
            if (activity.isRankConfigEnable() && activity.getRankConfig() != null) {
                TaskRankTemplateBO.TaskRankConfig rankConfig = new TaskRankTemplateBO.TaskRankConfig();
                BeanUtils.copyProperties(taskRankConfig, rankConfig);
                rankConfig.setTaskRankKey(activity.getRankConfig().getTaskRankKey());
                rankConfig.setExtraParam(activity.getRankConfig().getExtraParam());
//                String acKey = getActivityTaskKey(taskRankConfig.getActivityId(), 3, rankConfig.getTaskRankKey(), rankConfig.getExtraParam());
                bo.getRankTaskMap().computeIfAbsent(rankConfig.getTaskRankKey(), k -> new ArrayList<>()).add(rankConfig);
            }

            for (Map.Entry<String, List<TaskRankTemplateBO.TaskRankConfig>> entry : bo.getDailyTaskMap().entrySet()) {
                for (TaskRankTemplateBO.TaskRankConfig taskConfig : entry.getValue()) {
                    String acKey = getActivityTaskKey(taskConfig.getActivityId(), 0, taskConfig.getTaskRankKey(), taskConfig.getExtraParam());
                    if (taskConfig.getTotalProcess() != null && taskConfig.getTotalProcess().equals(keyMaxProcess.getOrDefault(acKey, 0))) {
                        taskConfig.setCollectData(true); // 只用等级最高的收集数据
                    } else {
                        taskConfig.setCollectData(false);
                    }
                }
            }

            for (Map.Entry<String, List<TaskRankTemplateBO.TaskRankConfig>> entry : bo.getPeriodTaskMap().entrySet()) {
                for (TaskRankTemplateBO.TaskRankConfig taskConfig : entry.getValue()) {
                    String acKey = getActivityTaskKey(taskConfig.getActivityId(), 1, taskConfig.getTaskRankKey(), taskConfig.getExtraParam());
                    if (taskConfig.getTotalProcess() != null && taskConfig.getTotalProcess().equals(keyMaxProcess.getOrDefault(acKey, 0))) {
                        taskConfig.setCollectData(true); // 只用等级最高的收集数据
                    } else {
                        taskConfig.setCollectData(false);
                    }
                }
            }
        }
        return bo;
    }

    @Cacheable(value = "getTaskRankTemplateActivitiesFromCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<TaskRankTemplateData> getTaskRankTemplateActivitiesFromCache() {
        List<TaskRankTemplateData> activityList = taskRankTemplateDao.getTaskRankingActivity();
        logger.info("getTaskRankTemplateActivitiesFromCache size={}", activityList.size());
        return activityList;
    }

    @Cacheable(value = "taskRankTemplate", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public TaskRankTemplateData getTaskRankTemplateActivity(String activityId) {
        if (StringUtils.isEmpty(activityId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        TaskRankTemplateData data = taskRankTemplateDao.getDataByID(activityId);
        if (null == data) {
            logger.error("cannot find ranking activity activityId={}", activityId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return data;
    }

    @Cacheable(value = "getOngoingRoomEventFromCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public boolean getOngoingRoomEventFromCache(String roomId) {
        int currentTime = DateHelper.getNowSeconds();
        RoomEventData ongoingRoomEvent = roomEventDao.getOngoingRoomEvent(roomId, currentTime);
        return ongoingRoomEvent != null;
    }


    public TaskRankTemplateData checkActivityTime(String activityId) {
        int curTime = DateHelper.getNowSeconds();
        TaskRankTemplateData activityData = taskRankTemplateService.getTaskRankTemplateActivity(activityId);
        int startTime = activityData.getStartTime();
        int endTime = activityData.getEndTime();
        if (curTime < startTime || curTime > endTime) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
        return activityData;
    }

    private String getCurrentDay(String activityId) {
        return DateHelper.ARABIAN.formatDateInDay();
        // return activityCommonRedis.getCommonStrValue(getDailyDate(activityId));
    }


    public TaskRankTemplateVO taskRankTemplateConfig(String activityId, String uid) {
        TaskRankTemplateData activity = taskRankTemplateService.getTaskRankTemplateActivity(activityId);
        TaskRankTemplateVO vo = new TaskRankTemplateVO();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        BeanUtils.copyProperties(activity, vo);
        // 配置日任务资源key
        fillDailyTaskVO(vo, uid);
        // 配置里程碑任务资源key
        fillPeriodTaskVO(vo, uid);
        // 设置榜单
        fillTaskRankVO(vo, actorData);
        return vo;
    }

    private String getTaskStatusKey(String taskKey) {
        return String.format("%s:status", taskKey);
    }

    // 填充日任务
    private void fillDailyTaskVO(TaskRankTemplateVO vo, String uid) {
        if (!vo.isDailyTaskEnable()) {
            return;
        }
        if (CollectionUtils.isEmpty(vo.getDailyTaskList())) {
            return;
        }
        List<String> resourceKeyList = vo.getDailyTaskList().stream().map(TaskRankTemplateData.TaskRankConfig::getResourceKey).collect(Collectors.toList());
        Map<String, ResourceKeyConfigData> resourceKeyMap = resourceKeyHandlerService.findListByResourceKeyList(resourceKeyList).stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));
        List<TaskRankTemplateData.TaskRankConfig> dailyTaskList = new ArrayList<>();
        String currentDay = getCurrentDay(vo.getActivityId());
        Map<String, Integer> dailyHashAll = taskRankRedis.getDailyHashAll(vo.getActivityId(), uid, currentDay);
        for (TaskRankTemplateData.TaskRankConfig taskConfig : vo.getDailyTaskList()) {
            // 设置进度
            String taskKey = taskConfig.getTaskRankKey();
            String extraParam = taskConfig.getExtraParam();
            int totalProcess = taskConfig.getTotalProcess();
            String detailTaskKey = getDetailTaskKey(taskKey, totalProcess, extraParam);
            String noProcessTaskKey = getDetailTaskKeyNoProcess(taskKey, extraParam);

            int currentProcess = dailyHashAll.getOrDefault(noProcessTaskKey, 0);
            taskConfig.setCurrentProcess(Math.min(currentProcess, totalProcess));
            // 设置状态
            String statusKey = getTaskStatusKey(detailTaskKey);
            int taskStatus = dailyHashAll.getOrDefault(statusKey, 0);
            taskConfig.setStatus(taskStatus > 0 ? taskStatus : (currentProcess >= totalProcess ? 1 : 0));
            taskConfig.setResourceKeyConfigData(resourceKeyMap.get(taskConfig.getResourceKey()));
            dailyTaskList.add(taskConfig);
        }
        vo.setDailyTaskList(dailyTaskList);

    }

    // 填充里程碑任务
    private void fillPeriodTaskVO(TaskRankTemplateVO vo, String uid) {
        if (!vo.isPeriodTaskEnable()) {
            return;
        }
        if (CollectionUtils.isEmpty(vo.getPeriodTaskList())) {
            return;
        }
        List<String> periodResKeyList = vo.getPeriodTaskList().stream().map(TaskRankTemplateData.TaskRankConfig::getResourceKey).collect(Collectors.toList());
        Map<String, ResourceKeyConfigData> periodResKeyMap = resourceKeyHandlerService.findListByResourceKeyList(periodResKeyList).stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));
        List<TaskRankTemplateData.TaskRankConfig> periodTaskList = new ArrayList<>();
        Map<String, Integer> periodHashAll = taskRankRedis.getPeriodHashAll(vo.getActivityId(), uid);
        for (TaskRankTemplateData.TaskRankConfig taskConfig : vo.getPeriodTaskList()) {
            // 设置进度
            String taskKey = taskConfig.getTaskRankKey();
            String extraParam = taskConfig.getExtraParam();
            int totalProcess = taskConfig.getTotalProcess();
            String detailTaskKey = getDetailTaskKey(taskKey, totalProcess, extraParam);
            String noProcessTaskKey = getDetailTaskKeyNoProcess(taskKey, extraParam);

            int currentProcess = periodHashAll.getOrDefault(noProcessTaskKey, 0);
            taskConfig.setCurrentProcess(Math.min(currentProcess, totalProcess));
            // 设置状态
            String statusKey = getTaskStatusKey(detailTaskKey);
            int taskStatus = periodHashAll.getOrDefault(statusKey, 0);
            taskConfig.setStatus(taskStatus > 0 ? taskStatus : (currentProcess >= totalProcess ? 1 : 0));

            taskConfig.setResourceKeyConfigData(periodResKeyMap.get(taskConfig.getResourceKey()));
            periodTaskList.add(taskConfig);
        }
        vo.setPeriodTaskList(periodTaskList);
    }

    private void fillTaskRankVO(TaskRankTemplateVO vo, ActorData actorData) {
        if (!vo.isRankConfigEnable()) {
            return;
        }

        // 设置榜单资源key
        TaskRankTemplateData.TaskRankConfig rankConfig = vo.getRankConfig();
        if (rankConfig == null) {
            return;
        }

        List<String> rankResKeyList = rankConfig.getRankRewardList().stream().map(TaskRankTemplateData.RankRewardConfig::getResourceKey).collect(Collectors.toList());
        Map<String, ResourceKeyConfigData> rankResKeyMap = resourceKeyHandlerService.findListByResourceKeyList(rankResKeyList).stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));
        List<TaskRankTemplateData.RankRewardConfig> rankRewardList = new ArrayList<>();
        for (TaskRankTemplateData.RankRewardConfig rankRewardConfig : rankConfig.getRankRewardList()) {
            rankRewardConfig.setResourceKeyConfigData(rankResKeyMap.get(rankRewardConfig.getResourceKey()));
            rankRewardList.add(rankRewardConfig);
        }
        rankConfig.setRankRewardList(rankRewardList);
        vo.setRankConfig(rankConfig);

        // 设置榜单
        String activityId = vo.getActivityId();
        String uid = actorData.getUid();
        String taskRankKey = rankConfig.getTaskRankKey();
        Map<String, Integer> rankingMap = taskRankRedis.getActivityRankMap(activityId, taskRankKey, TaskRankConstant.RANK_TOP_N);
        Map<String, String> commonFlagConfigMap = activityCommonConfig.getFlagConfigList().stream().collect(Collectors.toMap(ActivityCommonConfig.CommonFlagConfig::getCountryName, ActivityCommonConfig.CommonFlagConfig::getFlagUrl));
        int rank = 1;
        List<TaskRankTemplateVO.TaskRankConfig> taskRankConfigList = new ArrayList<>();
        TaskRankTemplateVO.TaskRankConfig myRankConfig = new TaskRankTemplateVO.TaskRankConfig();

        boolean roomRankFlag = taskRankKey.equals(TaskRankConstant.RANK_ROOM_DEVOTE) || taskRankKey.equals(TaskRankConstant.RANK_ROOM_EVENT_DEVOTE);
        String equalRankId = roomRankFlag ? RoomUtils.formatRoomId(uid) : uid;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            TaskRankTemplateVO.TaskRankConfig taskRankConfig = new TaskRankTemplateVO.TaskRankConfig();
            String rankId = entry.getKey();
            taskRankConfig.setRankId(rankId);
            taskRankConfig.setRank(rank);
            taskRankConfig.setScore(String.valueOf(entry.getValue()));

            if (roomRankFlag) {
                MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(rankId);
                taskRankConfig.setHead(ImageUrlGenerator.generateRoomUserUrl(mongoRoomData.getHead()));
                taskRankConfig.setName(mongoRoomData.getName());
                ActorData rankActor = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(rankId));
                String countryCode = ActorUtils.getCountryCode(rankActor.getCountry());
                taskRankConfig.setCountry(rankActor.getCountry());
                taskRankConfig.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
                taskRankConfig.setRidData(rankActor.getRidData());
            } else {
                ActorData rankActor = actorDao.getActorDataFromCache(rankId);
                taskRankConfig.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
                taskRankConfig.setName(rankActor.getName());
                String countryCode = ActorUtils.getCountryCode(rankActor.getCountry());
                taskRankConfig.setCountry(rankActor.getCountry());
                taskRankConfig.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
                taskRankConfig.setRidData(rankActor.getRidData());
            }
            if (rankId.equals(equalRankId)) {
                BeanUtils.copyProperties(taskRankConfig, myRankConfig);
                myRankConfig.setRidData(actorData.getRidData());
            }
            taskRankConfigList.add(taskRankConfig);
            rank += 1;
        }

        if (myRankConfig.getRank() == null || myRankConfig.getRank() == 0) {
            myRankConfig.setRankId(equalRankId);
            myRankConfig.setRidData(actorData.getRidData());
            myRankConfig.setName(actorData.getName());
            myRankConfig.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myRankConfig.setScore(String.valueOf(taskRankRedis.getActivityZSetRankScore(activityId, taskRankKey, uid)));
            myRankConfig.setRank(-1);
            myRankConfig.setCountry(actorData.getCountry());
            String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
            myRankConfig.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));

            if (roomRankFlag) {
                MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(equalRankId);
                if (mongoRoomData != null) {
                    myRankConfig.setName(mongoRoomData.getName());
                    myRankConfig.setHead(ImageUrlGenerator.generateRoomUserUrl(mongoRoomData.getHead()));
                }
            }
        }
        vo.setTaskRankConfigList(taskRankConfigList);
        vo.setMyTaskRankConfig(myRankConfig);
    }


    /**
     * 领取任务奖励
     */
    public void taskRankTemplateReward(String activityId, String uid, Integer taskType, String taskKey, String extraParam, int totalProcess) {
        TaskRankTemplateData taskRankTemplateData = checkActivityTime(activityId);
        String detailTaskKey = getDetailTaskKey(taskKey, totalProcess, extraParam);
        String noProcessTaskKey = getDetailTaskKeyNoProcess(taskKey, extraParam);
        synchronized (stringPool.intern("taskRankTemplateReward:" + uid)) {
            if (taskType == 0) {
                if (!taskRankTemplateData.isDailyTaskEnable()) {
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "لم يتم تكوين المهمة");
                }

                Map<String, TaskRankTemplateData.TaskRankConfig> rewardConfigMap = taskRankTemplateData.getDailyTaskList().stream()
                        .collect(Collectors.toMap(
                                taskRankConfig -> getDetailTaskKey(taskRankConfig.getTaskRankKey(), taskRankConfig.getTotalProcess(),
                                        taskRankConfig.getExtraParam()),
                                Function.identity()
                        ));

//                Map<String, TaskRankTemplateData.TaskRankConfig> rewardConfigMap = taskRankTemplateData.getDailyTaskList().stream().collect(Collectors.toMap(TaskRankTemplateData.TaskRankConfig::getTaskRankKey, Function.identity()));

                TaskRankTemplateData.TaskRankConfig taskConfig = rewardConfigMap.get(detailTaskKey);
                if (taskConfig == null) {
                    logger.info("taskType:{} detailTaskKey:{} not find", taskType, detailTaskKey);
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
                }
                // 领取每日任务奖励
                String currentDay = this.getCurrentDay(activityId);
                Map<String, Integer> dailyHashAllMap = taskRankRedis.getDailyHashAll(activityId, uid, currentDay);

                // 是否已领取奖励
                String statusKey = getTaskStatusKey(detailTaskKey);
                int rewardStatus = dailyHashAllMap.getOrDefault(statusKey, 0);
                if (rewardStatus > 0) {
                    throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY.getCode(), "تم جمع المكافأة");
                }

                int currentProcess = dailyHashAllMap.getOrDefault(noProcessTaskKey, 0);
                int tProcess = taskConfig.getTotalProcess();
                if (currentProcess < tProcess) {
                    throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
                }
//                String itemTitle = taskConfig.getTitle();
                String itemTitle = String.format(TITLE_DAILY_TASK, taskRankTemplateData.getNameEn());
                resourceKeyHandlerService.sendResourceData(uid, taskConfig.getResourceKey(), itemTitle, itemTitle, itemTitle, "", "");
                taskRankRedis.setDailyHashNum(activityId, uid, currentDay, statusKey, TaskRankConstant.TASK_FINISH);
            } else {
                if (!taskRankTemplateData.isPeriodTaskEnable()) {
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "لم يتم تكوين المهمة");
                }
                Map<String, TaskRankTemplateData.TaskRankConfig> rewardConfigMap = taskRankTemplateData.getPeriodTaskList().stream()
                        .collect(Collectors.toMap(
                                taskRankConfig -> getDetailTaskKey(taskRankConfig.getTaskRankKey(), taskRankConfig.getTotalProcess(),
                                        taskRankConfig.getExtraParam()),
                                Function.identity()
                        ));
//                Map<String, TaskRankTemplateData.TaskRankConfig> rewardConfigMap = taskRankTemplateData.getPeriodTaskList().stream().collect(Collectors.toMap(TaskRankTemplateData.TaskRankConfig::getTaskRankKey, Function.identity()));
                TaskRankTemplateData.TaskRankConfig taskConfig = rewardConfigMap.get(detailTaskKey);
                if (taskConfig == null) {
                    logger.info("taskType:{} detailTaskKey:{} not find", taskType, detailTaskKey);
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
                }
                Map<String, Integer> periodHashAllMap = taskRankRedis.getPeriodHashAll(activityId, uid);

                // 是否已领取奖励
                String statusKey = getTaskStatusKey(detailTaskKey);
                int rewardStatus = periodHashAllMap.getOrDefault(statusKey, 0);
                if (rewardStatus > 0) {
                    throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY.getCode(), "تم جمع المكافأة");
                }

                int currentProcess = periodHashAllMap.getOrDefault(noProcessTaskKey, 0);
                int tProcess = taskConfig.getTotalProcess();
                if (currentProcess < tProcess) {
                    throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
                }
//                String itemTitle = taskConfig.getTitle();
                String itemTitle = String.format(TITLE_PERIOD_TASK, taskRankTemplateData.getNameEn());
                resourceKeyHandlerService.sendResourceData(uid, taskConfig.getResourceKey(), itemTitle, itemTitle, itemTitle, "", "");
                taskRankRedis.setPeriodHashNum(activityId, uid, statusKey, TaskRankConstant.TASK_FINISH);
            }
        }
    }

    /**
     * 榜单数据处理
     */
    public void handlePersonRoomRankData(TaskRankMsgBO bo) {
        String rankId = bo.getTaskRankUid();
        int testStatus = bo.getTestStatus();
        String taskRankExtraParam = bo.getTaskRankExtraParam();
        msgLogger.info("handlePersonRoomRankData:{}", JSONObject.toJSONString(bo));
        synchronized (stringPool.intern("handlePersonRoomRankData:" + rankId)) {
            // 国家过滤
            if (!ObjectUtils.isEmpty(bo.getCountryFilter()) && !ActorUtils.isSameCountry(bo.getCountryFilter(), bo.getTaskUserCountry())) {
                return;
            }

            // 性别过滤
            if (bo.getGenderFilter() > 0 && bo.getGenderFilter() != bo.getTaskUserGender()) {
                return;
            }

            // 灰度测试过滤
            if (testStatus > 0) {
                boolean whiteTest = RoomUtils.isVoiceRoom(rankId) ? whiteTestDao.isMemberByType(rankId, WhiteTestDao.WHITE_TYPE_ROOM_ID) : whiteTestDao.isMemberByType(rankId, WhiteTestDao.WHITE_TYPE_RID);
                if (!whiteTest) {
                    return;
                }
            }

            // 礼物过滤
            int extraParamType = bo.getExtraParamType();
            if (extraParamType == TaskRankConstant.EXTRA_PARAM_TYPE_GIFT) {
                int extraParamInt = parseIntExtraParam(bo.getExtraParam());
                List<Integer> giftIdFilterList = bo.getGiftIdFilterList();
                int giftId = parseIntExtraParam(taskRankExtraParam);
                if (extraParamInt == 1 && !CollectionUtils.isEmpty(giftIdFilterList) && !giftIdFilterList.contains(giftId)) {
                    return;
                }
            }
            taskRankRedis.incrActivityZSetRankScoreWithTime(bo.getActivityId(), bo.getTaskRankKey(), rankId, bo.getScore());
        }
    }

    /**
     * 每日任务处理
     */

    // 去重任务key
    private String getSetTaskKey(String activityId, String taskKey, String dateStr) {
        return ObjectUtils.isEmpty(dateStr) ? String.format("%s:%s", activityId, taskKey) : String.format("%s:%s:%s", activityId, taskKey, dateStr);
    }

    private int parseIntExtraParam(String taskRankExtraParam) {
        try {
            return Integer.parseInt(taskRankExtraParam);
        } catch (Exception e) {
            logger.error("parseTaskRankExtraParam taskRankExtraParam={} error={}", taskRankExtraParam, e);
        }
        return 0;
    }

    public void handleDailyTaskData(TaskRankMsgBO bo) {
        String taskUid = bo.getTaskRankUid();
        String taskAid = bo.getTaskAid();
        String activityId = bo.getActivityId();
        int testStatus = bo.getTestStatus();
        String currentDay = getCurrentDay(activityId);
        String taskKey = bo.getTaskRankKey();
        boolean setFlag = bo.isSetFlag();
        int totalProcess = bo.getTotalProcess();
        int score = bo.getScore();
        boolean isCollectData = bo.isCollectData();
        msgLogger.info("handleDailyTaskData:{}", JSONObject.toJSONString(bo));
        if (!isCollectData) {
            return;
        }
        String extraParam = bo.getExtraParam(); // mongo配置
        String taskRankExtraParam = bo.getTaskRankExtraParam(); // 逻辑传进来的参数，非mongo配置
        String detailTaskKey = getDetailTaskKey(taskKey, totalProcess, extraParam);
        String noProcessTaskKey = getDetailTaskKeyNoProcess(taskKey, extraParam);
        synchronized (stringPool.intern("handleDailyTaskData:" + taskUid)) {
            // 国家过滤
            if (!ObjectUtils.isEmpty(bo.getCountryFilter()) && !ActorUtils.isSameCountry(bo.getCountryFilter(), bo.getTaskUserCountry())) {
                return;
            }

            // 性别过滤
            if (bo.getGenderFilter() > 0 && bo.getGenderFilter() != bo.getTaskUserGender()) {
                return;
            }

            // 灰度测试过滤
            if (testStatus > 0 && !whiteTestDao.isMemberByType(taskUid, WhiteTestDao.WHITE_TYPE_RID)) {
                return;
            }

            Map<String, Integer> dailyHashAllMap = taskRankRedis.getDailyHashAll(activityId, taskUid, currentDay);
            // 任务完成状态判断
            int currentProcess = dailyHashAllMap.getOrDefault(noProcessTaskKey, 0);
            if (currentProcess >= totalProcess) {
                return;
            }
            String taskStatusKey = getTaskStatusKey(detailTaskKey);
            int taskStatus = dailyHashAllMap.getOrDefault(taskStatusKey, 0);
            if (taskStatus > 0) {
                return;
            }

            // 任务礼物过滤
            int extraParamType = bo.getExtraParamType();
            if (extraParamType == TaskRankConstant.EXTRA_PARAM_TYPE_GIFT) {
                List<Integer> giftIdFilterList = bo.getGiftIdFilterList();
                int giftId = parseIntExtraParam(taskRankExtraParam);
                if (!CollectionUtils.isEmpty(giftIdFilterList) && !giftIdFilterList.contains(giftId)) {
                    return;
                }
            }
            // 任务游戏过滤
            if (extraParamType == TaskRankConstant.EXTRA_PARAM_TYPE_GAME) {
//                 String extraParam = bo.getExtraParam();
                if (!ObjectUtils.isEmpty(taskRankExtraParam) && !ObjectUtils.isEmpty(extraParam) && !taskRankExtraParam.equals(extraParam)) {
                    return;
                }
            }

            // 指定礼物task 来源过滤、用户类型过滤
            if (TaskRankConstant.TASK_PERSON_SEND_ORDER_GIFT_BEAN.equals(taskKey)){
                if (bo.getGiftOrigin() != null && bo.getGiftOrigin() == 1 && (bo.getGiving_type() == 5 || bo.getGiving_type() == 6)){
                    return;
                }

                if (bo.getUserType() != null && bo.getUserType() == 1){
                    ActorData aidActorData = actorDao.getActorDataFromCache(taskAid);
                    if (aidActorData == null || !ActorUtils.isNewDeviceAccount(taskAid, aidActorData.getFirstTnId())){
                        return;
                    }
                }
            }

            // 设备限制
            if (setFlag) {
                ActorData aidActorData = actorDao.getActorDataFromCache(taskAid);
                String aidTnId = aidActorData.getTn_id();
                String taskSetKey = getSetTaskKey(activityId, noProcessTaskKey, currentDay);
                boolean isTaskSetFlag = taskRankRedis.isActivitySetData(taskSetKey, aidTnId);
                if (isTaskSetFlag) {
                    return;
                }
                taskRankRedis.addActivitySetData(taskSetKey, aidTnId);
            }
            taskRankRedis.incDailyHashNum(activityId, taskUid, currentDay, noProcessTaskKey, score);
        }
    }

    public void handlePeriodTaskData(TaskRankMsgBO bo) {
        String taskUid = bo.getTaskRankUid();
        String taskAid = bo.getTaskAid();
        String activityId = bo.getActivityId();
        int testStatus = bo.getTestStatus();
        String taskKey = bo.getTaskRankKey();
        boolean setFlag = bo.isSetFlag();
        int totalProcess = bo.getTotalProcess();
        int score = bo.getScore();
        boolean isCollectData = bo.isCollectData();
        msgLogger.info("handlePeriodTaskData:{}", JSONObject.toJSONString(bo));
        if (!isCollectData) {
            return;
        }
        String extraParam = bo.getExtraParam(); // mongo配置
        String taskRankExtraParam = bo.getTaskRankExtraParam(); // 逻辑传进来的参数，非mongo配置
        String detailTaskKey = getDetailTaskKey(taskKey, totalProcess, extraParam);
        String noProcessTaskKey = getDetailTaskKeyNoProcess(taskKey, extraParam);
        synchronized (stringPool.intern("handlePeriodTaskData:" + taskUid)) {
            // 国家过滤
            if (!ObjectUtils.isEmpty(bo.getCountryFilter()) && !ActorUtils.isSameCountry(bo.getCountryFilter(), bo.getTaskUserCountry())) {
                return;
            }

            // 性别过滤
            if (bo.getGenderFilter() > 0 && bo.getGenderFilter() != bo.getTaskUserGender()) {
                return;
            }

            // 灰度测试过滤
            if (testStatus > 0 && !whiteTestDao.isMemberByType(taskUid, WhiteTestDao.WHITE_TYPE_RID)) {
                return;
            }

            Map<String, Integer> periodHashAllMap = taskRankRedis.getPeriodHashAll(activityId, taskUid);
            // 任务完成状态判断
            int currentProcess = periodHashAllMap.getOrDefault(noProcessTaskKey, 0);
            if (currentProcess >= totalProcess) {
                return;
            }
            String taskStatusKey = getTaskStatusKey(detailTaskKey);
            int taskStatus = periodHashAllMap.getOrDefault(taskStatusKey, 0);
            if (taskStatus > 0) {
                return;
            }

            // 任务礼物过滤
            int extraParamType = bo.getExtraParamType(); // 非mongo配置字段
            if (extraParamType == TaskRankConstant.EXTRA_PARAM_TYPE_GIFT) {
                List<Integer> giftIdFilterList = bo.getGiftIdFilterList();
                int giftId = parseIntExtraParam(taskRankExtraParam);
                if (!CollectionUtils.isEmpty(giftIdFilterList) && !giftIdFilterList.contains(giftId)) {
                    return;
                }
            }

            // 任务游戏过滤
            if (extraParamType == TaskRankConstant.EXTRA_PARAM_TYPE_GAME) {
                if (!ObjectUtils.isEmpty(taskRankExtraParam) && !ObjectUtils.isEmpty(extraParam) && !taskRankExtraParam.equals(extraParam)) {
                    return;
                }
            }

            // 指定礼物task 来源过滤、用户类型过滤
            if (TaskRankConstant.TASK_PERSON_SEND_ORDER_GIFT_BEAN.equals(taskKey)){
                if (bo.getGiftOrigin() != null && bo.getGiftOrigin() == 1 && (bo.getGiving_type() == 5 || bo.getGiving_type() == 6)){
                    return;
                }

                if (bo.getUserType() != null && bo.getUserType() == 1){
                    ActorData aidActorData = actorDao.getActorDataFromCache(taskAid);
                    if (aidActorData == null || !ActorUtils.isNewDeviceAccount(taskAid, aidActorData.getFirstTnId())){
                        return;
                    }
                }
            }

            // 设备限制
            if (setFlag) {
                ActorData aidActorData = actorDao.getActorDataFromCache(taskAid);
                String aidTnId = aidActorData.getTn_id();
                String taskSetKey = getSetTaskKey(activityId, noProcessTaskKey, null);
                boolean isTaskSetFlag = taskRankRedis.isActivitySetData(taskSetKey, aidTnId);
                if (isTaskSetFlag) {
                    return;
                }
                taskRankRedis.addActivitySetData(taskSetKey, aidTnId);
            }
            taskRankRedis.incPeriodHashNum(activityId, taskUid, noProcessTaskKey, score);
        }
    }

    private String getDetailTaskKey(String taskKey, int totalProcess, String extraParam) {
        return ObjectUtils.isEmpty(extraParam) ? String.format("d:%s:%s", taskKey, totalProcess)
                : String.format("d:%s:%s:%s", taskKey, totalProcess, extraParam);
    }

    private String getDetailTaskKeyNoProcess(String taskKey, String extraParam) {
        return ObjectUtils.isEmpty(extraParam) ? taskKey
                : String.format("%s:%s", taskKey, extraParam);
    }

    private String getActivityTaskKey(String activityId, int type, String taskKey, String extraParam) {
        return ObjectUtils.isEmpty(extraParam) ? String.format("a:%s:%s:%s", activityId, type, taskKey)
                : String.format("a:%s:%s:%s:%s", activityId, type, taskKey, extraParam);
    }

    private <K> void handleTasks(K key, Map<K, List<TaskRankTemplateBO.TaskRankConfig>> taskMap, TaskRankMsgBO bo, Consumer<TaskRankMsgBO> handler) {
        List<TaskRankTemplateBO.TaskRankConfig> taskList = taskMap.getOrDefault(key, Collections.emptyList());
        for (TaskRankTemplateBO.TaskRankConfig task : taskList) {
            BeanUtils.copyProperties(task, bo);
            handler.accept(bo);
        }
    }

    public void handleTaskMsgData(TaskRankMsgBO bo) {
        String taskUid = RoomUtils.isVoiceRoom(bo.getTaskRankUid()) ? RoomUtils.getRoomHostId(bo.getTaskRankUid()) : bo.getTaskRankUid();
        String taskRankKey = bo.getTaskRankKey();
        ActorData taskActorData = actorDao.getActorDataFromCache(taskUid);
        bo.setTaskUserCountry(taskActorData.getCountry());
        bo.setTaskUserGender(taskActorData.getGender());
        // 获取当前进行的活动
        TaskRankTemplateBO taskRankTemplateBO = taskRankTemplateService.getTaskRankTemplateAllMapFromCache();
        handleTasks(taskRankKey, taskRankTemplateBO.getDailyTaskMap(), bo, this::handleDailyTaskData);
        handleTasks(taskRankKey, taskRankTemplateBO.getPeriodTaskMap(), bo, this::handlePeriodTaskData);
        handleTasks(taskRankKey, taskRankTemplateBO.getRankTaskMap(), bo, this::handlePersonRoomRankData);
    }


    /**
     * 礼物发送接收相关
     */
    @Override
    public void process(SendGiftData giftData) {
        if (giftData.getGift_cost_type() == 2) {
            // 金币礼物不统计
            return;
        }
        if (RoomUtils.isGameRoom(giftData.getRoomId())) {
            // 游戏房礼物不统计
            return;
        }

        int giftId = giftData.getGid();
        String giftIdStr = String.valueOf(giftId);
        String fromUid = giftData.getFrom_uid();
        String roomId = giftData.getRoomId();
        Set<String> aidList = giftData.getAid_list();
        int giftNumberPrice = giftData.getNumber() * giftData.getPrice();
        int totalGiftPrice = giftNumberPrice * aidList.size();
        // 个人发礼榜单
        handleTaskMsgData(new TaskRankMsgBO(fromUid, null, TaskRankConstant.RANK_PERSON_SEND, giftIdStr, TaskRankConstant.EXTRA_PARAM_TYPE_GIFT, false, totalGiftPrice));
        // 个人发礼任务
        handleTaskMsgData(new TaskRankMsgBO(fromUid, null, TaskRankConstant.TASK_PERSON_SEND_GIFT_BEAN, giftIdStr, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, false, totalGiftPrice));

        for (String aid : aidList) {

            // 个人发指定礼物任务
            TaskRankMsgBO orderGiftTaskBO = new TaskRankMsgBO(fromUid, aid, TaskRankConstant.TASK_PERSON_SEND_ORDER_GIFT_BEAN, giftIdStr, TaskRankConstant.EXTRA_PARAM_TYPE_GIFT, false, giftNumberPrice);
            orderGiftTaskBO.setGiving_type(giftData.getGiving_type());
            handleTaskMsgData(orderGiftTaskBO);

            // 个人收礼榜单
            handleTaskMsgData(new TaskRankMsgBO(aid, null, TaskRankConstant.RANK_PERSON_RECEIVE, giftIdStr, TaskRankConstant.EXTRA_PARAM_TYPE_GIFT, false, giftNumberPrice));
            // 个人收礼任务
            handleTaskMsgData(new TaskRankMsgBO(aid, null, TaskRankConstant.TASK_PERSON_RECEIVE_GIFT_BEAN, giftIdStr, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, false, giftNumberPrice));
            // 个人收指定礼物任务
            handleTaskMsgData(new TaskRankMsgBO(aid, null, TaskRankConstant.TASK_PERSON_RECEIVE_ORDER_GIFT_BEAN, giftIdStr, TaskRankConstant.EXTRA_PARAM_TYPE_GIFT, false, giftNumberPrice));
        }

        if (!ObjectUtils.isEmpty(roomId)) {
            String hostUid = RoomUtils.getRoomHostId(roomId);
            // 个人房间榜单
            handleTaskMsgData(new TaskRankMsgBO(roomId, null, TaskRankConstant.RANK_ROOM_DEVOTE, giftIdStr, TaskRankConstant.EXTRA_PARAM_TYPE_GIFT, false, totalGiftPrice));
            // 房间发礼任务
            handleTaskMsgData(new TaskRankMsgBO(hostUid, null, TaskRankConstant.TASK_ROOM_GIFT_BEAN, giftIdStr, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, false, totalGiftPrice));
            // 房间指定礼物发礼任务
            handleTaskMsgData(new TaskRankMsgBO(hostUid, null, TaskRankConstant.TASK_ROOM_ORDER_GIFT_BEAN, giftIdStr, TaskRankConstant.EXTRA_PARAM_TYPE_GIFT, false, totalGiftPrice));
            boolean ongoingRoomEventFlag = taskRankTemplateService.getOngoingRoomEventFromCache(roomId);
            if (ongoingRoomEventFlag) {
                // 个人房间活动期间榜单
                handleTaskMsgData(new TaskRankMsgBO(roomId, null, TaskRankConstant.RANK_ROOM_EVENT_DEVOTE, giftIdStr, TaskRankConstant.EXTRA_PARAM_TYPE_GIFT, false, totalGiftPrice));
                // 房间活动期间发礼任务
                handleTaskMsgData(new TaskRankMsgBO(hostUid, null, TaskRankConstant.TASK_ROOM_EVENT_GIFT_BEAN, giftIdStr, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, false, totalGiftPrice));
                // 房间活动期间指定礼物发礼任务
                handleTaskMsgData(new TaskRankMsgBO(hostUid, null, TaskRankConstant.TASK_ROOM_EVENT_ORDER_GIFT_BEAN, giftIdStr, TaskRankConstant.EXTRA_PARAM_TYPE_GIFT, false, totalGiftPrice));
            }
        }
    }

    /**
     * 充值相关
     */
    @Override
    public void process(RechargeInfo rechargeInfo) {

        int rechargeDiamond = rechargeInfo.getRechargeDiamond();
        String rechargeUid = rechargeInfo.getUid();
        String rechargeItem = rechargeInfo.getRechargeItem();
        if (!TaskRankConstant.RECHARGE_ITEM_LIST.contains(rechargeItem)) {
            return;
        }
        // 个人充值任务处理
        handleTaskMsgData(new TaskRankMsgBO(rechargeUid, null, TaskRankConstant.TASK_PERSON_RECHARGE_BEAN, null, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, false, rechargeDiamond));

        // 个人充值榜单
        handleTaskMsgData(new TaskRankMsgBO(rechargeUid, null, TaskRankConstant.RANK_RECHARGE_BEAN, null, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, false, rechargeDiamond));
    }

    /**
     * 任务相关
     */
    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String taskUid = data.getUid();
        String taskAid = data.getAid();
        String roomId = data.getRoomId();
        int value = data.getValue();
        String item = data.getItem();
        // 将消息转换指定taskKey对象，再遍历活动
        String hostUid;
        switch (item) {
            case CommonMqTaskConstant.ON_MIC_TIME:
                // 个人在任意语音房的麦上时长任务
                handleTaskMsgData(new TaskRankMsgBO(taskUid, null, TaskRankConstant.TASK_ON_MIC_TIME, null, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, false, value));

                hostUid = RoomUtils.getRoomHostId(roomId);
                handleTaskMsgData(new TaskRankMsgBO(hostUid, taskUid, TaskRankConstant.TASK_INVITE_DEVICE_VALID, null, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, true, 1));
                if (hostUid.equals(taskUid)) {
                    // 在自己房间上麦
                    handleTaskMsgData(new TaskRankMsgBO(taskUid, "", TaskRankConstant.TASK_ON_MY_ROOM_MIC_TIME, null, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, false, value));
                }
                break;
            case CommonMqTaskConstant.INVITE_USER_ACTION:
                handleTaskMsgData(new TaskRankMsgBO(taskUid, taskAid, TaskRankConstant.TASK_INVITE_DEVICE, null, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, true, 1));
                break;
            case CommonMqTaskConstant.PLAY_LUDO:
            case CommonMqTaskConstant.PLAY_JACKAROO:
            case CommonMqTaskConstant.PLAY_CARROM_POOL:
            case CommonMqTaskConstant.PLAY_MONSTER_CRUSH:
            case CommonMqTaskConstant.PLAY_BALOOT:
            case CommonMqTaskConstant.PLAY_UMO:
            case CommonMqTaskConstant.PLAY_DOMINO:
            case CommonMqTaskConstant.PLAY_WHEEL:
            case CommonMqTaskConstant.PLAY_VOTE:
            case CommonMqTaskConstant.PLAY_PK_GAME:
            case CommonMqTaskConstant.PLAY_TRUTH_DARE_WHEEL:
            case CommonMqTaskConstant.PLAY_TRUTH_DARE_V2_WHEEL:
            case CommonMqTaskConstant.PLAY_FRUIT_MACHINE:
                handleTaskMsgData(new TaskRankMsgBO(taskUid, "", TaskRankConstant.TASK_PLAY_ORDER_GAME, item, TaskRankConstant.EXTRA_PARAM_TYPE_GAME, false, 1));
                break;
            case CommonMqTaskConstant.CREATE_ROOM_EVENT:
                hostUid = RoomUtils.getRoomHostId(roomId);
                handleTaskMsgData(new TaskRankMsgBO(hostUid, "", TaskRankConstant.TASK_CREATE_EVENT, null, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, false, 1));
                break;
            case CommonMqTaskConstant.JOIN_ROOM_MEMBER:
                hostUid = RoomUtils.getRoomHostId(roomId);
                handleTaskMsgData(new TaskRankMsgBO(hostUid, "", TaskRankConstant.TASK_ROOM_MEMBER, null, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, false, 1));
                break;
            case CommonMqTaskConstant.WIN_LUDO:
            case CommonMqTaskConstant.WIN_JACKAROO:
            case CommonMqTaskConstant.WIN_CARROM_POOL:
            case CommonMqTaskConstant.WIN_MONSTER_CRUSH:
            case CommonMqTaskConstant.WIN_BALOOT:
            case CommonMqTaskConstant.WIN_UMO:
            case CommonMqTaskConstant.WIN_DOMINO:
            case CommonMqTaskConstant.WIN_LUCKY_WHEEL:
                CommonMqTopicData.WinGameInfo winGameInfo = JSONObject.parseObject(data.getJsonData(), CommonMqTopicData.WinGameInfo.class);
                if (winGameInfo.getCurrencyType() == 2) {
                    handleTaskMsgData(new TaskRankMsgBO(taskUid, "", TaskRankConstant.TASK_PERSON_EARN_GAME_BEAN, item, TaskRankConstant.EXTRA_PARAM_TYPE_GAME, false, winGameInfo.getAwardValue()));
                    // 个人游戏赚
                    handleTaskMsgData(new TaskRankMsgBO(taskUid, null, TaskRankConstant.RANK_GAME_EARN, null, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, false, winGameInfo.getAwardValue()));
                }
                break;
            case CommonMqTaskConstant.ADD_FRIEND:
                handleTaskMsgData(new TaskRankMsgBO(taskUid, taskAid, TaskRankConstant.TASK_PERSON_FRIEND, null, TaskRankConstant.EXTRA_PARAM_TYPE_NOT, true, 1));
                break;
        }
    }
}
