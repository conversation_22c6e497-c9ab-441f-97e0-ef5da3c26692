package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.*;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.BadgeListDTO;
import com.quhong.data.dto.UserCardDTO;
import com.quhong.data.dto.UserInfoDTO;
import com.quhong.data.dto.UserStateInfoDTO;
import com.quhong.data.vo.*;
import com.quhong.elasticsearch.HomeVisitorEs;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.msg.obj.RoomMicInfoObject;
import com.quhong.msg.room.NewVisitorMsg;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.*;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.service.redis.RechargeRedis;
import com.quhong.utils.*;
import com.quhong.vo.RoomMicListVo;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
public class UserInfoService {

    private static final Logger logger = LoggerFactory.getLogger(UserInfoService.class);
    private static final String ZHAIYUEDENG1 = "https://cdn3.qmovies.tv/youstar/zhaiyuedeng1.webp";
    private static final String ZHAIYUEDENG2 = "https://cdn3.qmovies.tv/youstar/zhaiyuedeng2.webp";
    private static final String ZHAIYUEDENG3 = "https://cdn3.qmovies.tv/youstar/zhaiyuedeng3.webp";
    private static final String NEW_VISITOR_ITEM = "new_visitor";
    private static final String CHANGE_VISITOR_STATUS_ITEM = "change_visitor_status";
    private static final String VISITOR_PROFILE_PERSON_NAME = "visitor_profile_person";
    private static final String USER_COST_BEANS = "user_cost_beans";
    private static final String RIPPLE_URL = "https://cdn3.qmovies.tv/test/default_voice_mic.svga";
    private static final Integer DECORATE_START_TIME = 1663621200;
    private static final Integer DECORATE_END_TIME = 1684270800;
    private static final String DECORATE_BG_URL = "";
    private static final String SUPER_QUEEN_URL = ServerConfig.isProduct() ? "https://static.youstar.live/super_queen2024/?activityId=676cf15f819709ee60d83839" : "https://test2.qmovies.tv/super_queen2024/?activityId=676cf15f819709ee60d83839";
    private static final List<String> SUPER_QUEEN_BADGE = Arrays.asList("https://cdn3.qmovies.tv/badge/op_1734057319_30k_600.png", "https://cdn3.qmovies.tv/badge/op_1734057356_50k_600.png", "https://cdn3.qmovies.tv/badge/op_1734057381_100k_600.png");

    //    public static final String[] CONSTELLATION_ARR = {"水瓶座", "双鱼座", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "魔羯座"};
    public static final int[] CONSTELLATION_ARR = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12};
    public static final int[] CONSTELLATION_EDGE_DAY = {20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22};
    private static String URL_INVITE_URL;
    private static String CUSTOMER_SUPPORT_URL;
    private static final Integer MODIFY_COUNTRY_VIP_LEVEL = 4;
    private static final Map<Integer, UserCardFrameData> VIP_V1_USER_CARD_FRAME_MAP = new HashMap<>();
    private static final Map<Integer,UserCardFrameData> VIP_V2_USER_CARD_FRAME_MAP = new HashMap<>();

    @Resource
    private ActorDao actorDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private FollowDao followDao;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private BeautifulRidDao beautifulRidDao;
    @Resource
    private UserInfoShowRedis userInfoShowRedis;
    @Resource
    private UserLabelDao userLabelDao;
    @Resource
    private JoinCartonDao joinCartonDao;
    @Resource
    private JoinSourceDao joinSourceDao;
    @Resource
    private CommonDao commonDao;
    @Resource
    private UserLevelTaskService userLevelTaskService;
    @Resource
    private HVElasticsearchService hvElasticsearchService;
    @Resource
    private BadgeDao badgeDao;
    @Resource
    private BadgeListDao badgeListDao;
    @Resource
    private RippleDao rippleDao;
    @Resource
    private BeansTotalDevoteDao beansTotalDevoteDao;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private BlackListDao blackListDao;
    @Resource
    private RechargeRedis rechargeRedis;
    @Resource
    private UserRegisterDao userRegisterDao;
    @Resource
    private BackstageConfigDao backstageConfigDao;
    @Resource
    private UserOnlineRedis userOnlineRedis;
    @Resource
    private BaseInitData baseInitData;
    @Resource
    private FriendsDao friendsDao;
    @Resource
    private NewUserHonorDao newUserHonorDao;
    @Resource
    private SpecialGustDao specialGustDao;
    @Resource
    private GoodsListHomeRedis goodsListHomeRedis;
    @Resource
    private SlaveHeartRecordDao slaveHeartRecordDao;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private UserOptService userOptService;
    @Resource
    private GuardService guardService;
    @Resource
    private GiftNumDao giftNumDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private HonorTitleService honorTitleService;
    @Resource
    private UserFeedbackRedis userFeedbackRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private NewcomerTaskDao newcomerTaskDao;
    @Resource
    private UserTaskRedis userTaskRedis;
    @Resource
    private HotSearchHistoryDao hotSearchHistoryDao;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private FamilyRequestDao familyRequestDao;
    @Resource
    private HomeBannerService homeBannerService;
    @Resource
    private GameRoomUserCareerDao gameRoomUserCareerDao;
    @Resource
    private ConfigCenterService configCenterService;
    @Resource
    private ActorCommonService actorCommonService;
    @Resource
    private BadgeService badgeService;
    @Resource
    private PayRedis payRedis;
    @PostConstruct
    public void postInit() {
        URL_INVITE_URL = ServerConfig.isProduct() ? "https://static.youstar.live/invite_entry2025/" : "https://test2.qmovies.tv/invite_entry2025/";
        // CUSTOMER_SUPPORT_URL = ServerConfig.isProduct() ? "" : "https://test2.qmovies.tv/user_feedback/";
        CUSTOMER_SUPPORT_URL = ServerConfig.isProduct() ? "" : "";

        VIP_V1_USER_CARD_FRAME_MAP.put(1, new UserCardFrameData(0, 1, "https://cdn3.qmovies.tv/youstar/op_1755251430_vip_header_1.png", "https://cdn3.qmovies.tv/youstar/op_1756105717_vip1-pijian.png", "https://cdn3.qmovies.tv/youstar/op_1756105717_vip1-beij-baise.png", "https://cdn3.qmovies.tv/youstar/op_1756105717_vip1-beij-heise.png"));
        VIP_V1_USER_CARD_FRAME_MAP.put(2, new UserCardFrameData(0, 2, "https://cdn3.qmovies.tv/youstar/op_1755251454_vip_header_2.png", "https://cdn3.qmovies.tv/youstar/op_1756105923_vip2-pijian.png", "https://cdn3.qmovies.tv/youstar/op_1756105923_vip2-beijng-baise.png", "https://cdn3.qmovies.tv/youstar/op_1756105923_vip2-beijng-heise.png"));
        VIP_V1_USER_CARD_FRAME_MAP.put(3, new UserCardFrameData(0, 3, "https://cdn3.qmovies.tv/youstar/op_1755251454_vip_header_3.png", "https://cdn3.qmovies.tv/youstar/op_1756105967_vip3-pijian.png", "https://cdn3.qmovies.tv/youstar/op_1756105967_vip3-baijng-baise.png", "https://cdn3.qmovies.tv/youstar/op_1756105967_vip3-baijng-heise.png"));
        VIP_V1_USER_CARD_FRAME_MAP.put(4, new UserCardFrameData(0, 4, "https://cdn3.qmovies.tv/youstar/op_1755251553_vip_header_4.png", "https://cdn3.qmovies.tv/youstar/op_1756106260_VIP4_pijian.png", "https://cdn3.qmovies.tv/youstar/op_1756777766_VIP4_beij_11111.png", "https://cdn3.qmovies.tv/youstar/op_1756777766_VIP4_beij_11111.png"));
        VIP_V1_USER_CARD_FRAME_MAP.put(5, new UserCardFrameData(0, 5, "https://cdn3.qmovies.tv/youstar/op_1755251385_vip_header_5.png", "https://cdn3.qmovies.tv/youstar/op_1756106304_vip5-pijian.png", "https://cdn3.qmovies.tv/youstar/op_1756106304_VIP5-beijing-baise.png", "https://cdn3.qmovies.tv/youstar/op_1756106304_VIP5-beijing-heise.png"));
        VIP_V1_USER_CARD_FRAME_MAP.put(6, new UserCardFrameData(0, 6, "https://cdn3.qmovies.tv/youstar/op_1755251385_vip_header_63x.png", "https://cdn3.qmovies.tv/youstar/op_1756106340_vip_infocard_rail62x.png", "https://cdn3.qmovies.tv/youstar/op_1756106527_vip6-beijng-baise.png", "https://cdn3.qmovies.tv/youstar/op_1756106340_vip6-beijing-heise.png"));
        VIP_V1_USER_CARD_FRAME_MAP.put(10, new UserCardFrameData(0, 10, "https://cdn3.qmovies.tv/youstar/op_1756778466_touxiaokuang.svga", "https://cdn3.qmovies.tv/youstar/op_1756867318_queen-pijian.webp", "https://cdn3.qmovies.tv/youstar/op_1756106566_queen-beijing-baise.png", "https://cdn3.qmovies.tv/youstar/op_1756106566_queen-beijing-heise.png"));

        VIP_V2_USER_CARD_FRAME_MAP.put(1, new UserCardFrameData(1, 1, "https://cdn3.qmovies.tv/youstar/op_1755743931_VIP1.webp", "https://cdn3.qmovies.tv/youstar/op_1755744474_VIP1.webp", "https://cdn3.qmovies.tv/youstar/op_1756726026_bj-vip1.png", "https://cdn3.qmovies.tv/youstar/op_1756726026_bj-vip1.png"));
        VIP_V2_USER_CARD_FRAME_MAP.put(2, new UserCardFrameData(1, 2, "https://cdn3.qmovies.tv/youstar/op_1755744244_VIP2.webp", "https://cdn3.qmovies.tv/youstar/op_1755744474_VIP2.webp", "https://cdn3.qmovies.tv/youstar/op_1756726026_bj-vip2.png", "https://cdn3.qmovies.tv/youstar/op_1756726026_bj-vip2.png"));
        VIP_V2_USER_CARD_FRAME_MAP.put(3, new UserCardFrameData(1, 3, "https://cdn3.qmovies.tv/youstar/op_1755744297_VIP3.webp", "https://cdn3.qmovies.tv/youstar/op_1755744474_VIP3.webp", "https://cdn3.qmovies.tv/youstar/op_1756726026_bj-vip3.png", "https://cdn3.qmovies.tv/youstar/op_1756726026_bj-vip3.png"));
        VIP_V2_USER_CARD_FRAME_MAP.put(4, new UserCardFrameData(1, 4, "https://cdn3.qmovies.tv/youstar/op_1755744297_VIP4.webp", "https://cdn3.qmovies.tv/youstar/op_1755744474_VIP4.webp", "https://cdn3.qmovies.tv/youstar/op_1756726027_bj-vip4.png", "https://cdn3.qmovies.tv/youstar/op_1756726027_bj-vip4.png"));
        VIP_V2_USER_CARD_FRAME_MAP.put(5, new UserCardFrameData(1, 5, "https://cdn3.qmovies.tv/youstar/op_1755744297_VIP5.webp", "https://cdn3.qmovies.tv/youstar/op_1755744474_VIP5.webp", "https://cdn3.qmovies.tv/youstar/op_1756726027_bj-vip5.png", "https://cdn3.qmovies.tv/youstar/op_1756726027_bj-vip5.png"));
        VIP_V2_USER_CARD_FRAME_MAP.put(6, new UserCardFrameData(1, 6, "https://cdn3.qmovies.tv/youstar/op_1755744297_VIP6.webp", "https://cdn3.qmovies.tv/youstar/op_1755744475_VIP6.webp", "https://cdn3.qmovies.tv/youstar/op_1756726026_bj-king.png", "https://cdn3.qmovies.tv/youstar/op_1756726026_bj-king.png"));
        VIP_V2_USER_CARD_FRAME_MAP.put(10, new UserCardFrameData(1, 10, "https://cdn3.qmovies.tv/youstar/op_1755744297_queen.webp", "https://cdn3.qmovies.tv/youstar/op_1755744475_car_queen750.webp", "https://cdn3.qmovies.tv/youstar/op_1756726026_bj-queen.png", "https://cdn3.qmovies.tv/youstar/op_1756726026_bj-queen.png"));


    }

    public UserInfoVO getActorInfo(UserInfoDTO userInfoDTO, boolean isFromOld) {
        String uid = userInfoDTO.getUid();
        String aid = userInfoDTO.getAid();
        int slang = userInfoDTO.getSlang();
        int versioncode = userInfoDTO.getVersioncode();
        String appPackageName = userInfoDTO.getApp_package_name();
        UserActorData userActorData = actorDao.getActorFromRedis(aid, UserActorData.class);
        if (null == userActorData) {
            logger.error("cannot find actor, aid={} ", aid);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        UserInfoVO userInfoVO = !isFromOld ? new UserInfoVO() : new PyUserInfoVO();

        userInfoVO.setName(userActorData.getName());
        int vipLevel = vipInfoDao.getIntVipLevel(aid);
        userInfoVO.setVipLevel(vipLevel);
        userInfoVO.setVipMedal(actorCommonService.getCommonVipMedal(aid, vipLevel));
        userInfoVO.setHead(ImageUrlGenerator.generateRoomUserUrl(userActorData.getHead(), vipLevel));
        userInfoVO.setMicFrame(micFrameRedis.getMicSourceFromCache(aid));
        userInfoVO.setDesc(userActorData.getDesc());
        List<String> newBanners = getBanners(userActorData, userInfoDTO);
        userInfoVO.setBanner(newBanners);
        userInfoVO.setGender(userActorData.getFb_gender() == 2 ? 2 : 1);
        userInfoVO.setCountry(userActorData.getCountry());
        userInfoVO.setAge(userActorData.getAge());
        long registerTime = new ObjectId(aid).getTimestamp() * 1000L;
        userInfoVO.setRegisterDay(DateHelper.ARABIAN.formatDateInDay(new Date(registerTime)));
        userInfoVO.setRid(userActorData.getRid());
        userInfoVO.setAlphaRid("");
        userInfoVO.setAlphaType(0);
        userInfoVO.setRidData(userActorData.getRidData());
        userInfoVO.setAid(aid);
        userInfoVO.setFollower(String.valueOf(followDao.getFollowsCountByMonGo(aid)));
        userInfoVO.setFollowed(String.valueOf(followDao.getFollowingCount(aid)));
        userInfoVO.setIsFollowed(Boolean.TRUE.equals(followDao.isFollowed(uid, aid)) ? 1 : 0);
        boolean isFriends = commonDao.isFriendsByIndex(uid, aid);
        userInfoVO.setFriends(Boolean.TRUE.equals(isFriends) ? 1 : 0);
        userInfoVO.setuLevel(userLevelDao.getUserLevel(aid));
        userInfoVO.setIsBeautifulRid(beautifulRidDao.isBeautifulRid(aid));
        userInfoVO.setSayHello(isSayHello(uid, aid, isFriends));

        int os = userActorData.getIntOs();
        if (!isFromOld) {
            fillJoinCarton(aid, os, userInfoVO);
            fillInRoom(aid, uid, userInfoVO, userActorData.getAccept_talk());
        } else {
            fillOldPyInfo(uid, aid, userInfoVO, userActorData);
        }
        List<Integer> labelList = userActorData.getLabelList();
        List<LabelInfoBeanVO> myLabelList = getLabelInfos(aid, slang, labelList, isFromOld);
        userInfoVO.setLabelList(myLabelList);
        fillLantan(aid, userInfoVO);
        fillDecorationVO(aid, userInfoVO);

        String videoUrl = userActorData.getVideoUrl();
        if (!StringUtils.isEmpty(userActorData.getVideoUrl())) {
            int vipOnlineSwitch = commonConfig.getSwitchConfigValue(CommonConfig.VIP_ONLINE_SWITCH_KEY, 0);
            long toShowVideoSwitch = actorConfigDao.getLongUserConfig(aid, ActorConfigDao.SHOW_VIDEO_IN_DETAIL, 0);
            if (vipOnlineSwitch == 0 || toShowVideoSwitch == 0 || vipLevel < UserInfoConstant.VISITOR_NEED_VIP_LEVEL) {
                videoUrl = "";
            }
        } else {
            videoUrl = "";
        }
        userInfoVO.setVideoUrl(videoUrl);
        userInfoVO.setImgVideoUrl(ImageUrlGenerator.generateSnapshotUrl(videoUrl, 0, 0));
        userInfoVO.setGuardianTopThree(guardService.getRankingList(aid, 1, 3, false));
        userInfoVO.setGiftTopFour(getTopFourRecevList(aid));
        userInfoVO.setHonorList(honorTitleService.getUserHonorTitleList(aid));
        userInfoVO.setEventHonorList(honorTitleService.getHonorTitleList(1));
        userInfoVO.setAchievementHonorList(honorTitleService.getHonorTitleList(2));
        userInfoVO.setGiftBannerList(homeBannerService.getHomeBannerList(uid, IndexBannerDao.TYPE_USER_INFO, slang, versioncode, appPackageName, os, false));
        if (userActorData.getFamilyId() > 0) {
            userInfoVO.setFamilyInfo(getFamilyInfo(userActorData.getFamilyId()));
        }
        userInfoVO.setIsNewUser(ActorUtils.isNewDeviceAccount(aid, userActorData.getFirstTnId()) ? 1 : 0);
        userInfoVO.setDevote(getDevote(aid));

        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                if (!uid.equals(aid)) {
                    userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(aid, UserLevelConstant.HOMEPAGE_VIEWED, uid));
                    visitorToEs(uid, aid);
                }
                if (userInfoDTO.getFromScene() == FromSceneConstant.SEARCH_KEY) {
                    HotSearchHistoryData data = new HotSearchHistoryData();
                    data.setUid(uid);
                    data.setSearchKey(aid);
                    data.setSearchType(HotSearchHistoryConstant.USER_TYPE);
                    data.setSearchNum(1);
                    data.setMtime(DateHelper.getNowSeconds());
                    int row = hotSearchHistoryDao.incNum(data);
                    if (row <= 0) {
                        hotSearchHistoryDao.insertOrUpdate(data);
                    }
                }
            }
        });
        return userInfoVO;
    }

    private List<String> getBanners(UserActorData userActorData, UserInfoDTO dto) {
        List<String> banners = userActorData.getBannerList();
        List<String> newBanners = new ArrayList<>();
//        List<String> newBanners = CollectionUtil.getPropertyList(banners, ImageUrlGenerator::generateChatUrl, null);
//        if (CollectionUtils.isEmpty(newBanners)) {
//            newBanners = new ArrayList<>();
//        }
        if (CollectionUtils.isEmpty(banners)) {
            banners = new ArrayList<>();
        }
        String cover = userActorData.getCover();
        if (!AppVersionUtils.versionCheck(844, dto)) {
            if (!StringUtils.isEmpty(cover)) {
//                cover = ImageUrlGenerator.generateChatUrl(cover);
                banners.add(0, cover);
            }
        } else {
            int len = 0;
            String oldHead = userActorData.getHead();
            if ((len = banners.size()) <= UserInfoConstant.MAX_BANNER_SIZE && !StringUtils.isEmpty(oldHead)
                    && !StringUtils.isEmpty(cover) && !oldHead.equals(cover) && !banners.contains(cover)) {
                if (len < UserInfoConstant.MAX_BANNER_SIZE) {
                    logger.info("844 info add cover uid:{} oldHead:{} oldCover:{} len:{}", dto.getUid(), oldHead, cover, len);
                    banners.add(0, cover);
                } else {
                    // 满8张移除head
                    boolean isRemoveHeadSuccess = banners.remove(oldHead);
                    if (isRemoveHeadSuccess) {
                        banners.add(0, cover);
                        logger.info("844 info add cover success uid:{} oldHead:{} oldCover:{} len:{} isRemoveHead:{}", dto.getUid(), oldHead, cover, len, isRemoveHeadSuccess);
                    } else {
                        logger.info("844 info add cover fail uid:{} oldHead:{} oldCover:{} len:{} isRemoveHead:{}", dto.getUid(), oldHead, cover, len, isRemoveHeadSuccess);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(banners)) {
            String head = userActorData.getHead();
            banners.add(0, head);
        }

        for (String item : banners) {
            newBanners.add(ImageUrlGenerator.generateChatUrl(item));
        }
        return newBanners;
    }

    private void fillOldPyInfo(String uid, String aid, UserInfoVO userInfoVO, UserActorData userActorData) {
        userInfoVO.setGold(0);
        userInfoVO.setBirthday(userActorData.getBirthday());
        userInfoVO.setCity("");
        userInfoVO.setAccept_talk(userActorData.getAccept_talk());
        userInfoVO.setVchat_status(userActorData.getVchat_status());
        userInfoVO.setAnchor(userActorData.getGender());
        userInfoVO.setIdentify(userInfoVO.getVipLevel() > 0 ? 1 : 0);
        userInfoVO.setNewFans(0);
        userInfoVO.setFollowerStr(userInfoVO.getFollower());
        userInfoVO.setFocus(0);

        String myRoom = RoomUtils.formatRoomId(aid);
        MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(myRoom);
        if (null != mongoRoomData) {
            userInfoVO.setAnnounce(mongoRoomData.getAnnounce());
            userInfoVO.setPrivi(mongoRoomData.getPrivi());
            userInfoVO.setFee(mongoRoomData.getFee());
            userInfoVO.setFeetype(mongoRoomData.getFeetype());
            userInfoVO.setTheme(mongoRoomData.getTheme());
            userInfoVO.setComp(mongoRoomData.getComp());
            userInfoVO.setPwd(!StringUtils.isEmpty(mongoRoomData.getPwd()) ? 1 : 0);
            userInfoVO.setMyroom(myRoom);
        } else {
            userInfoVO.setTopic("");
            userInfoVO.setAnnounce("");
            userInfoVO.setPrivi(1);
            userInfoVO.setFee(0);
            userInfoVO.setFeetype(1);
            userInfoVO.setTheme(0);
            userInfoVO.setComp(0);
            userInfoVO.setPwd(0);
            userInfoVO.setMyroom("");
        }
        userInfoVO.setRoom_level(3);
        int acceptTalk = userActorData.getAccept_talk();
        int sstatus = 0;// 0 TALK_SHOW_OFFLINE  1 TALK_SHOW_ONLINE  3 TALK_SHOW_SELF_ROOM  4 TALK_SHOW_OTHER_ROOM
        String roomId = "";
        if (acceptTalk == 1) {
            roomId = roomPlayerRedis.getActorRoomStatus(aid);
            if (StringUtils.isEmpty(roomId)) {
                roomId = "";
                int nettyStatus = userOnlineRedis.isOnline(aid);
                if (nettyStatus > 0) {
                    sstatus = 1;
                }
            } else {
                String rUid = roomId.length() >= 2 ? RoomUtils.getRoomHostId(roomId) : "";
                if (rUid.equals(aid)) {
                    sstatus = 3;
                } else {
                    sstatus = 4;
                }
            }
        }
        userInfoVO.setSstatus(sstatus);
        userInfoVO.setRoomId(roomId);
        userInfoVO.setReceive("0K");
        userInfoVO.setBadge(badgeDao.getBadgeList(aid));
        long vCount = hvElasticsearchService.getVisitorCount(aid, false);
        userInfoVO.setVisitorsNum(vCount);
        userInfoVO.setVisitorsNumStr(numToHuman(vCount));
        int newVisitor = 0;
        if (uid.equals(aid)) {
            newVisitor = (int) hvElasticsearchService.getVisitorCount(aid, true);
        }
        userInfoVO.setNewVisitor(newVisitor);
        userInfoVO.setNewVisitorStr(newVisitor > 0 ? String.valueOf(newVisitor) : "");

    }

    private void fillInRoom(String aid, String uid, UserInfoVO userInfoVO, int acceptTalk) {
        ActorRoomStatusVO actorRoomStatusVO = actorCommonService.getActorRoomStatus(uid, aid, acceptTalk);
        userInfoVO.setInRoomId(actorRoomStatusVO == null ? "" : actorRoomStatusVO.getInRoomId());
        userInfoVO.setInRoomHead(actorRoomStatusVO == null ? "" : actorRoomStatusVO.getInRoomHead());
        userInfoVO.setInRoomName(actorRoomStatusVO == null ? "" : actorRoomStatusVO.getInRoomName());
        // if (acceptTalk == 1 && !blackListDao.isBlock(aid, uid) && !roomBlacklistDao.isBlock(RoomUtils.formatRoomId(aid), uid)) {
        //     String roomId = roomPlayerRedis.getActorRoomStatus(aid);
        //     if (!StringUtils.isEmpty(roomId) && RoomUtils.isVoiceRoom(roomId)) {
        //         if (!whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
        //             inRoomId = roomId;
        //             MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(inRoomId);
        //             if (mongoRoomData == null) {
        //                 logger.info("fillInRoom fail mongoRoomData is null aid:{} inRoomId:{}", aid, inRoomId);
        //                 inRoomId = "";
        //             } else {
        //                 inRoomName = mongoRoomData.getName();
        //                 inRoomHead = ImageUrlGenerator.generateRoomUserUrl(mongoRoomData.getHead());
        //             }
        //         } else {
        //             logger.info("uid:{} aid:{} aidInRoomId:{} is test room", uid, aid, inRoomId);
        //         }
        //     }
        // }
        // userInfoVO.setInRoomId(inRoomId);
        // userInfoVO.setInRoomHead(inRoomHead);
        // userInfoVO.setInRoomName(inRoomName);
    }

    private List<LabelInfoBeanVO> getLabelInfos(String aid, int slang, List<Integer> myLabelList, boolean isFromOld) {
        List<LabelInfoBeanVO> labelList = new ArrayList<>();
        List<UserLabelData> userLabelDatas = userLabelDao.getUserLabelFromRedis(aid, myLabelList);
        for (UserLabelData data : userLabelDatas) {
            LabelInfoBeanVO labelInfoBeanVO = !isFromOld ? new LabelInfoBeanVO() : new PyLabelInfoBeanVO();
            labelInfoBeanVO.setLabelId(data.getLabelId());
            labelInfoBeanVO.setLabelName(slang == 2 ? data.getLabelArname() : data.getLabelName());
            labelInfoBeanVO.setLabelArName(data.getLabelArname());
            labelInfoBeanVO.setLabelColor(data.getRgba());
            labelList.add(labelInfoBeanVO);
        }
        return labelList;
    }

    private void fillLantan(String aid, UserInfoVO userInfoVO) {
        RamadanVO ramadanVO = new RamadanVO();
        List<LantanStatusVO> ramadanList = new ArrayList<>();
        LantanStatusVO lantanStatusVO1 = new LantanStatusVO();
        lantanStatusVO1.setIcon(ZHAIYUEDENG1);
        lantanStatusVO1.setStatus(userInfoShowRedis.getLantanStatus(aid, UserInfoShowRedis.KEBAB_ZSET_TYPE));
        LantanStatusVO lantanStatusVO2 = new LantanStatusVO();
        lantanStatusVO2.setIcon(ZHAIYUEDENG2);
        lantanStatusVO2.setStatus(userInfoShowRedis.getLantanStatus(aid, UserInfoShowRedis.SWEET_ZSET_TYPE));
        LantanStatusVO lantanStatusVO3 = new LantanStatusVO();
        lantanStatusVO3.setIcon(ZHAIYUEDENG3);
        lantanStatusVO3.setStatus(userInfoShowRedis.getLantanStatus(aid, UserInfoShowRedis.SHAWARMA_ZSET_TYPE));
        ramadanList.add(lantanStatusVO1);
        ramadanList.add(lantanStatusVO2);
        ramadanList.add(lantanStatusVO3);
        ramadanVO.setRamadanList(ramadanList);
        userInfoVO.setRamadanVo(ramadanVO);
    }


    private void fillDecorationVO(String aid, UserInfoVO userInfoVO) {


        // 设置装饰佩戴
        DecorationVO decorationVO = new DecorationVO();
        int curTime = DateHelper.getNowSeconds();
        if (curTime >= DECORATE_START_TIME && curTime <= DECORATE_END_TIME) {
            decorationVO.setActivityUrl(ServerConfig.isProduct() ? "https://static.youstar.live/plant_date_palm/?activityId=6446682cb9607fdbcb11256f" : "https://test2.qmovies.tv/plant_date_palm/?activityId=6446682cb9607fdbcb11256f");
            String decorateUrl = userInfoShowRedis.getDecoration(aid);
            decorationVO.setDecorateUrl(StringUtils.isEmpty(decorateUrl) ? DECORATE_BG_URL : decorateUrl);
            decorationVO.setDecorateSwitch(StringUtils.isEmpty(decorateUrl) ? 0 : 1);

        } else {
            decorationVO.setDecorateSwitch(0);
        }

        userInfoVO.setDecorationVO(decorationVO);

        // 设置浮萍
        FloatScreenSourceData floatScreenSourceData = userInfoShowRedis.getFloatScreen(aid);
        if (floatScreenSourceData != null) {
            userInfoVO.setFloatScreen(floatScreenSourceData.getScreen_source());
            userInfoVO.setScreenSourceVap(floatScreenSourceData.getScreenSourceVap());
        } else {
            userInfoVO.setFloatScreen("");
            userInfoVO.setScreenSourceVap("");
        }
    }

    private int isSayHello(String uid, String aid, boolean isFriends) {
        return 1;
//        int isSayHello = 0;
//        if (isFriends || friendsListRedis.isSayHello(uid, aid) || friendsListRedis.isDelete(uid, aid)) {
//            isSayHello = 1;
//        }
//        return isSayHello;
    }

    private void fillJoinCarton(String aid, int os, UserInfoVO userInfoVO) {
        List<ActorJoinCartonVO> joinCartonList = new ArrayList<>();
        List<JoinCartonData> joinCartonDataList = joinCartonDao.findList(aid);
        ActorJoinCartonVO wearActorJoinCarton = null;
        if (!CollectionUtils.isEmpty(joinCartonDataList)) {
            for (JoinCartonData joinCartonData : joinCartonDataList) {
                int joinId = joinCartonData.getJoin_carton_id();
                if (joinId == 1) {
                    continue;
                }
                JoinSourceData sourceData = joinSourceDao.getSourceData(joinId);
                if (null != sourceData) {
                    ActorJoinCartonVO actorJoinCartonVO = new ActorJoinCartonVO();
                    actorJoinCartonVO.setJoinIcon(sourceData.getJoin_icon());
                    actorJoinCartonVO.setSourceType(sourceData.getSource_type());
                    actorJoinCartonVO.setTimeLong(sourceData.getShow_time());
                    actorJoinCartonVO.setJoinId(joinId);
//                    if (os == ClientOS.IOS) {
//                        actorJoinCartonVO.setSourceUrl(sourceData.getIos_url());
//                        actorJoinCartonVO.setSourceMd5(sourceData.getIos_md5());
//                    } else {
//                        actorJoinCartonVO.setSourceUrl(sourceData.getSource_url());
//                        actorJoinCartonVO.setSourceMd5(sourceData.getSource_md5());
//                    }
                    actorJoinCartonVO.setSourceUrl(sourceData.getSource_url());
                    actorJoinCartonVO.setSourceMd5(sourceData.getSource_md5());
                    joinCartonList.add(actorJoinCartonVO);
                    if (joinCartonData.getStatus() == 1) {
                        wearActorJoinCarton = new ActorJoinCartonVO();
                        BeanUtils.copyProperties(actorJoinCartonVO, wearActorJoinCarton);
                    }

                }
            }
        }
        userInfoVO.setWearActorJoinCarton(wearActorJoinCarton);
        userInfoVO.setJoinCartonList(joinCartonList);
    }

    private void visitorToEs(String uid, String aid) {
        if (!uid.equals(aid)) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            GeneralConfActorData generalConfActorData = actorData.getGeneralConfActorData();
            int isInvisible = generalConfActorData == null ? 0 : generalConfActorData.getInvisible();
            int nowSeconds = DateHelper.getNowSeconds();
            String dateNum = DateHelper.DEFAULT.formatDateInDay2();
            List<HomeVisitorEs> homeVisitorEsList = hvElasticsearchService.getHomeVisitorDetails(uid, aid);
            if (!CollectionUtils.isEmpty(homeVisitorEsList)) {
                HomeVisitorEs homeVisitorEs = homeVisitorEsList.get(0);
//                logger.info("getHomeVisitorDetails result homeVisitorEs={}", homeVisitorEs);
                int times = homeVisitorEs.getTimes() == null ? 0 : homeVisitorEs.getTimes();
                hvElasticsearchService.updateDataByDocId(homeVisitorEs.getId(), times, isInvisible, nowSeconds, dateNum);
            } else {
                HomeVisitorEs homeVisitorEs = new HomeVisitorEs();
                homeVisitorEs.setUid(uid);
                homeVisitorEs.setAid(aid);
                homeVisitorEs.setTimes(1);
                homeVisitorEs.setInvisible(isInvisible);
                homeVisitorEs.setMtime(nowSeconds);
                homeVisitorEs.setCtime(nowSeconds);
                homeVisitorEs.setDate_num(dateNum);
                homeVisitorEs.setIs_new(1);
                homeVisitorEs.setPerson_new(1);
                hvElasticsearchService.insert(homeVisitorEs);
//                logger.info("not find homeVisitorEs insert new homeVisitorEs={}",homeVisitorEs);
//                PushNewVisitorData pushNewVisitorData = new PushNewVisitorData();
//                pushNewVisitorData.setItem(NEW_VISITOR_ITEM);
//                pushNewVisitorData.setUid(uid);
//                pushNewVisitorData.setAid(aid);
//                RedisTaskService.broadcastMessage(VISITOR_PROFILE_PERSON_NAME, pushNewVisitorData);
                long newCount = hvElasticsearchService.getVisitorCount(aid, true);
                NewVisitorMsg pushMsg = new NewVisitorMsg();
                pushMsg.setUid(uid);
                pushMsg.setNewVisitor((int) newCount);
                roomWebSender.sendPlayerWebMsg("", "", aid, pushMsg, false);

            }
            hvElasticsearchService.updateDataByQuery(uid, aid);
        }
    }

    public UserCardVO getActorCard(UserCardDTO dto) {
        String aid = dto.getAid();
        String uid = dto.getUid();
        String roomId = dto.getRoomId();
        // 填充用户基本信息
        UserCardVO vo = setBasicUserInfo(uid, aid);
        vo.setUlvl(userLevelDao.getUserLevel(aid));
        // 设置勋章相关信息
        setBadgeListInfo(vo, dto);
        vo.setNameV2(vo.getName());
        int joinCartonId = getJoinCartonId(aid);
        vo.setJoinCartonId(joinCartonId);
        String timeMs = "0";
        String smallIcon = "";
        String rippleUrl = rippleDao.getRippleUrl(roomId, aid);
        if (joinCartonId > 1) {
            String values = joinSourceDao.getSource(String.valueOf(joinCartonId));
            String[] split = values.split("\\?\\?");
            timeMs = split[0];
            smallIcon = split[1];
        }
        vo.setRippleUrl(rippleUrl);
        vo.setCartonTimes(timeMs);
        vo.setSmallIcon(smallIcon);
        vo.setSourceType(getSourceType(joinCartonId));
        if (vo.getRideOption() == 1) {
            vo.setJoinCartonId(0);
        }
        vo.setJoinCartonType(getCartonType(joinCartonId));
        vo.setIsBeautifulRid(beautifulRidDao.isBeautifulRid(aid));
        vo.setDevote(getDevote(aid));
        vo.setMicFrame(micFrameRedis.getMicSourceFromCache(aid));
        RoomMemberData memberData = roomMemberDao.findData(dto.getRoomId(), aid);
        vo.setIsMember(memberData != null ? 1 : 0);
        setRoomUserInfo(vo, aid, roomId, memberData);
        MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(roomId);
        if (mongoRoomData != null && mongoRoomData.getRoom_type() == 1) {
            vo.setForbidEnTime(0);
            vo.setMicForbidEnTime(0);
        } else {
            vo.setForbidEnTime(micFrameRedis.getForbidTime(roomId, aid));
            if (!AppVersionUtils.versionCheck(843, dto)) {
                RoomMicListVo roomMic = roomMicRedis.getRoomMicFromRedis(roomId);
                if (null != roomMic) {
                    for (RoomMicInfoObject micObj : roomMic.getList()) {
                        if (null != micObj.getUser() && aid.equals(micObj.getUser().getAid()) && micObj.getMute() == 1) {
                            vo.setMicForbidEnTime(DateHelper.getNowSeconds() + (int) TimeUnit.DAYS.toSeconds(3));
                        }
                    }
                }
            }
        }
        vo.setIsFollowed(followDao.isFollowed(uid, aid) ? 1 : 0);
        vo.setFriends(commonDao.isFriendsByIndex(uid, aid) ? 1 : 0);
        vo.setBlock(blackListDao.anyBlock(uid, aid));
        vo.setVipLevelV2(vipInfoDao.getIntVipLevel(aid));
        vo.setVipMedal(actorCommonService.getCommonVipMedal(aid, vo.getVipLevelV2()));
        vo.setShowRookie(0);
        if (vo.getVipLevelV2() != 0 && vo.getVipLevelV2() != 10) {
            char a = 7;
            vo.setName("vip" + vo.getVipLevelV2() + a + vo.getNameV2());
        }
        if (RoomUtils.isGameRoom(roomId)) {
            UserCardVO.UserGameInfo userGameInfo;
            GameRoomUserCareerData careerData = gameRoomUserCareerDao.getCareerDataByUid(aid);
            if (careerData != null) {
                userGameInfo = new UserCardVO.UserGameInfo();
                BeanUtils.copyProperties(careerData, userGameInfo);
                if (userGameInfo.getJoinNum() > 0 && userGameInfo.getWinNum() > 0) {
                    double ret = ArithmeticUtils.div((double) (userGameInfo.getWinNum() * 100),
                            (double) (userGameInfo.getJoinNum()), 2); // 0.6533
                    userGameInfo.setWinRate(ret + "%");
                } else {
                    userGameInfo.setWinRate("0%");
                }
            } else {
                userGameInfo = new UserCardVO.UserGameInfo(0L, 0L, 0L, "0");
            }
            vo.setUserGameInfo(userGameInfo);
        }
        // 设置守护者前三名
        vo.setGuardianTopThree(guardService.getRankingList(aid, 1, 3, false));
        setUserCardFrameData(vo, dto);
        return vo;
    }

    private void setUserCardFrameData(UserCardVO vo, UserCardDTO dto) {
        String aid = dto.getAid();
        int vipLevel = vo.getVipLevelV2();
        if (vipLevel <= 0){
            return;
        }
        int vipVersion = actorCommonService.getVipVersion(aid);
        vo.setUserCardFrame(vipVersion > 0 ? VIP_V2_USER_CARD_FRAME_MAP.get(vipLevel) : VIP_V1_USER_CARD_FRAME_MAP.get(vipLevel));
    }

    private void setBadgeListInfo(UserCardVO vo, UserCardDTO dto) {
        String aid = dto.getAid();
        // 获取用户勋章列表并设置勋章数量，避免多次查询数据库
        List<BadgeData> badgeDataList = badgeDao.getAllBadgeList(aid);
        if (CollectionUtils.isEmpty(badgeDataList)) {
            vo.setBadgeCount(0);
            vo.setBadgeModelList(Collections.emptyList());
            vo.setBadge(Collections.emptyList());
            return;
        }
        vo.setBadgeCount(badgeDataList.size());
        // 获取已佩戴的勋章列表
        List<BadgeData> ownBadgeDataList = badgeDataList.stream().filter(badge -> badge.getStatus() != 0).sorted(Comparator.comparing(BadgeData::getStatus)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ownBadgeDataList)) {
            vo.setBadgeModelList(Collections.emptyList());
            vo.setBadge(Collections.emptyList());
            return;
        }
        List<Integer> badgeIdList = ownBadgeDataList.stream().map(BadgeData::getBadge_id).collect(Collectors.toList());
        Map<Integer, BadgeListData> badgeListDataList = badgeListDao.findBadgesListByIds(badgeIdList).stream().collect(Collectors.toMap(BadgeListData::getBadge_id, Function.identity()));
        List<BadgeListDTO> badgeListDTOList = new ArrayList<>();
        List<String> badgeIconList = new ArrayList<>();
        for (BadgeData badgeData : ownBadgeDataList) {
            BadgeListData data = badgeListDataList.get(badgeData.getBadge_id());
            if (data == null) {
                continue;
            }
            BadgeListDTO badgeListDTO = badgeService.buildBadgeListDTO(dto.getSlang(), data);
            badgeListDTO.setGainTime(badgeData.getGet_time());
            badgeListDTOList.add(badgeListDTO);
            badgeIconList.add(data.getSmall_icon());
        }
        vo.setBadge(badgeIconList);
        vo.setBadgeModelList(badgeListDTOList);
    }

    private void setRoomUserInfo(UserCardVO vo, String aid, String roomId, RoomMemberData memberData) {
        vo.setRole(getRole(aid, roomId, memberData));
        vo.setRoleData(0);
        if (memberData != null && memberData.getUtype() == 4) {
            vo.setViceHost(1);
        }
    }

    private int getRole(String uid, String roomId, RoomMemberData memberData) {
        if (roomId != null && roomId.length() >= 2 && RoomUtils.getRoomHostId(roomId).equals(uid)) {
            return RoomRoleType.HOST;
        }
        if (memberData != null) {
            if (memberData.getUtype() == 2 || memberData.getUtype() == 4) {
                return RoomRoleType.MANAGER;
            } else if (memberData.getUtype() == 1) {
                return RoomRoleType.MEMBER;
            }
        }
        return RoomRoleType.AUDIENCE;
    }

    private Integer getImportantUser(String aid) {
        if (rechargeRedis.isRechargeUserByCache(aid)) {
            return 1;
        }
        UserRegisterInfoData register = userRegisterDao.getRegister(aid);
        if (register != null) {
            String strModels = backstageConfigDao.getConfigData(BackStageConfigConstant.HIGN_END_MODELS);
            JSONObject jsonObject = JSONObject.parseObject(strModels);
            if (jsonObject.getJSONArray("HighEndModels").contains(register.getModel())) {
                return 1;
            }
        }
        return 0;
    }

    private String getDevote(String aid) {
        BeansTotalDevoteData data = beansTotalDevoteDao.getData(aid);
        if (data == null || data.getBeans() == 0) {
//            PushUserCostBeansData pushUserCostBeansData = new PushUserCostBeansData();
//            pushUserCostBeansData.setUid(aid);
//            pushUserCostBeansData.setBeans(0);
//            pushUserCostBeansData.setActive(1);
//            RedisTaskService.broadcastMessage(USER_COST_BEANS, pushUserCostBeansData);
            return "0";
        }
        return numToHuman(data.getBeans());
    }

    private String numToHuman(long num) {
        BigDecimal beans = new BigDecimal(String.valueOf(num));
        BigDecimal oneThousand = new BigDecimal("1000");
        if (beans.compareTo(oneThousand) < 0) {
            return beans.toString();
        } else if (beans.divide(oneThousand, 1, BigDecimal.ROUND_HALF_UP).compareTo(oneThousand) < 0) {
            return beans.divide(oneThousand, 1, BigDecimal.ROUND_HALF_UP).toString() + "K";
        } else {
            return beans.divide(oneThousand.multiply(oneThousand), 1, BigDecimal.ROUND_HALF_UP).toString() + "M";
        }
    }

    /**
     * @param joinCartonId
     * @return 1 vip, 2 honor, 3 buy
     */
    private int getCartonType(int joinCartonId) {
        if (10 < joinCartonId && joinCartonId < 100) {
            return 2;
        } else if (joinCartonId >= 100) {
            return 3;
        } else {
            return 1;
        }
    }

    private int getSourceType(int joinCartonId) {
        JoinSourceData sourceData = joinSourceDao.getSourceData(joinCartonId);

        return sourceData != null ? sourceData.getSource_type() : 0;
    }

    private int getJoinCartonId(String aid) {
        JoinCartonData joinCartonData = joinCartonDao.findData(aid, 1);
        if (joinCartonData != null) {
            return joinCartonData.getJoin_carton_id();
        }
        return 0;
        // status 为0的特殊处理
//        JoinCartonData oldData = joinCartonDao.findOldData(aid, 1);
//        return oldData != null ? 1 : 0;
    }

    private UserCardVO setBasicUserInfo(String uid, String aid) {
        ActorData userInfo = null;
        if (uid.equals(aid)) {
            userInfo = actorDao.getActorData(aid);
        } else {
            userInfo = actorDao.getActorDataFromCache(aid);
        }
        if (userInfo == null) {
            logger.error("can not find actor data. uid={}", aid);
            throw new CommonException(UserHttpCode.PARAM_ERROR);
        }
        UserCardVO vo = new UserCardVO();
        vo.setName(userInfo.getName());
        vo.setHead(!StringUtils.isEmpty(userInfo.getHead()) ? ImageUrlGenerator.generateRoomUserUrl(userInfo.getHead(), vipInfoDao.getIntVipLevelFromCache(aid)) : "");
        vo.setDesc(userInfo.getDesc());
        vo.setGender(userInfo.getFb_gender());
        vo.setAge(userInfo.getAge());
        vo.setCountry(userInfo.getCountry());
        vo.setRid(userInfo.getRid());
        vo.setOs(userInfo.getIntOs());
        vo.setValid(userInfo.getValid());
        vo.setRideOption(userInfo.getRide_option());
        vo.setAid(aid);
        vo.setAlphaRid("");
        vo.setAlphaType(0);
        vo.setRidData(userInfo.getRidData());
        vo.setIsNewUser(ActorUtils.isNewDeviceAccount(aid, userInfo.getFirstTnId()) ? 1 : 0);
        return vo;
    }

    public MeUserInfoVO getMeUserInfo(HttpEnvData httpEnvData) {
        String uid = httpEnvData.getUid();
        int slang = httpEnvData.getSlang();
        int os = httpEnvData.getOs();
        MeUserInfoVO userInfoVO = new MeUserInfoVO();
        MongoUserActorData userActorData = commonDao.findOne(new Query(Criteria.where("_id").is(new ObjectId(uid))), MongoUserActorData.class);

        String cover = ImageUrlGenerator.generateNormalUrl(userActorData.getCover());
        int follower = followDao.getFollowsCountByMonGo(uid);
        int vipLevel = vipInfoDao.getIntVipLevel(uid);
        String head = vipInfoDao.generateVipUrlByLevel(vipLevel, userActorData.getHead(), ImageUrlGenerator.MODE_300);
        NewUserHonorData honor = newUserHonorDao.findData(uid);
        int honorLevel = honor == null ? 0 : honor.getHonor_level();
        String honorName = baseInitData.getHonorNameByLevel(honorLevel);
        int nextBeans = baseInitData.getNextHonorBeans(honorLevel);
//        logger.info("uid:{} honor:{} honorName:{} nextBeans:{}", uid, honor, honorName, nextBeans);
        int fillDone = userActorData.getFillStatus("fill_done", 1);
        int fillGender = userActorData.getFillStatus("fill_gender", 1);
        int headIndex = userActorData.getFillStatus("headIndex", 0);
        int fillAlbum = userActorData.getFillStatus("fill_album", 0);
        int fillInterests = userActorData.getFillStatus("fill_interests", 0);
        int fillAboutMe = userActorData.getFillStatus("fill_about_me", 0);
        SpecialGustData specialGustData = specialGustDao.findData(uid);
        int useSpecial = 0;
        String specialAccount = "";
        if (specialGustData != null) {
            useSpecial = 1;
            specialAccount = specialGustData.get_id();
        } else {
            if (vipLevel >= 5) {
                useSpecial = 1;
            }
        }
        logger.info("uid:{} specialGustData:{} useSpecial:{} specialAccount:{}", uid, specialGustData, useSpecial, specialAccount);
        userInfoVO.setrLevel(honorName);
        userInfoVO.setrTotal(nextBeans);
        userInfoVO.setName(userActorData.getName());
        userInfoVO.setHead(head);
        userInfoVO.setMicFrame(micFrameRedis.getMicSourceFromCache(uid));
        userInfoVO.setDesc(userActorData.getDesc());
        List<String> banners = userActorData.getBannerList();
        List<String> newBanners = new ArrayList<>();
        if (!CollectionUtils.isEmpty(banners)) {
            String oldHead = userActorData.getHead();
            String oldCover = userActorData.getCover();
            int len = banners.size();
            if (AppVersionUtils.versionCheck(844, httpEnvData) && len <= UserInfoConstant.MAX_BANNER_SIZE
                    && !StringUtils.isEmpty(oldCover) && !StringUtils.isEmpty(oldHead) && !oldHead.equals(oldCover)
                    && !banners.contains(oldCover)) {
                if (len < UserInfoConstant.MAX_BANNER_SIZE) {
                    newBanners.add(0, ImageUrlGenerator.generateNormalUrl(oldCover));
                    logger.info("844 me add cover success uid:{} oldHead:{} oldCover:{} len:{}", uid, oldHead, oldCover, len);
                } else {
                    // 满8张移除head
                    boolean isRemoveHeadSuccess = banners.remove(oldHead);
                    if (isRemoveHeadSuccess) {
                        newBanners.add(0, ImageUrlGenerator.generateNormalUrl(oldCover));
                        logger.info("844 me add cover success uid:{} oldHead:{} oldCover:{} len:{} isRemoveHead:{}", uid, oldHead, oldCover, len, isRemoveHeadSuccess);
                    } else {
                        logger.info("844 me add cover fail uid:{} oldHead:{} oldCover:{} len:{} isRemoveHead:{}", uid, oldHead, oldCover, len, isRemoveHeadSuccess);
                    }
                }
            }
            for (String item : banners) {
                newBanners.add(ImageUrlGenerator.generateNormalUrl(item));
//                newBanners.add(vipInfoDao.generateVipUrlByLevel(vipLevel, item, ImageUrlGenerator.MODE_300));
            }
        }
        userInfoVO.setBanner(newBanners);
        userInfoVO.setCover(StringUtils.isEmpty(cover) ? head : cover);
        userInfoVO.setGender(userActorData.getFb_gender() == 2 ? 2 : 1);
        userInfoVO.setCountry(userActorData.getCountry());
        userInfoVO.setAge(userActorData.getAge());
        userInfoVO.setRid(userActorData.getRid());
        userInfoVO.setAlphaRid("");
        userInfoVO.setAlphaType(0);
        userInfoVO.setRidData(userActorData.getRidData());
        userInfoVO.setAid(uid);
        userInfoVO.setGold(userActorData.getGold());
        userInfoVO.setBirthday(userActorData.getBirthday());
        userInfoVO.setCity("");
        userInfoVO.setAccept_talk(userActorData.getAccept_talk());
        userInfoVO.setVchat_status(userActorData.getVchat_status());
        userInfoVO.setAnchor(userActorData.getGender());
        userInfoVO.setIdentify(userInfoVO.getVipLevel() > 0 ? 1 : 0);
        userInfoVO.setIdentify_switch(0);
        userInfoVO.setSvip_subscription(os == ClientOS.ANDROID ? 2 : 0);

        userInfoVO.setVipLevel(vipLevel);
        userInfoVO.setVipMedal(actorCommonService.getCommonVipMedal(uid, vipLevel));
        userInfoVO.setFollower(follower);
        userInfoVO.setFollowerStr(String.valueOf(follower));
        userInfoVO.setFollowed(followDao.getFollowingCount(uid));
        userInfoVO.setNewFans(followDao.getNewFollowsCount(uid));
        userInfoVO.setFriends(friendsDao.getFriendCount(uid));
        userInfoVO.setuLevel(userLevelDao.getUserLevel(uid));
        userInfoVO.setBadge(badgeDao.getBadgeList(uid));
        userInfoVO.setIsBeautifulRid(beautifulRidDao.isBeautifulRid(uid));
        List<Integer> labelList = userActorData.getLabelList();
        List<LabelInfoBeanVO> myLabelList = getLabelInfos(uid, slang, labelList, true);
        userInfoVO.setLabelList(myLabelList);

        long vCount = hvElasticsearchService.getVisitorCount(uid, false);
        int newVisitor = (int) hvElasticsearchService.getVisitorCount(uid, true);
        userInfoVO.setVisitorsNum(vCount);
        userInfoVO.setVisitorsNumStr(numToHuman(vCount));
        userInfoVO.setNewVisitor(newVisitor);
        userInfoVO.setNewVisitorStr(newVisitor > 0 ? numToHuman(newVisitor) : "");

        userInfoVO.setFillDone(fillDone);
        userInfoVO.setFillGender(fillGender);
        userInfoVO.setFillName(fillDone);
        userInfoVO.setFillBirth(fillDone);
        userInfoVO.setHeadIndex(headIndex);
        userInfoVO.setProcess(completedProgress(userActorData));
        userInfoVO.setHeart_task_url("");

        userInfoVO.setSpecial_guest(useSpecial);
        userInfoVO.setAccount(specialAccount);
        userInfoVO.setDiscount(payRedis.getDiscount());
        userInfoVO.setBeans(userActorData.getBeans());
        userInfoVO.setDiamonds(userActorData.getBeans());
        userInfoVO.setDiamondsStr(MatchUtils.formatDevotes(userActorData.getBeans(), RoundingMode.FLOOR));
        userInfoVO.setCoins(userActorData.getHeart_got());
        userInfoVO.setCoinsStr(MatchUtils.formatDevotes(userActorData.getHeart_got(), RoundingMode.FLOOR));
        userInfoVO.setBonus(getBonusStatus(uid));
        fillRoomInfo(userInfoVO);
        userInfoVO.setGetResType(goodsListHomeRedis.getResTypeFromRedis(uid));
        userInfoVO.setMessageTopSwitch(actorConfigDao.getIntRoomConfig(uid, ActorConfigDao.MESSAGE_TOP_SWITCH, 1));

        if (AppVersionUtils.versionCheck(844, httpEnvData)) {
            userInfoVO.setImgVideoUrl(StringUtils.isEmpty(userActorData.getVideoUrl()) ? "" : ImageUrlGenerator.generateSnapshotUrl(userActorData.getVideoUrl(), 0, 0));
            userInfoVO.setVideoUrl(StringUtils.isEmpty(userActorData.getVideoUrl()) ? "" : userActorData.getVideoUrl());
            userInfoVO.setStarSign(getConstellation(userActorData.getBirthday()));

            int vipOnlineSwitch = commonConfig.getSwitchConfigValue(CommonConfig.VIP_ONLINE_SWITCH_KEY, 0);
            long toShowVideoSwitch = actorConfigDao.getLongUserConfig(uid, ActorConfigDao.SHOW_VIDEO_IN_DETAIL, 0);
            userInfoVO.setVipOnlineSwitch(vipOnlineSwitch);
            userInfoVO.setShowVideoSwitch(toShowVideoSwitch);

            userInfoVO.setVideoVip(UserInfoConstant.VISITOR_NEED_VIP_LEVEL);
            userInfoVO.setFillAlbum(fillAlbum == 0 && CollectionUtils.isEmpty(banners) ? 0 : 1);
            userInfoVO.setFillInterests(fillInterests == 0 && CollectionUtils.isEmpty(myLabelList) ? 0 : 1);
            userInfoVO.setFillAboutMe(fillAboutMe == 0 && StringUtils.isEmpty(userActorData.getDesc()) ? 0 : 1);
        }
        userInfoVO.setMaxVersionCode(commonConfig.getSwitchConfigValue(CommonConfig.MAX_VERSION_CODE, 999));
        JSONObject config = commonConfig.getSwitchConfig();
        userInfoVO.setMaxVersionName(config.getString(CommonConfig.MAX_VERSION_NAME));
        userInfoVO.setFeedbackStatus(userFeedbackRedis.getFeedbackStatus(uid));
        userInfoVO.setHasTaskReward(getHasTaskReward(httpEnvData));

        if (userActorData.getFamilyId() <= 0) {
            // ID1012006 4、Me页面非家族长、主播不展示加入栏
            // userInfoVO.setFamilyTop3(familyDevoteDao.getDailyTop3Family());
        } else {
            FamilyMemberData familyMember = familyMemberDao.selectByUidFromCache(uid);
            if (null == familyMember) {
                logger.error("actor has exited family. uid={} familyId={}", uid, userActorData.getFamilyId());
            } else {
                FamilyData familyData = familyDao.selectById(familyMember.getFamilyId());
                userInfoVO.setFamilyId(familyData.getRid());
                if (familyMember.isAdmin()) {
                    userInfoVO.setFamilyUnread(familyRequestDao.getUnprocessedCount(familyData.getId()));
                }
            }
        }
        userInfoVO.setInvitationUrl(URL_INVITE_URL);

        userInfoVO.setMaleHeadList(BaseInitData.SYS_NEW_MALE_HEAD_LIST);
        userInfoVO.setFemaleHeadList(BaseInitData.SYS_NEW_FEMALE_HEAD_LIST);
        userInfoVO.setIsNewUser(ActorUtils.isNewDeviceAccount(uid, userActorData.getFirstTnId()) ? 1 : 0);


        SuperQueenEntryConfigData entryConfig = configCenterService.getBeanByKey
                (ConfigCenterLabelKeyConstant.USER_ACTIVITY_SERVER,
                        ConfigCenterKeyConstant.ACTIVITY_SUPER_QUEEN_CONFIG, SuperQueenEntryConfigData.class);
        int curTime = DateHelper.getNowSeconds();
//        logger.info("entryConfig:{}", JSON.toJSONString(entryConfig));
        if (entryConfig.getStartTime() <= curTime && curTime <= entryConfig.getEndTime()
                && entryConfig.getEntryConfig() != 3) {
            if ((entryConfig.getEntryConfig() == 1 && userActorData.getFb_gender() == 2)
                    || entryConfig.getEntryConfig() == 2
                    || (entryConfig.getEntryConfig() == 4 && whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID))) {
                List<Integer> queenBadgeList = entryConfig.getBadgeList(); //2923,2922,2921
                String superQueenUrl = entryConfig.getSuperQueenUrl();
                if (!CollectionUtils.isEmpty(queenBadgeList) && !StringUtils.isEmpty(superQueenUrl)) {
                    String activityId = null;
                    try {
                        UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(superQueenUrl).build();
                        activityId = uriComponents.getQueryParams().get("activityId").get(0);
                    } catch (Exception e) {
                        logger.error("parse activityId error superQueenUrl={}", superQueenUrl);
                    }
                    List<Integer> myBadgeDataList = Arrays.asList(0, 0, 0);// 依次为热情，魅力，慷慨
                    if (activityId != null) {
                        String jsonValue = userInfoShowRedis.getQueenHashStrValue(activityId, uid);
                        if (StringUtils.hasLength(jsonValue)) {
                            JSONObject jsonObject = JSONObject.parseObject(jsonValue);
                            if (!StringUtils.isEmpty(jsonObject.getString("passionFirstDay"))) {
                                myBadgeDataList.set(0, 1);
                            }
                            if (!StringUtils.isEmpty(jsonObject.getString("charmFirstDay"))) {
                                myBadgeDataList.set(1, 1);
                            }
                            if (!StringUtils.isEmpty(jsonObject.getString("generousFirstDay"))) {
                                myBadgeDataList.set(2, 1);
                            }

                        }
                    }
                    logger.info("activityId:{}, myBadgeDataList:{}", activityId, myBadgeDataList);
//                    List<Integer> myBadgeDataList = userActorData.getFb_gender() == 2 ?
//                            badgeDao.findBadgesByIds(uid, queenBadgeList).stream()
//                                    .map(BadgeData::getBadge_id)
//                                    .collect(Collectors.toList()) : Collections.emptyList();

                    List<BadgeListData> configList = badgeListDao.findBadgesListByIds(queenBadgeList);
                    Map<Integer, BadgeListData> configMap =
                            configList.stream().collect(Collectors.toMap(BadgeListData::getBadge_id, Function.identity()));
                    List<String> badgeList = new ArrayList<>();
                    int index = 0;
                    for (Integer badgeId : queenBadgeList) { //
                        BadgeListData badgeListData = configMap.get(badgeId);
                        if (myBadgeDataList.get(index) == 1) {
                            badgeList.add(badgeListData.getIcon());
                        } else {
                            badgeList.add(badgeListData.getIcon_back());
                        }
                        index++;
                    }
                    userInfoVO.setSuperQueenUrl(superQueenUrl);
                    userInfoVO.setSuperQueenBadgeList(badgeList);
                }
            }
        }

        // TODO 优化只查一次库
        userInfoVO.setCustomerSupportUrl(CUSTOMER_SUPPORT_URL);
        long vipCardCount = actorConfigDao.getLongUserConfig(uid, ActorConfigDao.VIP_CARD_COUNT, 0);
        userInfoVO.setVipCardUnread((int) vipCardCount);
        return userInfoVO;
    }

    /**
     * 获取完成未领取任务数量
     */
    private int getHasTaskReward(HttpEnvData dto) {
        String uid = dto.getUid();
        boolean ver8621 = AppVersionUtils.versionCheck(8621, dto);
        int hasRewardCount = ver8621 ? userTaskRedis.getWebUserHasRewardCount(uid) : userTaskRedis.getUserHasRewardCount(uid);
        if (hasRewardCount > 0) {
            return 1;
        }
        boolean isNewRegister = ActorUtils.isNewRegisterActor(uid, 7);
        hasRewardCount = isNewRegister ? newcomerTaskDao.selectHasRewardCount(uid, ver8621 ? 2 : 1) : 0;
        return hasRewardCount > 0 ? 1 : 0;
    }


    private void fillRoomInfo(MeUserInfoVO userInfoVO) {
        String myRoom = RoomUtils.formatRoomId(userInfoVO.getAid());
        MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(myRoom);
        if (null != mongoRoomData) {
            userInfoVO.setAnnounce(mongoRoomData.getAnnounce());
            userInfoVO.setPrivi(mongoRoomData.getPrivi());
            userInfoVO.setFee(mongoRoomData.getFee());
            userInfoVO.setFeetype(mongoRoomData.getFeetype());
            userInfoVO.setTheme(mongoRoomData.getTheme());
            userInfoVO.setComp(mongoRoomData.getComp());
            userInfoVO.setPwd(!StringUtils.isEmpty(mongoRoomData.getPwd()) ? 1 : 0);
            userInfoVO.setRoomId(myRoom);
            userInfoVO.setRoom_head(ImageUrlGenerator.generateNormalUrl(mongoRoomData.getHead()));
            userInfoVO.setRoom_name(mongoRoomData.getName());
            userInfoVO.setRoom_level(3);
            userInfoVO.setOnline(mongoRoomData.getOnline());
        }
    }

    public int completedProgress(MongoUserActorData userActorData) {
        int process = 0;
        int bannerSize = userActorData.getBannerList().size();
        int labelSize = userActorData.getLabelList().size();
        int bannerCount = bannerSize * 5;
        int labelCount = Math.min(40, labelSize * 4);

        process += bannerCount;
        process += labelCount;
        if (!baseInitData.isSystemUrl(userActorData.getHead())) {
            process += 5;
        }
        if (!StringUtils.isEmpty(userActorData.getDesc())) {
            process += 12;
        }
        if (!StringUtils.isEmpty(userActorData.getName())) {
            process += 1;
        }
        if (!StringUtils.isEmpty(userActorData.getBirthday())) {
            process += 1;
        }
        if (!StringUtils.isEmpty(userActorData.getCountry())) {
            process += 1;
        }
        return process;
    }

    private int getBonusStatus(String uid) {
        return commonConfig.getSwitchConfigValue(CommonConfig.FIRST_CHARGE_SWITCH, 0);
//        return rechargeRedis.hasRechargeOffer();
    }

    public HomePageVisitorsDetailVO listHomepageVisitors(String uid, int atype) {
        HomePageVisitorsDetailVO homePageVisitorsDetailVO = new HomePageVisitorsDetailVO();
        List<VisitorListVO> visitorList = new ArrayList<>();
        if (atype == 1) {
            String dateNum = DateHelper.DEFAULT.formatDateInDay2();
            HomeVisitorList homeVisitorList = hvElasticsearchService.getVisitMeDetails(uid);
            homePageVisitorsDetailVO.setTotalVisitorStr(numToHuman(homeVisitorList.getTotalVisitor()));
            homePageVisitorsDetailVO.setTodayVisitorStr(numToHuman(hvElasticsearchService.getTodayVisitMeCount(uid, dateNum)));
            List<HomeVisitorEs> homeVisitorEsList = homeVisitorList.getEsList();
            for (HomeVisitorEs item : homeVisitorEsList) {
                VisitorListVO visitorListVO = new VisitorListVO();
                String vid = item.getUid();
                ActorData vidData = actorDao.getActorDataFromCache(vid);
                UserBasicInfo userInfo = new UserBasicInfo();
                userInfo.setName(vidData.getName());
                userInfo.setHead(ImageUrlGenerator.generateNormalUrl(vidData.getHead()));
                userInfo.setMicFrame(micFrameRedis.getMicSourceFromCache(vid));
                userInfo.setAid(vid);
                userInfo.setDesc("");
                userInfo.setCountry(vidData.getCountry());
                userInfo.setRid(vidData.getRid());
                userInfo.setRidData(vidData.getRidData());
                visitorListVO.setUser_info(userInfo);
                visitorListVO.setTimes(item.getTimes());
                visitorListVO.setInvisible(item.getInvisible());
                visitorListVO.setMtime(item.getMtime());
                visitorListVO.setCtime(item.getCtime());
                visitorListVO.setDate_num(item.getDate_num());
                visitorListVO.setIs_new(item.getIs_new());
                visitorList.add(visitorListVO);
            }
//            PushNewVisitorData pushNewVisitorData = new PushNewVisitorData();
//            pushNewVisitorData.setItem(CHANGE_VISITOR_STATUS_ITEM);
//            pushNewVisitorData.setUid(uid);
//            RedisTaskService.broadcastMessage(VISITOR_PROFILE_PERSON_NAME, pushNewVisitorData);
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    cleanEsAllPersonNew(uid);
                }
            });
        } else {
            ActorData myData = actorDao.getActorData(uid);
            GeneralConfActorData generalConfActorData = myData.getGeneralConfActorData();
            int isInvisible = generalConfActorData == null ? 0 : generalConfActorData.getInvisible();
            homePageVisitorsDetailVO.setInvisible(isInvisible);
            homePageVisitorsDetailVO.setViplevel(vipInfoDao.getIntVipLevel(uid));
            homePageVisitorsDetailVO.setNeed_level(UserInfoConstant.VISITOR_NEED_VIP_LEVEL);
            List<HomeVisitorEs> homeVisitorEsList = hvElasticsearchService.getMyVisitDetails(uid);
            for (HomeVisitorEs item : homeVisitorEsList) {
                VisitorListVO visitorListVO = new VisitorListVO();
                String aid = item.getAid();
                ActorData vidData = actorDao.getActorDataFromCache(aid);
                UserBasicInfo userInfo = new UserBasicInfo();
                userInfo.setName(vidData.getName());
                userInfo.setMicFrame(micFrameRedis.getMicSourceFromCache(aid));
                userInfo.setHead(ImageUrlGenerator.generateNormalUrl(vidData.getHead()));
                userInfo.setAid(aid);
                userInfo.setDesc("");
                userInfo.setCountry(vidData.getCountry());
                userInfo.setRid(vidData.getRid());
                userInfo.setRidData(vidData.getRidData());
                visitorListVO.setUser_info(userInfo);
                visitorListVO.setTimes(item.getTimes());
                visitorListVO.setInvisible(item.getInvisible());
                visitorListVO.setMtime(item.getMtime());
                visitorListVO.setCtime(item.getCtime());
                visitorListVO.setDate_num(item.getDate_num());
                visitorListVO.setIs_new(0);
                visitorList.add(visitorListVO);
            }
        }
        homePageVisitorsDetailVO.setVisitor_list(visitorList);
        return homePageVisitorsDetailVO;
    }


    public HeartRecordListVO apiHeartDetail(String uid, Integer page) {
        if (StringUtils.isEmpty(uid) || page == null) {
            logger.info("uid is empty, uid={} page={}", uid, page);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        HeartRecordListVO vo = new HeartRecordListVO();
        if (page < 1) {
            page = 1;
        }
        int size = 30;
        List<HeartRecordData> dataList = slaveHeartRecordDao.getDetails(uid, page, size);
        List<HeartRecordListVO.HeartRecordDetailVO> detailList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (HeartRecordData item : dataList) {
                HeartRecordListVO.HeartRecordDetailVO data = new HeartRecordListVO.HeartRecordDetailVO();
                data.setChanged(item.getChanged());
                data.setTitle(item.getTitle());
                data.setDesc(item.getRemark());
                data.setMtime(item.getCtime());
                detailList.add(data);
            }
        }
        if (detailList.size() < size) {
            vo.setNextUrl("");
        } else {
            vo.setNextUrl(String.valueOf(page + 1));
        }
        vo.setList(detailList);
        return vo;

    }


    public void dailyTaskReport(HttpEnvData dto) {
        throw new CommonException(UserHttpCode.INVALID_TASK);
    }

    public PyRoomInfoVO pyRoomInfoNew(HttpEnvData dto) {
        String roomId = dto.getRoomId();
        String uid = dto.getRoomId();
        if (StringUtils.isEmpty(roomId) || StringUtils.isEmpty(uid)) {
            logger.error("pyRoomInfoNew uid or roomId is empty, dto={}", dto);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        PyRoomInfoVO vo = new PyRoomInfoVO();
        MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
        if (roomData == null) {
            logger.error("pyRoomInfoNew roomData is null, dto={}", dto);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        int uType = roomMemberDao.findMemberUtype(roomId, uid);
        vo.setUtype(uType);
        vo.setAnnounce(roomData.getAnnounce());
        vo.setPrivi(roomData.getPrivi());
        vo.setFee(roomData.getFee());
        vo.setFeetype(roomData.getFeetype());
        vo.setComp(roomData.getComp());
        vo.setPwd(StringUtils.isEmpty(roomData.getPwd()) ? 0 : 1);
        vo.setRoomId(roomId);
        vo.setCountry(roomData.getCountry());
        vo.setOnline(roomData.getOnline());
        vo.setRoom_name("");
        vo.setRoom_head(ImageUrlGenerator.generateNormalUrl(roomData.getHead()));
        vo.setRoom_type(roomData.getRoom_type());
        return vo;
    }

    public UserStateInfoVO getUserStateInfo(UserStateInfoDTO dto) {
        String aid = dto.getAid();
        UserStateInfoVO vo = new UserStateInfoVO();
        if (StringUtils.isEmpty(aid)) {
            logger.info("getUserStateInfo aid is empty dto={}", dto);
            return vo;
        }
        String inRoomId = roomPlayerRedis.getActorRoomStatus(aid);
        vo.setInRoomId(StringUtils.isEmpty(inRoomId) ? "" : whiteTestDao.isMemberByType(inRoomId, WhiteTestDao.WHITE_TYPE_ROOM_ID) ? "" : inRoomId);
        return vo;
    }

    public void cleanEsAllPersonNew(String uid) {
        int page = 0;
        int size = 30;
        int resultSize = size;
        int count = 0;
        while (resultSize == size) {
            List<HomeVisitorEs> homeVisitorEsList = hvElasticsearchService.getNewVisitMeDetails(uid, page, size);
            if (!CollectionUtils.isEmpty(homeVisitorEsList)) {
                resultSize = homeVisitorEsList.size();
                for (HomeVisitorEs item : homeVisitorEsList) {
                    hvElasticsearchService.updatePersonNewDataByDocId(item.getId());
                }
            } else {
                resultSize = 0;
            }
            count++;
            if (count >= 100) {
                break;
            }
        }
    }

    public int getConstellation(String birthday) {
        if (StringUtils.isEmpty(birthday)) {
            return 0;
        }
        LocalDate birthDate;
        try {
            birthday = userOptService.handleArbNum(birthday);
            birthDate = DateSupport.parseYyyymd(birthday);
        } catch (DateTimeParseException e) {
            birthDate = DateSupport.parse(birthday);
        }
        if (birthDate == null) {
            return 0;
        }
        Date date = DateSupport.UTC.getDate(birthDate);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int month = cal.get(Calendar.MONTH);//0-11
        int day = cal.get(Calendar.DAY_OF_MONTH);
        // {20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22}
        if (day < CONSTELLATION_EDGE_DAY[month]) {
            month = month - 1;
        }
        if (month >= 0) {
            return CONSTELLATION_ARR[month];
        }
        return CONSTELLATION_ARR[11];
    }

    @Cacheable(value = "getTopFourRecevList", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<GiftRankItemVO> getTopFourRecevList(String aid) {
        List<GiftNum> allGiftNum = giftNumDao.getAllGiftNum(aid);
        if (!CollectionUtils.isEmpty(allGiftNum)) {
            List<GiftRankItemVO> list = new ArrayList<>();
            for (GiftNum data : allGiftNum) {
                GiftData giftData = giftDao.getGiftFromCache(data.getGiftId());
                if (null == giftData) {
                    continue;
                }
                GiftRankItemVO vo = new GiftRankItemVO();
                vo.setGiftNum(data.getGiftNum() + "");
                vo.setGiftIcon(giftData.getGicon());
                if (null != giftData.getPrice()) {
                    vo.setSortNum((long) data.getGiftNum() * giftData.getPrice());
                }
                list.add(vo);
            }
            list.sort(Comparator.comparing(GiftRankItemVO::getSortNum).reversed());
            return list.size() <= 4 ? list : list.subList(0, 4);
        } else {
            return Collections.emptyList();
        }
    }

    public Object clearNewVisitor(UserCardDTO dto) {
        hvElasticsearchService.updateDataByQuery(dto.getUid(), dto.getAid());
        return null;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public UserInfoVO.FamilyInfoVO getFamilyInfo(int familyId) {
        FamilyData familyData = familyDao.selectById(familyId);
        if (null == familyData) {
            return null;
        }
        UserInfoVO.FamilyInfoVO familyInfoVO = new UserInfoVO.FamilyInfoVO();
        familyInfoVO.setFamilyRid(familyData.getRid());
        familyInfoVO.setName(familyData.getName());
        familyInfoVO.setHead(ImageUrlGenerator.generateRoomUserUrl(familyData.getHead()));
        familyInfoVO.setMembers(familyMemberDao.selectMemberCount(familyId));
        return familyInfoVO;
    }


}
