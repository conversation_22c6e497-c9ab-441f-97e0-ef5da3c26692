package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.config.AsyncConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.AccountConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.enums.RoomConstant;
import com.quhong.constant.RoomListConstant;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.*;
import com.quhong.data.dto.FamilyRoomDTO;
import com.quhong.data.dto.PartyListDTO;
import com.quhong.data.dto.RoomDevoteListDTO;
import com.quhong.data.vo.*;
import com.quhong.enums.ClientOS;
import com.quhong.enums.HotSearchHistoryConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.CountData;
import com.quhong.mysql.data.FamilyMemberData;
import com.quhong.mysql.data.HotSearchHistoryData;
import com.quhong.redis.*;
import com.quhong.room.OfficialRoom;
import com.quhong.room.RoomTags;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.userMonitor.UserMonitorRedis;
import com.quhong.utils.*;
import com.quhong.video.redis.VideoRedis;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Service
public class RoomListService {
    private static final Logger logger = LoggerFactory.getLogger(RoomListService.class);
    private static final Integer LIKE_ROOM_NUM = 1000;
    private static final Set<String> HIDE_KEY_SET = new HashSet<>(Arrays.asList("sex", "porn", "dick", "boob"));
    //    private static final List<GameItemInfoVO> HOT_ITEM_MATCH_LIST = Arrays.asList(
//            new GameItemInfoVO(2, "Ludo", 2),
//            new GameItemInfoVO(6, "Carrom", 6),
//            new GameItemInfoVO(3, "UNO", 3)
//    );
    // all-hot页面的快速进入

    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomListRedis roomListRedis;
    @Resource
    private RoomEventSubDao roomEventSubDao;
    @Resource
    private RoomListService roomListService;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private FollowRoomDao followRoomDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private RecentlyRoomRedis recentlyRoomRedis;
    @Resource
    private RoomTags roomTags;
    @Resource
    private UserMonitorRedis userMonitorRedis;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private BadgeDao badgeDao;
    @Resource
    private SlaveUidAidDevoteLogDao slaveUidAidDevoteLogDao;
    @Resource
    private VideoRedis videoRedis;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private RoomHotDevoteRedis roomHotDevoteRedis;
    @Resource
    private PartyListService partyListService;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private HotSearchHistoryDao hotSearchHistoryDao;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Resource
    private FamilyDao familyDao;
    @Resource
    private HomeBannerService homeBannerService;
    @Resource
    private NewRookieRoomRedis newRookieRoomRedis;
    @Resource
    private RoomLevelService roomLevelService;
    @Resource
    private RoomVisitorsRedis roomVisitorsRedis;
    @Resource
    private ActorCommonService actorCommonService;

    public ListVO getPopularList(PartyListDTO req) {
        ActorData actorData = null;
        if (!StringUtils.isEmpty(req.getUid())) {
            actorData = roomListService.getActorDataFromCache(req.getUid());
            if (1 == req.getPage()) {
                basePlayerRedis.incrTokenExpireTime(req.getUid());
            }
        }
        ListVO vo = new ListVO();
        List<PopularListVO> result = null;
        String myCountry = ActorUtils.getCountryCode(null == actorData ? req.getLang() : actorData.getCountry());
        if (RoomListConstant.MAJOR_COUNTRY_SET.contains(myCountry)) {
            if (RoomListConstant.ONE_AREA_SET.contains(myCountry)) {
                PopularRecommendConfigData config = partyListService.getPopularRecommendConfigData();
                if (config.getIsOpenSaWeight() > 0) {
                    int myGccCode = RoomListConstant.GCC_SET.contains(myCountry) ?
                            RoomListConstant.GCC_COUNTRY_AREA : RoomListConstant.UN_GCC_COUNTRY_AREA;
                    result = roomListRedis.getPopularList(myGccCode, req.getPage());
                    if(ServerConfig.isNotProduct()) {
                        logger.info("myCountry:{} myGccCode:{} page:{} result.size:{}", myCountry, myGccCode, req.getPage(), result.size());
                    }
                } else {
                    result = roomListRedis.getPopularList(RoomListConstant.MAJOR_COUNTRY, req.getPage());
                }
            } else {
                result = roomListRedis.getPopularList(RoomListConstant.MAJOR_COUNTRY, req.getPage());
            }
        } else {
            result = roomListRedis.getPopularList(RoomListConstant.OTHER_COUNTRY, req.getPage());
        }
        if (req.getPage() == 1) {
//            List<PopularListVO> rookieRoomList = getRookieRoomList(req.getUid());
//            if (!CollectionUtils.isEmpty(rookieRoomList)) {
//                rookieRoomList.addAll(result);
//                result = rookieRoomList;
//            }

            Map<String, Integer> mapCount = roomListRedis.getGameTypeDataAll();
            vo.setFeaturedList(roomListService.getLikeRoomRankingList());
            vo.setRoomEventList(homeBannerService.getHomeBannerList(req.getUid(), IndexBannerDao.TYPE_ROOM_EVENT, req.getSlang(), req.getVersioncode(), req.getApp_package_name(), req.getOs(), false));

            List<GameItemInfoVO> itemInfoList = new ArrayList<>();
            boolean showNewGame = whiteTestDao.isMemberByType(req.getUid(), WhiteTestDao.WHITE_TYPE_RID);
            List<SociaEntryData> hotItemMatchList = showNewGame ? RoomConstant.HOT_ITEM_MATCH_LIST_V2 : RoomConstant.HOT_ITEM_MATCH_LIST_V1;
            // boolean is839V = AppVersionUtils.versionCheck(839, req)

            for (SociaEntryData entryData : hotItemMatchList) {
                GameItemInfoVO itemInfoVO = new GameItemInfoVO();
                BeanUtils.copyProperties(entryData, itemInfoVO);
                itemInfoVO.setItemType(entryData.getGameType());
                itemInfoVO.setItemName(req.getSlang() == SLangType.ENGLISH ? entryData.getItemNameEn() : entryData.getItemNameAr());
                itemInfoVO.setItemIcon(req.getSlang() == SLangType.ENGLISH ? entryData.getIconEn() : entryData.getIconAr());
                itemInfoVO.setItemOnlineCount(mapCount.getOrDefault(String.valueOf(itemInfoVO.getItemType()), 0));
                itemInfoVO.setOnlineVersion(req.getOs() == ClientOS.IOS ? entryData.getIosVersion() : entryData.getAndroidVersion());
                itemInfoList.add(itemInfoVO);
            }
            vo.setItemInfoList(itemInfoList);
        }
        result = fillVipTagNameList(result, req.getSlang(), req.getOs(), req.getVersioncode());

        fillRoomOnline(result, req.getOs(), req.getVersioncode());
        vo.setList(result);
        vo.setNextUrl(result.size() > 0 ? req.getPage() + 1 + "" : "");
        return vo;
    }

    private void fillRoomOnline(List<? extends RoomListData> list, int os, int versionCode) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (roomVisitorsRedis.showRoomVisitor(os, versionCode)) {
            list.forEach(k -> {
                k.setOnline(k.getVisitorNum());
            });
        }
    }

    @Cacheable(value = "getLikeRoomRankingList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<RoomLikeRankingVO> getLikeRoomRankingList() {
        Map<String, Long> likeRoomRankingMap = roomListRedis.getLikeRankingMap(3);
        List<RoomLikeRankingVO> roomLikeRankingVOList = new ArrayList<>();
        int rank = 1;
        for (Map.Entry<String, Long> entry : likeRoomRankingMap.entrySet()) {
            String roomId = entry.getKey();
            long likeNum = entry.getValue();
            if (rank == 1 && likeNum < LIKE_ROOM_NUM) {
                break;
            }
            RoomLikeRankingVO vo = new RoomLikeRankingVO();
            MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(roomId);
            vo.setRoomId(roomId);
            vo.setRank(rank);
            vo.setRoomHead(ImageUrlGenerator.generateRoomUserUrl(mongoRoomData.getHead()));
            vo.setRoomName(mongoRoomData.getName());
            vo.setPwd(StringUtils.isEmpty(mongoRoomData.getPwd()) ? 0 : 1);
            vo.setLikesNum(likeNum);
            vo.setStrLikesNum(MatchUtils.formatDevotes(likeNum, RoundingMode.FLOOR));
            roomLikeRankingVOList.add(vo);
            rank += 1;
        }
        return roomLikeRankingVOList;
    }

    private List<PopularListVO> fillVipTagNameList(List<PopularListVO> popularList, int slang, int os, int versioncode) {
//        boolean isOpen = roomListRedis.isPopularVipSwOpen();
        for (PopularListVO item : popularList) {
//            if (!isOpen) {
//                item.setViplevel(0);
//            }
            int tag = item.getTag();
            item.setTagName(roomTags.getTagNameById(tag, slang));
            item.setVipMedal(actorCommonService.getCommonVipMedal(item.getAid(), item.getViplevel()));
            if (item.getRecreationTag() == RecreationTagService.ROOM_EVENT_ACTIVITY_TAG) {
                if (!AppVersionUtils.versionCheck(857, versioncode, os)) {
                    item.setRecreationTag(RecreationTagService.COMMON_TAG);
                } else {
                    item.setRecreationValue(slang == SLangType.ARABIC ? RecreationTagService.ROOM_EVENT_ACTIVITY_NAME_AR : RecreationTagService.ROOM_EVENT_ACTIVITY_NAME_EN);
                }
            }
        }
        if (os == ClientOS.IOS) {
            List<PopularListVO> newList = new ArrayList<>();
            popularList.forEach(vo -> {
                if (!RoomListConstant.APPLE_HIDE_SET.contains(ActorUtils.getCountryCode(vo.getCountry()))) {
                    newList.add(vo);
                }
            });
            return newList;
        }
        return popularList;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public ActorData getActorDataFromCache(String uid) {
        return actorDao.getActorDataFromCache(uid);
    }

    public ListVO getForYouList(PartyListDTO req) {
        // 7天内注册的新用户或者迎新用户可见迎新房
        return roomListService.doGetForYouList(req.getSlang(), req.getPage(), req.getOs(), req.getVersioncode());
    }

    @Cacheable(value = "forYouList", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')",
            cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public ListVO doGetForYouList(int slang, int page, int os, int versionCode) {
        ListVO vo = new ListVO();
        List<ForYouListVO> forYouList = roomListRedis.getForYouList();
        forYouList.removeIf(item -> RoomListConstant.APPLE_HIDE_SET.contains(ActorUtils.getCountryCode(item.getCountry())));
        PageUtils.PageData<ForYouListVO> pageData = PageUtils.getPageData(forYouList, page, RoomListConstant.FOR_YOU_PAGE_SIZE);
        vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");
        vo.setList(pageData.list);
        fillRoomHeadList(pageData.list, slang);
        fillRoomOnline(pageData.list, os, versionCode);
        return vo;
    }

    public ListVO getActiveRoomList(PartyListDTO req) {
        // 847 discover chat list
        return roomListService.doGetActiveRoomList(req.getUid(), req.getSlang(), req.getPage(), ActorUtils.isNewRegisterActor(req.getUid(), 7), req.getOs(), req.getVersioncode());
    }

    @Cacheable(value = "activeRoomList", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')",
            cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public ListVO doGetActiveRoomList(String uid, int slang, int page, boolean rookie, int os, int versionCode) {
        ListVO vo = new ListVO();
        List<ForYouListVO> forYouList = roomListRedis.getActiveRoomList(rookie);
//        forYouList.removeIf(item -> RoomListConstant.APPLE_HIDE_SET.contains(ActorUtils.getCountryCode(item.getCountry())));
        PageUtils.PageData<ForYouListVO> pageData = PageUtils.getPageData(forYouList, page, RoomListConstant.FOR_YOU_PAGE_SIZE);
        vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");
        List<ForYouListVO> list = pageData.list;
        // 被列入房间黑名单的房间和被房间踢出的房间不推荐
        list.removeIf(item -> roomKickRedis.isKick(item.getRoomId(), uid) || roomBlacklistDao.isBlock(item.getRoomId(), uid));
        vo.setList(list);
        fillRoomHeadList(list, slang);
        fillRoomOnline(list, os, versionCode);
        return vo;
    }

    public CountryVO getCountryList(PartyListDTO req) {
        String countryCode;
        if (StringUtils.isEmpty(req.getCountryCode())) {
            ActorData actorData = null;
            if (!StringUtils.isEmpty(req.getUid())) {
                actorData = roomListService.getActorDataFromCache(req.getUid());
            }
            countryCode = ActorUtils.getCountryCode(null == actorData || StringUtils.isEmpty(actorData.getCountry()) ? req.getLang() : actorData.getCountry());
        } else {
            countryCode = req.getCountryCode();
        }
        if (req.getOs() == ClientOS.IOS && RoomListConstant.APPLE_HIDE_SET.contains(countryCode)) {
            CountryVO vo = new CountryVO();
            vo.setList(Collections.emptyList());
            vo.setNextUrl("");
            vo.setCountryCode(countryCode);
            return vo;
        }
        return roomListService.doGetCountryList(countryCode, 0 == req.getPage() ? 1 : req.getPage(), req.getSlang(), req.getOs(), req.getVersioncode());
    }

    @Cacheable(value = "countryList", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public CountryVO doGetCountryList(String countryCode, int page, int slang, int os, int versionCode) {
        CountryVO vo = new CountryVO();
        List<CountryListVO> countryList = roomListRedis.getCountryList(countryCode);
        PageUtils.PageData<CountryListVO> pageData = PageUtils.getPageData(countryList, page, RoomListConstant.COUNTRY_PAGE_SIZE);
        vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");
        vo.setList(pageData.list);
        vo.setCountryCode(countryCode);
        fillRoomHeadList(pageData.list, slang);
        fillRoomOnline(pageData.list, os, versionCode);
        return vo;
    }

    public void fillRoomHeadList(List<? extends ForYouListVO> forYouList, int slang) {
        for (ForYouListVO forYouListVO : forYouList) {
            List<String> roomActors = roomPlayerRedis.getEarliestActors(forYouListVO.getRoomId(), 30);
            forYouListVO.setTagName(roomTags.getTagNameById(forYouListVO.getTag(), slang));
            forYouListVO.setMicPersonList(videoRedis.getMicPersonList(forYouListVO.getRoomId(), roomActors, 5));
//            String hostUid = RoomUtils.getRoomHostId(forYouListVO.getRoomId());
//            forYouListVO.setRoomBadgesList(badgeDao.getBadgeList(hostUid));
        }
    }

    @Cacheable(value = "joinList", key = "#p0.uid.concat('-').concat(#p0.page)", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public ListVO joinList(PartyListDTO req) {
        ListVO vo = new ListVO();
        int start = (req.getPage() - 1) * RoomListConstant.JOIN_PAGE_SIZE;
//        List<RoomMemberData> roomMemberList = roomMemberDao.selectPage(req.getUid(), start, RoomListConstant.JOIN_PAGE_SIZE);
        List<RoomMemberDao.RoomMemberLookUpData> roomMemberList = roomMemberDao.selectLookUpPage(req.getUid(), start, RoomListConstant.JOIN_PAGE_SIZE);
        List<JoinListVO> joinListVOList = new ArrayList<>();
        for (RoomMemberDao.RoomMemberLookUpData memberData : roomMemberList) {
            if (whiteTestDao.isMemberByType(memberData.getRoom_id(), WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                continue;
            }
            PartyListData partyData = partyListService.getPartyData(memberData.getRoom_id());
            JoinListVO joinListVO = new JoinListVO();
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(memberData.getRoom_id());
            joinListVO.copyFromMongoRoomData(roomData);
            if (roomVisitorsRedis.showRoomVisitor(req)) {
                joinListVO.setOnline(roomVisitorsRedis.getRoomVisitorNum(memberData.getRoom_id()));
            } else {
                joinListVO.setOnline(memberData.getOnline());
            }
            joinListVO.setTagName(roomTags.getTagNameById(joinListVO.getTag(), req.getSlang()));
            joinListVO.setRecreationTag(null == partyData ? 0 : partyData.getRecreationTag());
            joinListVO.setIsOfficial(OfficialRoom.ifOfficialRoom(roomData.getRid()) ? 1 : 0);
//            String hostUid = RoomUtils.getRoomHostId(memberData.getRoom_id());
//            joinListVO.setRoomBadgesList(badgeDao.getBadgeList(hostUid));
            joinListVOList.add(joinListVO);
        }
        vo.setList(joinListVOList);
        vo.setNextUrl(joinListVOList.size() > 0 ? req.getPage() + 1 + "" : "");
        return vo;
    }

    @Cacheable(value = "followList", key = "#p0.uid.concat('-').concat(#p0.page)", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public ListVO followList(PartyListDTO req) {
        ListVO vo = new ListVO();
        int start = (req.getPage() - 1) * RoomListConstant.JOIN_PAGE_SIZE;
//        List<FollowRoomData> followRoomList = followRoomDao.selectPage(req.getUid(), start, RoomListConstant.FOLLOW_PAGE_SIZE);
        List<FollowRoomDao.FollowRoomLookUpData> followRoomList = followRoomDao.selectLookUpPage(req.getUid(), start, RoomListConstant.FOLLOW_PAGE_SIZE);
        List<JoinListVO> joinListVOList = new ArrayList<>();
        for (FollowRoomDao.FollowRoomLookUpData memberData : followRoomList) {
            if (whiteTestDao.isMemberByType(memberData.getRoom_id(), WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                continue;
            }
            PartyListData partyData = partyListService.getPartyData(memberData.getRoom_id());
            JoinListVO joinListVO = new JoinListVO();
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(memberData.getRoom_id());
            if (roomVisitorsRedis.showRoomVisitor(req)) {
                joinListVO.setOnline(roomVisitorsRedis.getRoomVisitorNum(memberData.getRoom_id()));
            } else {
                joinListVO.setOnline(memberData.getOnline());
            }
            joinListVO.copyFromMongoRoomData(roomData);
            joinListVO.setTagName(roomTags.getTagNameById(joinListVO.getTag(), req.getSlang()));
            joinListVO.setRecreationTag(null == partyData ? 0 : partyData.getRecreationTag());
            joinListVO.setIsOfficial(OfficialRoom.ifOfficialRoom(roomData.getRid()) ? 1 : 0);
//            String hostUid = RoomUtils.getRoomHostId(memberData.getRoom_id());
//            joinListVO.setRoomBadgesList(badgeDao.getBadgeList(hostUid));
            joinListVOList.add(joinListVO);
        }
        vo.setList(joinListVOList);
        vo.setNextUrl(joinListVOList.size() > 0 ? req.getPage() + 1 + "" : "");
        return vo;
    }

    @Cacheable(value = "recentlyList", key = "#p0.uid.concat('-').concat(#p0.page)", cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
    public ListVO recentlyList(PartyListDTO req) {
        ListVO vo = new ListVO();
        int start = (req.getPage() - 1) * RoomListConstant.RECENTLY_PAGE_SIZE;
        int end = start + RoomListConstant.RECENTLY_PAGE_SIZE - 1;
        Set<String> recentlyRooms = recentlyRoomRedis.getRecentlyNumRooms(req.getUid(), start, end);
        List<RecentlyListVO> recentlyListVOList = new ArrayList<>();
        for (String roomId : recentlyRooms) {
            if (RoomUtils.getRoomHostId(roomId).equals(req.getUid())) {
                continue;
            }
            if (userMonitorRedis.isFreezeBan(RoomUtils.getRoomHostId(roomId))) {
                continue;
            }
            if (whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                continue;
            }
            PartyListData partyData = partyListService.getPartyData(roomId);
            RecentlyListVO recentlyListVO = new RecentlyListVO();
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
            if (roomVisitorsRedis.showRoomVisitor(req)) {
                recentlyListVO.setOnline(null == partyData ? roomVisitorsRedis.getRoomVisitorNum(roomId) : partyData.getVisitorNum());
            } else {
                recentlyListVO.setOnline(null == partyData ? roomPlayerRedis.getRoomActorsCount(roomId) : partyData.getRealOnline());
            }
            recentlyListVO.copyFromMongoRoomData(roomData);
            recentlyListVO.setTagName(roomTags.getTagNameById(recentlyListVO.getTag(), req.getSlang()));
            recentlyListVO.setRecreationTag(null == partyData ? 0 : partyData.getRecreationTag());
            recentlyListVO.setIsOfficial(OfficialRoom.ifOfficialRoom(roomData.getRid()) ? 1 : 0);
//            String hostUid = RoomUtils.getRoomHostId(roomId);
//            recentlyListVO.setRoomBadgesList(badgeDao.getBadgeList(hostUid));
            recentlyListVOList.add(recentlyListVO);
        }
        if (req.getPage() == 1) {
            String myRoomId = RoomUtils.formatRoomId(req.getUid());
            RecentlyListVO recentlyListVO = new RecentlyListVO();
            MongoRoomData roomData = mongoRoomDao.findData(myRoomId);
            if (roomData != null) {
                PartyListData partyData = partyListService.getPartyData(myRoomId);
                if (roomVisitorsRedis.showRoomVisitor(req)) {
                    recentlyListVO.setOnline(null == partyData ? roomVisitorsRedis.getRoomVisitorNum(myRoomId) : partyData.getVisitorNum());
                } else {
                    int online = null == partyData ? roomPlayerRedis.getRoomActorsCount(myRoomId) : partyData.getRealOnline();
                    if (online == 1) {
                        Set<String> allActor = roomPlayerRedis.getRoomActors(myRoomId);
                        online = allActor == null ? 0 : allActor.size();
                    }
                    recentlyListVO.setOnline(online);
                }
                recentlyListVO.copyFromMongoRoomData(roomData);
                recentlyListVO.setTagName(roomTags.getTagNameById(recentlyListVO.getTag(), req.getSlang()));
                recentlyListVO.setRecreationTag(null == partyData ? 0 : partyData.getRecreationTag());
                recentlyListVO.setRoomLevel(roomLevelService.getRoomLevel(myRoomId));
            }
            vo.setMyRoom(recentlyListVO);
        }
        vo.setList(recentlyListVOList);
        vo.setNextUrl(recentlyListVOList.size() > 0 ? req.getPage() + 1 + "" : "");
        return vo;
    }

    @Cacheable(value = "gameList", key = "T(String).valueOf(#p0.gameType).concat('-').concat(#p0.page).concat('-').concat(#p0.os).concat('-').concat(#p0.versioncode)",
            cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public ListVO gameList(PartyListDTO req) {
        ListVO vo = new ListVO();
        if (0 == req.getGameType()) {
            // 兼容部分老版本，返回转盘游戏
            req.setGameType(1);
        }
        List<GameListVO> gameList = roomListRedis.getGameList(req.getGameType());
        if (!AppVersionUtils.versionCheck(853, req)) {
            gameList = gameList.stream().filter(k -> k.getGame_type() != 5).collect(Collectors.toList());
        }
        PageUtils.PageData<GameListVO> pageData = PageUtils.getPageData(gameList, req.getPage(), RoomListConstant.GAME_PAGE_SIZE);
        vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");
        vo.setList(pageData.list);
        fillRoomOnline(pageData.list, req.getOs(), req.getVersioncode());
        return vo;
    }

    @Cacheable(value = "allGameList", key = "T(String).valueOf(#p0.page).concat('-').concat(#p0.os).concat('-').concat(#p0.versioncode)", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public AllGameListVO allGameList(PartyListDTO req) {
        AllGameListVO vo = new AllGameListVO();
        List<GameListVO> gameList = roomListRedis.getAllGameList();
        if (!AppVersionUtils.versionCheck(853, req)) {
            gameList = gameList.stream().filter(k -> k.getGame_type() != 5).collect(Collectors.toList());
        }
        PageUtils.PageData<GameListVO> pageData = PageUtils.getPageData(gameList, req.getPage(), RoomListConstant.GAME_PAGE_SIZE);
        vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");
        vo.setList(pageData.list);
        fillRoomOnline(pageData.list, req.getOs(), req.getVersioncode());
        fillGameSwitchInfo(vo);
        return vo;
    }

    private void fillGameSwitchInfo(AllGameListVO vo) {
        JSONObject switchConfig = commonConfig.getSwitchConfig();
        vo.setLudoSwitch(switchConfig.getIntValue("ludo"));
        vo.setUmoSwitch(switchConfig.getIntValue("umo_switch"));
        vo.setMonsterCrushSwitch(switchConfig.getIntValue("monster_crush_switch"));
        vo.setDominoSwitch(switchConfig.getIntValue("domino_switch"));
    }

    @Cacheable(value = "eventList", key = "T(String).valueOf(#p0.page)+'-'+ T(String).valueOf(#p0.slang)", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public RoomEventListVO eventList(PartyListDTO req) {
        RoomEventListVO vo = new RoomEventListVO();
        int slang = req.getSlang();
        vo.setPartyingList(getRoomEventVOListBySlang(roomListRedis.getEventList(RoomListConstant.EVENT_PARTYING), slang));
        List<RoomEventVO> eventList = getRoomEventVOListBySlang(roomListRedis.getEventList(RoomListConstant.EVENT_COMING_SOON), slang);
        PageUtils.PageData<RoomEventVO> pageData = PageUtils.getPageData(eventList, req.getPage(), RoomListConstant.EVENT_PAGE_SIZE);
        vo.setList(pageData.list);
        vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");
        return vo;
    }

    public RoomEventListVO familyEventList(PartyListDTO req) {
        RoomEventListVO vo = new RoomEventListVO();
        int slang = req.getSlang();
        List<RoomEventVO> eventList = getRoomEventVOListBySlang(roomListRedis.getFamilyEventList(req.getFamilyId()), slang);
        List<Integer> allMineSubList = roomEventSubDao.getAllMineSubList(req.getUid(), DateHelper.getNowSeconds());
        for (RoomEventVO roomEventVO : eventList) {
            roomEventVO.setSubscribed(allMineSubList.contains(roomEventVO.getEventId()) ? 1 : 0);
        }
        vo.setList(eventList);
        vo.setNextUrl("");
        return vo;
    }

    private List<RoomEventVO> getRoomEventVOListBySlang(List<RoomEventVO> srcList, int slang) {
        boolean isArb = slang == SLangType.ARABIC;
        srcList.forEach(item -> {
            if (isArb) {
                item.setTypeName(item.getTypeNameAr());
            }
        });
        return srcList;
    }

    public RoomDevoteVO roomDevoteList(RoomDevoteListDTO req) {
        if (!roomPlayerRedis.checkInRoom(req.getRoomId(), req.getUid())) {
            logger.info("actor do not in room.roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(HttpCode.NOT_IN_ROOM);
        }
        RoomDevoteVO vo = new RoomDevoteVO();
        String myUid = req.getUid();
        String roomId = req.getRoomId();
        int mode = req.getMode();
        ActorData myActorData = roomListService.getActorDataFromCache(myUid);
        vo.setMyUid(myUid);
        vo.setMyName(myActorData.getName());
        int vipLevel = vipInfoDao.getIntVipLevel(myUid);
        vo.setMyVipLevel(vipLevel);
        vo.setMyVipMedal(actorCommonService.getCommonVipMedal(myUid, vipLevel));
        vo.setMyHead(vipInfoDao.generateVipUrlByLevel(vipLevel, myActorData.getHead(), ImageUrlGenerator.MODE_150));
        vo.setMyMicFrame(micFrameRedis.getMicSourceFromCache(myUid));
        vo.setMyGender(myActorData.getFb_gender() == 2 ? 2 : 1);
        vo.setUlvl(userLevelDao.getUserLevel(myUid));
        vo.setMyAge(myActorData.getAge());
        vo.setMyBadge(badgeDao.getBadgeList(myUid));
        vo.setMyTotal(MatchUtils.formatDevotes(roomListService.getRedisMyRoomDevoteByMode(myUid, roomId, mode)));
        List<UserInfoItemVO> userInfoList = roomListService.getRedisUserInfoItemVOList(roomId, mode);
        vo.setList(userInfoList);
        vo.setMyRank(getMyRank(myUid, userInfoList));
        vo.setTotal(MatchUtils.formatDevotes(roomListService.getTotalRoomDevoteByMode(roomId, mode)));
        return vo;
    }

    public SearchVO search(PartyListDTO req) {
        SearchVO vo = new SearchVO();
        if (ObjectUtils.isEmpty(req.getKey())) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        String key = req.getKey().trim();
        if (key.length() >= 30 || HIDE_KEY_SET.contains(key.toLowerCase())) {
            return vo;
        }
        String reqUid = req.getUid();
        boolean isTestUser = false;
        if (!ObjectUtils.isEmpty(reqUid)) {
            isTestUser = whiteTestDao.isMemberByType(reqUid, WhiteTestDao.WHITE_TYPE_RID);
        }
        int page = Math.max(req.getPage(), 1);
        List<ActorData> actorDataList = actorDao.searchActorByRid(req.getKey(), page, RoomListConstant.SEARCH_PAGE_SIZE);
        String aid = "";
        for (ActorData actorData : actorDataList) {
            if (null != actorData && 1 == actorData.getValid() && AccountConstant.DELETED != actorData.getAccountStatus()) {
                aid = actorData.getUid();
                String sRoomId = RoomUtils.formatRoomId(actorData.getUid());
                if (!isTestUser && (whiteTestDao.isMemberByType(actorData.getUid(), WhiteTestDao.WHITE_TYPE_RID)
                        || whiteTestDao.isMemberByType(sRoomId, WhiteTestDao.WHITE_TYPE_ROOM_ID))) {
                    // 非测试用户不能搜索测试用户,房间
                    logger.info("normal user can not search test user reqUid:{} Key:{} sUid:{}", reqUid, req.getKey(), actorData.getUid());
                    continue;
                }
                vo.getUsers().add(copyActorVO(actorData));
                SearchVO.RoomVO itemRoomVO = getRoomVO(sRoomId, req.getSlang(), actorData, req);
                if (itemRoomVO != null) {
                    vo.getRooms().add(itemRoomVO);
                }
            }
        }
        vo.setNextUrl(actorDataList.size() >= RoomListConstant.SEARCH_PAGE_SIZE ? String.valueOf(page + 1) : "");
//        asyncWriteHistoryTask(reqUid, aid);
        return vo;
    }

    public void asyncWriteHistoryTask(String uid, String aid) {
        if (!StringUtils.isEmpty(aid)) {
            executor.execute(() -> {
                HotSearchHistoryData data = new HotSearchHistoryData();
                data.setUid(uid);
                data.setSearchKey(RoomUtils.formatRoomId(aid));
                data.setSearchType(HotSearchHistoryConstant.ROOM_TYPE);
                data.setSearchNum(1);
                data.setMtime(DateHelper.getNowSeconds());
                int row = hotSearchHistoryDao.incNum(data);
                if (row <= 0) {
                    hotSearchHistoryDao.insertOrUpdate(data);
                }
                data.setSearchKey(aid);
                data.setSearchType(HotSearchHistoryConstant.USER_TYPE);
                int row2 = hotSearchHistoryDao.incNum(data);
                if (row2 <= 0) {
                    hotSearchHistoryDao.insertOrUpdate(data);
                }
                logger.info("row:{} insert or update HotSearchHistoryData success:{}", row, data);
            });
        }
    }


    public HotSearchHistoryVO getHotSearchList(HttpEnvData req) {
        int start = DateHelper.getNowSeconds() - 30 * 86400;
        int size = 10;
        int count = 0;
        List<HotSearchListData> hotSearchRoomList = hotSearchHistoryDao.getHotSearchListData
                (HotSearchHistoryConstant.ROOM_TYPE, start);
        List<HotSearchListData> hotSearchUserList = hotSearchHistoryDao.getHotSearchListData
                (HotSearchHistoryConstant.USER_TYPE, start);
//        logger.info("hotSearchRoomList:{}  hotSearchUserList:{}", hotSearchRoomList, hotSearchUserList);

        HotSearchHistoryVO vo = new HotSearchHistoryVO();
        List<RecentlyListVO> hotRoomList = new ArrayList<>();
        List<UserInfoItemVO> hotUserList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(hotSearchRoomList)) {
            for (HotSearchListData item : hotSearchRoomList) {
                String roomId = item.getSearchKey();
                ActorData aidData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomId));
                if (null == aidData || aidData.getValid() == 0) {
                    continue;
                }
                PartyListData partyData = partyListService.getPartyData(roomId);
                RecentlyListVO recentlyListVO = new RecentlyListVO();
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
                if (roomVisitorsRedis.showRoomVisitor(req)) {
                    recentlyListVO.setOnline(null == partyData ? roomVisitorsRedis.getRoomVisitorNum(roomId) : partyData.getVisitorNum());
                } else {
                    recentlyListVO.setOnline(null == partyData ? roomPlayerRedis.getRoomActorsCount(roomId) : partyData.getRealOnline());
                }
                recentlyListVO.copyFromMongoRoomData(roomData);
                recentlyListVO.setTagName(roomTags.getTagNameById(recentlyListVO.getTag(), req.getSlang()));
                recentlyListVO.setRecreationTag(null == partyData ? 0 : partyData.getRecreationTag());
                recentlyListVO.setIsOfficial(OfficialRoom.ifOfficialRoom(roomData.getRid()) ? 1 : 0);
                hotRoomList.add(recentlyListVO);
                count++;
                if (count == size) {
                    break;
                }
            }
        }

        count = 0;
        if (!CollectionUtils.isEmpty(hotSearchUserList)) {
            for (HotSearchListData item : hotSearchUserList) {
                String aid = item.getSearchKey();
                ActorData aidData = actorDao.getActorDataFromCache(aid);
                if (null == aidData || aidData.getValid() == 0) {
                    continue;
                }
                UserInfoItemVO userInfoItemVO = new UserInfoItemVO();
                userInfoItemVO.setAid(aid);
                userInfoItemVO.setName(aidData.getName());
                userInfoItemVO.setAge(aidData.getAge());
                userInfoItemVO.setGender(aidData.getFb_gender() == 2 ? 2 : 1);
                userInfoItemVO.setUlvl(userLevelDao.getUserLevel(aid));
                int vipLevel = vipInfoDao.getIntVipLevel(aid);
                userInfoItemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(aidData.getHead(), vipLevel));
                userInfoItemVO.setMicFrame(micFrameRedis.getMicSourceFromCache(aid));
                userInfoItemVO.setViplevel(vipLevel);
                userInfoItemVO.setVipMedal(actorCommonService.getCommonVipMedal(aid, vipLevel));
                userInfoItemVO.setBadge(badgeDao.getBadgeList(aid));
                hotUserList.add(userInfoItemVO);
                count++;
                if (count == size) {
                    break;
                }
            }
        }
        vo.setHotRoomList(hotRoomList);
        vo.setHotUserList(hotUserList);
        return vo;
    }

    private SearchVO.ActorVO copyActorVO(ActorData actorData) {
        SearchVO.ActorVO actorVO = new SearchVO.ActorVO();
        actorVO.setRidInfo(actorData.getRidData());
        actorVO.setAid(actorData.getUid());
        actorVO.setName(actorData.getName());
        actorVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), actorVO.getViplevel()));
        actorVO.setMicFrame(micFrameRedis.getMicSourceFromCache(actorData.getUid()));
        actorVO.setGender(actorData.getFb_gender());
        actorVO.setAge(actorData.getAge());
        actorVO.setRid(actorData.getOriginalRid());
        actorVO.setUlvl(userLevelDao.getUserLevel(actorData.getUid()));
        actorVO.setViplevel(vipInfoDao.getIntVipLevelFromCache(actorData.getUid()));
        actorVO.setVipMedal(actorCommonService.getCommonVipMedal(actorData.getUid(), actorVO.getViplevel()));
        return actorVO;
    }

    private SearchVO.RoomVO getRoomVO(String roomId, int slang, ActorData actorData, HttpEnvData req) {
        MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(roomId);
        if (null == mongoRoomData) {
            return null;
        }
        SearchVO.RoomVO roomVO = new SearchVO.RoomVO();
        PartyListData partyData = partyListService.getPartyData(roomId);
        roomVO.copyFromMongoRoomData(mongoRoomData);
        if (roomVisitorsRedis.showRoomVisitor(req)) {
            roomVO.setOnline(null == partyData ? roomVisitorsRedis.getRoomVisitorNum(roomId) : partyData.getVisitorNum());
        } else {
            roomVO.setOnline(null == partyData ? 0 : partyData.getRealOnline());
        }
        roomVO.setTagName(roomTags.getTagNameById(roomVO.getTag(), slang));
        roomVO.setRidInfo(actorData.getRidData());
        roomVO.setRecreationTag(null == partyData ? 0 : partyData.getRecreationTag());
        roomVO.setIsOfficial(null == partyData ? 0 : partyData.getIsOfficial());
        return roomVO;
    }

    private String getMyRank(String uid, List<UserInfoItemVO> userInfoList) {
        try {
            List<String> ids = userInfoList.stream().map(UserInfoItemVO::getAid).collect(Collectors.toList());
            int rank = ids.indexOf(uid);
            return rank != -1 ? String.valueOf(rank + 1) : userInfoList.size() > 10 ? "10+" : "";
        } catch (Exception e) {
            logger.error("getMyRank error. {}", e.getMessage(), e);
            return "10+";
        }
    }


    private int getStartTime(int mode) {
        int now = DateHelper.getNowSeconds();
        int d = mode == RoomListConstant.DAY_MODE ? 24 * 3600 : 7 * 24 * 3600;
        return now - d;
    }

    public PageVO<PopularListVO> getFamilyRoomList(FamilyRoomDTO req) {
        if (req.getFamilyId() == null || req.getFamilyId() == 0) {
            logger.error("familyId:{} is empty", req.getFamilyId());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        PopularListVO familyOwner = null;
        PageVO<PopularListVO> vo = new PageVO<>();
        List<FamilyMemberData> allMembers = familyMemberDao.selectMemberList(req.getFamilyId());
        List<PopularListVO> result = new ArrayList<>();
        for (FamilyMemberData familyMemberData : allMembers) {
            String roomId = RoomUtils.formatRoomId(familyMemberData.getUid());
            PartyListData partyData = partyListService.getPartyData(roomId);
            if (partyData != null) {
                PopularListVO item = new PopularListVO();
                BeanUtils.copyProperties(partyData, item);
                item.setTagName(roomTags.getTagNameById(item.getTag(), req.getSlang()));
                if (familyMemberData.getRole() == 1) {
                    familyOwner = item;
                } else {
                    result.add(item);
                }
            }
        }
        if (null != familyOwner) {
            result.add(0, familyOwner);
        }
        result.sort(Comparator.comparing(PopularListVO::getWeight).reversed());
        vo.setList(result);
        return vo;
    }

    @Cacheable(value = "getUserInfoItemVOList", key = "#p0 + #p1", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<UserInfoItemVO> getUserInfoItemVOList(String roomId, int mode) {
        List<UserInfoItemVO> userList = new ArrayList<>();
        int start = getStartTime(mode);
        List<CountData> dataList = slaveUidAidDevoteLogDao.getUserRankByRoom(roomId, start, 10);
        for (CountData item : dataList) {
            String aid = item.getMyKey();
            long beans = item.getCount();
            ActorData aidData = roomListService.getActorDataFromCache(aid);
            UserInfoItemVO userInfoItemVO = new UserInfoItemVO();
            userInfoItemVO.setAid(aid);
            userInfoItemVO.setName(aidData.getName());
            int vipLevel = vipInfoDao.getIntVipLevel(aid);
            userInfoItemVO.setViplevel(vipLevel);
            userInfoItemVO.setHead(vipInfoDao.generateVipUrlByLevel(vipLevel, aidData.getHead(), ImageUrlGenerator.MODE_150));
            userInfoItemVO.setMicFrame(micFrameRedis.getMicSourceFromCache(aid));
            userInfoItemVO.setAge(aidData.getAge());
            userInfoItemVO.setGender(aidData.getFb_gender() == 2 ? 2 : 1);
            userInfoItemVO.setUlvl(userLevelDao.getUserLevel(aid));
            userInfoItemVO.setBadge(badgeDao.getBadgeList(aid));
            userInfoItemVO.setTotal(MatchUtils.formatDevotes(beans));
            userList.add(userInfoItemVO);
        }
        return userList;
    }

    @Cacheable(value = "getRedisUserInfoItemVOList", key = "#p0 + #p1", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public List<UserInfoItemVO> getRedisUserInfoItemVOList(String roomId, int mode) {
        int rankMode = mode == 1 ? RoomHotDevoteRedis.ROOM_USER_RANK_DAY : RoomHotDevoteRedis.ROOM_USER_RANK_WEEK;
        Map<String, Long> linkedRankMap = roomHotDevoteRedis.getUserInRoomMap(roomId, rankMode, 10);
        if (!CollectionUtils.isEmpty(linkedRankMap)) {
            List<UserInfoItemVO> userList = new ArrayList<>();
            for (Map.Entry<String, Long> entry : linkedRankMap.entrySet()) {
                String aid = entry.getKey();
                long beans = entry.getValue();
                ActorData aidData = roomListService.getActorDataFromCache(aid);
                UserInfoItemVO userInfoItemVO = new UserInfoItemVO();
                userInfoItemVO.setAid(aid);
                int vipLevel = vipInfoDao.getIntVipLevel(aid);
                userInfoItemVO.setViplevel(vipLevel);
                userInfoItemVO.setVipMedal(actorCommonService.getCommonVipMedal(aid, vipLevel));
                userInfoItemVO.setName(aidData.getName());
                userInfoItemVO.setHead(vipInfoDao.generateVipUrlByLevel(vipLevel, aidData.getHead(), ImageUrlGenerator.MODE_150));
                userInfoItemVO.setMicFrame(micFrameRedis.getMicSourceFromCache(aid));
                userInfoItemVO.setAge(aidData.getAge());
                userInfoItemVO.setGender(aidData.getFb_gender() == 2 ? 2 : 1);
                userInfoItemVO.setUlvl(userLevelDao.getUserLevel(aid));
                userInfoItemVO.setBadge(badgeDao.getBadgeList(aid));
                userInfoItemVO.setTotal(MatchUtils.formatDevotes(beans));
                userList.add(userInfoItemVO);
            }
            logger.info("read roomHotDevoteRedis success roomId={} mode={}", roomId, mode);
            return userList;
        }
        return getUserInfoItemVOList(roomId, mode);
    }


    public long getRedisMyRoomDevoteByMode(String uid, String roomId, int mode) {
        int rankMode = mode == 1 ? RoomHotDevoteRedis.ROOM_USER_RANK_DAY : RoomHotDevoteRedis.ROOM_USER_RANK_WEEK;
        long devote = roomHotDevoteRedis.getUserInRoomScore(roomId, uid, rankMode);
        return devote > 0 ? devote : getMyRoomDevoteByMode(uid, roomId, mode);
    }

    public List<PopularListVO> getRookieRoomList(String uid) {
        if (!newRookieRoomRedis.canSeeRookieRoom(uid)) {
            return null;
        }
        Set<String> rookieRoomIdSet = newRookieRoomRedis.getAllNewRookieRoom();
        List<PopularListVO> popularList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(rookieRoomIdSet)) {
            rookieRoomIdSet.forEach(k ->
                    {
                        PartyListData data = partyListService.getPartyData(k);
                        if (data != null) {
                            PopularListVO vo = new PopularListVO();
                            BeanUtils.copyProperties(data, vo);
                            popularList.add(vo);
                        }
                    }

            );
        }
        return popularList;
    }

    @Cacheable(value = "getMyRoomDevoteByMode", key = "#p0 + #p1 + #p2", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public long getMyRoomDevoteByMode(String uid, String roomId, int mode) {
        int start = getStartTime(mode);
        return slaveUidAidDevoteLogDao.getMyRoomDevoteByTime(roomId, uid, start);
    }

    @Cacheable(value = "getTotalRoomDevoteByMode", key = "#p0 + #p1", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public long getTotalRoomDevoteByMode(String roomId, int mode) {
        int start = getStartTime(mode);
        return slaveUidAidDevoteLogDao.calculateRoomDevote(roomId, start);
    }

    @CacheEvict(value = "getRedisUserInfoItemVOList", key = "#p0 + #p1", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public void clearRedisUserInfoItemList(String roomId, int mode) {
    }


    @CacheEvict(value = "getUserInfoItemVOList", key = "#p0 + #p1", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public void clearUserInfoItemList(String roomId, int mode) {
    }

    @CacheEvict(value = "getTotalRoomDevoteByMode", key = "#p0 + #p1", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public void clearTotalRoomDevoteByMode(String roomId, int mode) {
    }
}
