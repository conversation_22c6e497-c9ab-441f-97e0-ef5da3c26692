package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.ShareActivityDTO;
import com.quhong.data.vo.HappyTripVO;
import com.quhong.data.vo.OtherSupportUserVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.dao.ResourceConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceConfigData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.UserResourceDao;
import com.quhong.mysql.data.UserResourceData;
import com.quhong.utils.PageUtils;
import com.quhong.vo.PageVO;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class HappyTripService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(HappyTripService.class);

    private static final String MILEAGE_AVAILABLE_KEY = "mileageAvailableNum"; // 剩余可用里程数
    private static final String CONSUME_MILEAGE_KEY = "consumeMileageNum";// 消耗里程数
    private static final String TOTAL_MILEAGE_KEY = "totalMileageNum"; // 总里程数
    private static final String BID_TOTAL_NUM_KEY = "bidTotalNum"; // 拍的藏品总次数
    private static final String LAST_ROUND_WIN_INFO_RETURNED_KEY = "lastRoundWinInfoReturned%s"; // 上轮中奖是否已经返回
    private static final List<Integer> START_PRICE_ORDER_LIST = Arrays.asList(200, 100, 50, 300, 1000, 555, 111, 55);

    private static final String TEAM_ID_KEY = "teamId";                    // 队伍id-Key
    public static String ACTIVITY_ID = "kkk";
    public static String ACTIVITY_URL = String.format("https://static.youstar.live/happy_anniversary_trip/?activityId=%s", ACTIVITY_ID);
    public static final String ACTIVITY_TITLE_EN = "Happy Anniversary Trip";
    public static final String ACTIVITY_TITLE_AR = "رحلة ذكرى سعيدة";
    private static final String HAPPY_TRIP_BID_KEY = "HappyAnniversaryTripBid";    // 拍品资源key
    private static final Integer LIMIT_INIT_POOL = 30;
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final Integer MAX_TEAM_SIZE = 2;
    private static final Integer MAX_SUPPORT_USER_SIZE = 20;
    private static final int BASE_OPEN_SHOW = 50000000;
    private static final List<Integer> TEAM_SCORE_LEVEL_LIST = Arrays.asList(100, 10000, 60000, 140000, 300000, 600000);
    private static final List<String> TEAM_LEVEL_KEY_LIST = Arrays.asList("HappyAnniversaryTripReward1", "HappyAnniversaryTripReward2", "HappyAnniversaryTripReward3"
            , "HappyAnniversaryTripReward4", "HappyAnniversaryTripReward5", "HappyAnniversaryTripReward6");
    private static final List<String> TRIP_TOP_KEY_LIST = Arrays.asList("HappyAnniversaryTripTop1", "HappyAnniversaryTripTop2", "HappyAnniversaryTripTop3"
            , "HappyAnniversaryTripTop4", "HappyAnniversaryTripTop5", "HappyAnniversaryTripTop6-10", "HappyAnniversaryTripTop6-10"
            , "HappyAnniversaryTripTop6-10", "HappyAnniversaryTripTop6-10", "HappyAnniversaryTripTop6-10");
    private static final String TRIP_TEAM_SUCCESS_KEY  ="HappyAnniversaryTripTeam";
    private static final String TRIP_CONSUME_TOP_KEY  ="HappyAnniversaryTripTitle";

    private static final Interner<String> stringPool = Interners.newWeakInterner();


    @Resource
    private EventReport eventReport;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private UserResourceDao userResourceDao;
    @Resource
    private ResourceConfigDao resourceConfigDao;
    @Resource
    private FriendsDao friendsDao;


    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "68b508d48bd509865b08694d";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/happy_anniversary_trip/?activityId=%s", ACTIVITY_ID);
        }
    }


    // hash 每一轮的开奖结果,filed为roundNum
    private String getHashRoundActivityId(String activityId) {
        return String.format("happy_trip:all:activityId:%s", activityId);
    }

    private String getStrNowBidInfo(String activityId) {
        return String.format("happy_trip:bidInfo:activityId:%s", activityId);
    }

    // 个人数据信息key
    private String getHashActivityId(String activityId, String uid) {
        return String.format("happy_trip:my:%s:%s", activityId, uid);
    }

    // 个人消耗里程数key
    private String getZSetUserConsumeRankKey(String activityId) {
        return String.format("happy_trip:consumeRank:%s", activityId);
    }

    // 个人竞拍数据key
    private String getZSetUserBidRankKey(String activityId, int roundNum) {
        return String.format("happy_trip:bidRank:%s:%s", activityId, roundNum);
    }

    // 个设置提醒key set
    private String getSetUserReminderKey(String activityId, int roundNum) {
        return String.format("happy_trip:set:reminder:%s:%s", activityId, roundNum);
    }


    // 团队成员key
    private String getListTeamMemberKey(String activityId, String teamId) {
        return String.format("happy_trip:teamMember:%s:%s", activityId, teamId);
    }

    // 团队榜单key
    private String getZSetTeamRankKey(String activityId) {
        return String.format("happy_trip:teamRank:%s", activityId);
    }


    // 个人投注历史记录Key,结算后再记录
    private String getListHistoryBidKey(String activityId, String uid) {
        return String.format("happy_trip:history:user:%s:%s", activityId, uid);
    }

    // 开奖记录Key,结算后再记录
    private String getListHistoryRolley(String activityId) {
        return String.format("happy_trip:history:roll:%s", activityId);
    }


    // 限制每日分享次数
    private String getShareLimitKey(String activityId, String uid, String aid, String date) {
        return String.format("shareLimit:%s:%s:%s:%s", activityId, uid, aid);
    }

    private String getLocalUserKey(String uid) {
        return "lock:happy_trip:user:" + uid;
    }

    // 结算竞拍标志位
    private String getStrBidSettleAuction(String activityId) {
        return String.format("happy_trip:bidSettleAuction:%s", activityId);
    }

    private String getZetFriendFindKey(String activityId) {
        return String.format("happy_trip:friendFind:%s", activityId);
    }

    private String getStrFriendFindIncKey(String activityId) {
        return String.format("happy_trip:friendFind:inc:%s", activityId);
    }

    private int getCurrLevelScore(int score) {
        List<Integer> tempLevelNumList = new ArrayList<>(TEAM_SCORE_LEVEL_LIST);
        tempLevelNumList.add(0, 0);
        return getBaseIndexLevel(score, tempLevelNumList);
    }

    public HappyTripVO happyTripConfig(String activityId, String uid) {
        HappyTripVO vo = new HappyTripVO();
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
        vo.setMileageTotalNum(Integer.valueOf(userDataMap.getOrDefault(TOTAL_MILEAGE_KEY, "0")));


        String teamId = userDataMap.get(TEAM_ID_KEY);
        HappyTripVO.TripTeamConfigVO teamInfoVO = new HappyTripVO.TripTeamConfigVO();
        if (ObjectUtils.isEmpty(teamId)) {
            // 未组队成功则显示只有自己在队伍中
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            teamInfoVO.setTeamName(actorData.getName());
            teamInfoVO.setTeamLeadUid(uid);
            List<HappyTripVO.TeamInfoVO> teamMemberList = new ArrayList<>();
            HappyTripVO.TeamInfoVO teamInfo = new HappyTripVO.TeamInfoVO();
            teamInfo.setUid(uid);
            teamInfo.setName(actorData.getName());
            teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            teamMemberList.add(teamInfo);
            teamInfoVO.setTeamMemberList(teamMemberList);

        } else {
            List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), MAX_TEAM_SIZE);
            List<HappyTripVO.TeamInfoVO> teamMemberList = new ArrayList<>();
            teamInfoVO.setTeamId(teamId);
            for (int i = 0; i < teamMemberUidList.size(); i++) {
                String memberUid = teamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                if (i == 0) {
                    teamInfoVO.setTeamName(actorData.getName());
                    teamInfoVO.setTeamLeadUid(memberUid);
                }
                HappyTripVO.TeamInfoVO teamInfo = new HappyTripVO.TeamInfoVO();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                teamMemberList.add(teamInfo);
            }
            teamInfoVO.setTeamMemberList(teamMemberList);
        }
        vo.setTeamInfo(teamInfoVO);

        // 设置竞拍信息

        return vo;
    }

    public HappyTripVO happyTripTeamRank(String activityId, String uid) {
        HappyTripVO vo = new HappyTripVO();
        // 设置榜单数据
        List<HappyTripVO.TripTeamConfigVO> teamRankList = new ArrayList<>();
        Map<String, Integer> teamRankMap = activityCommonRedis.getCommonRankingMap(getZSetTeamRankKey(activityId), 10);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : teamRankMap.entrySet()) {
            String rankTeamId = entry.getKey();
            List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, rankTeamId), MAX_TEAM_SIZE);
            HappyTripVO.TripTeamConfigVO dragonTeamConfig = new HappyTripVO.TripTeamConfigVO();
            List<HappyTripVO.TeamInfoVO> teamMemberList = new ArrayList<>();
            for (int i = 0; i < teamMemberUidList.size(); i++) {
                String memberUid = teamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                if (i == 0) {
                    dragonTeamConfig.setTeamName(actorData.getName());
                    dragonTeamConfig.setTeamLeadUid(memberUid);
                }
                HappyTripVO.TeamInfoVO teamInfo = new HappyTripVO.TeamInfoVO();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                teamMemberList.add(teamInfo);
            }
            dragonTeamConfig.setTeamId(rankTeamId);
            dragonTeamConfig.setTeamScore(entry.getValue());
            dragonTeamConfig.setTeamMemberList(teamMemberList);
            dragonTeamConfig.setRank(rank);
            dragonTeamConfig.setTripLevel(getCurrLevelScore(entry.getValue()));
            teamRankList.add(dragonTeamConfig);
            rank += 1;
        }
        vo.setTeamRankList(teamRankList);

        // 设置我的队伍数据
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
        String myTeamId = userDataMap.get(TEAM_ID_KEY);
        if (!ObjectUtils.isEmpty(myTeamId)) {
            HappyTripVO.TripTeamConfigVO myTeamConfig = new HappyTripVO.TripTeamConfigVO();
            List<String> myTeamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, myTeamId), MAX_TEAM_SIZE);
            List<HappyTripVO.TeamInfoVO> teamMemberList = new ArrayList<>();
            for (int i = 0; i < myTeamMemberUidList.size(); i++) {
                String memberUid = myTeamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                String memberActivityId = getHashActivityId(activityId, memberUid);
                Map<String, String> memberDataMap = memberUid.equals(uid) ? userDataMap : activityCommonRedis.getCommonHashAllMapStr(memberActivityId);
                int trainNum = Integer.parseInt(memberDataMap.getOrDefault(TOTAL_MILEAGE_KEY, "0"));
                if (i == 0) {
                    myTeamConfig.setTeamName(actorData.getName());
                    myTeamConfig.setTeamLeadUid(memberUid);
                }
                HappyTripVO.TeamInfoVO teamInfo = new HappyTripVO.TeamInfoVO();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                teamInfo.setScore(trainNum);
                teamMemberList.add(teamInfo);
            }
            myTeamConfig.setTeamId(myTeamId);
            int myTeamScore = activityCommonRedis.getCommonZSetRankingScore(getZSetTeamRankKey(activityId), myTeamId);
            myTeamConfig.setTeamScore(myTeamScore);
            myTeamConfig.setTeamMemberList(teamMemberList);

            int myRank = activityCommonRedis.getCommonZSetRank(getZSetTeamRankKey(activityId), myTeamId);
            myTeamConfig.setRank(myRank);
            myTeamConfig.setTripLevel(getCurrLevelScore(myTeamScore));

            // 处理距离上一名分差
            int muchScore = 0;
            int lastRank = myRank - 1;
            if (lastRank > 0) {
                // 只获取上一名的数据
                Map<String, Integer> lastRankMap = activityCommonRedis.getCommonRankingMapByPage(getZSetTeamRankKey(activityId), lastRank - 1, lastRank);
                if (!CollectionUtils.isEmpty(lastRankMap)) {
                    // 只取第一个元素，即上一名的分数
                    for (Map.Entry<String, Integer> entry : lastRankMap.entrySet()) {
                        int lastScore = entry.getValue();
                        logger.info("happyTripTeamRank lastRankMap rankTeamId:{} lastScore:{} myTeamScore:{}", entry.getKey(), lastScore, myTeamScore);
                        // 计算分差：上一名分数 - 我的分数
                        muchScore = lastScore - myTeamScore;
                        break; // 只处理第一个元素
                    }
                }
            }
            myTeamConfig.setMuchScore(muchScore);
            vo.setTeamInfo(myTeamConfig);
        }
        return vo;
    }

    /**
     * 礼物发送处理
     */
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        String fromUid = giftData.getFrom_uid();
        synchronized (stringPool.intern(getLocalUserKey(fromUid))) {
            int totalBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            String hashActivityId = getHashActivityId(activityId, fromUid);
            if (totalBeans > 0) {
                activityCommonRedis.incCommonHashNum(hashActivityId, MILEAGE_AVAILABLE_KEY, totalBeans);
                activityCommonRedis.incCommonHashNum(hashActivityId, TOTAL_MILEAGE_KEY, totalBeans);

                String myTeamId = activityCommonRedis.getCommonHashStrValue(hashActivityId, TEAM_ID_KEY);
                if (!ObjectUtils.isEmpty(myTeamId)) {
                    incTeamTrainScore(activityId, myTeamId, totalBeans);
                }

                doReportSpecialItemsEvent(activityId, fromUid, 1, totalBeans, 1);
            }
        }
    }

    /**
     * 拍卖下注-竞价
     *
     * @param activityId: 活动id
     * @param uid:        用户uid
     * @param amount:     下注的里程数
     * @return vo
     */

    public HappyTripVO.BidRewardDataVO happyTripBid(String activityId, String uid, int round, int amount) {
        checkActivityTime(activityId);

        if (activityCommonRedis.getCommonStrScore(getStrBidSettleAuction(activityId)) == 1) {
            logger.info("in settlement, please wait");
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "in settlement, please wait"); // 正在结算中，请稍后下注
        }
        HappyTripVO.BidRewardDataVO vo = new HappyTripVO.BidRewardDataVO();
        // 获取当前竞拍信息
        String bidInfoStr = activityCommonRedis.getCommonStrValue(getStrNowBidInfo(activityId));
        if (ObjectUtils.isEmpty(bidInfoStr)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "auc not start");
        }
        HappyTripVO.BidInfo currentBidInfo = JSON.parseObject(bidInfoStr, HappyTripVO.BidInfo.class);
        int currentRound = currentBidInfo.getRound();
        int startPrice = currentBidInfo.getStartPrice();
        if (round != currentRound) {
            logger.info("round not match. round:{} currentRound:{}", round, currentRound);
            throw new CommonH5Exception(ActivityHttpCode.ROUND_NOT_MATCH.getCode(), "round not match");
        }

        vo.setRound(currentRound);
        vo.setStartPrice(startPrice);

        String bidRankKey = getZSetUserBidRankKey(activityId, currentRound);
        int availableMileage = 0 ;
        synchronized (stringPool.intern(getLocalUserKey(uid))) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
            availableMileage = Integer.parseInt(userDataMap.getOrDefault(MILEAGE_AVAILABLE_KEY, "0"));
            if (availableMileage <= 0 || availableMileage < amount) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER.getCode(), "الأميال غير كافية، أسرع وأرسل هدية");
            }
            // 检查出价是否高于起始价
            if (amount < startPrice) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "bid amount less than start price");
            }

            // 检查是否高于自己之前的出价
            int myPreviousBid = activityCommonRedis.getCommonZSetRankingScore(bidRankKey, uid);
            if (amount <= myPreviousBid) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "يجب أن تكون المزايدة أعلى من المرة السابقة");
            }

            // 记录竞拍出价到Redis ZSet (直接设置出价，不是增量)
            activityCommonRedis.addCommonZSetRankingScoreTime(bidRankKey, uid, amount);

            logger.info("happyTripBid success uid:{} amount:{} round:{}", uid, amount, currentRound);
        }

        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(bidRankKey, MAX_SUPPORT_USER_SIZE);
        // 计算最高价和最低价
        if (!rankingMap.isEmpty()) {
            List<Integer> prices = new ArrayList<>(rankingMap.values());
            vo.setHighestPrice(Collections.max(prices));
            if (rankingMap.size() < MAX_SUPPORT_USER_SIZE) {
                vo.setLowestPrice(Collections.min(prices));
            } else {
                Map<String, Integer> rankingAscMap = activityCommonRedis.getCommonRangeRankingMap(bidRankKey, 1);
                vo.setLowestPrice(rankingAscMap.values().iterator().next());
            }
        }

        // 构建竞价榜单
        List<OtherSupportUserVO> bidUserList = new ArrayList<>();
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String bidUid = entry.getKey();
            Integer bidPrice = entry.getValue();
            ActorData actorData = actorDao.getActorDataFromCache(bidUid);

            OtherSupportUserVO bidUser = new OtherSupportUserVO();
            bidUser.setUid(bidUid);
            bidUser.setName(actorData.getName());
            bidUser.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            bidUser.setScore(bidPrice);
            bidUserList.add(bidUser);
            rank++;
        }
        vo.setMyBidPrice(amount);
        vo.setBidUserList(bidUserList);
        vo.setMyAvailableMileage(availableMileage);

        return vo;
    }

    /**
     * 竞拍相关的信息
     *
     * @param activityId
     * @param uid
     * @return
     */
    public HappyTripVO happyTripBidInfo(String activityId, String uid) {
        HappyTripVO.BidRewardDataVO bidRewardDataVO = new HappyTripVO.BidRewardDataVO();
        List<HappyTripVO.BidRankingListVO> bidRankingList = new ArrayList<>();   // 竞拍榜单
        HappyTripVO.BidRankingListVO myBidRank = new HappyTripVO.BidRankingListVO();   // 我的竞拍排名

        // 获取当前竞拍信息
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
        String bidInfoStr = activityCommonRedis.getCommonStrValue(getStrNowBidInfo(activityId));
        if (!ObjectUtils.isEmpty(bidInfoStr)) {
            HappyTripVO.BidInfo currentBidInfo = JSON.parseObject(bidInfoStr, HappyTripVO.BidInfo.class);
            int currentRound = currentBidInfo.getRound();
            // 获取资源配置
            ResourceKeyConfigData resourceKeyConfigData = this.getResourceKeyConfig();
            if (resourceKeyConfigData != null) {
                Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList()
                        .stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(currentBidInfo.getMeteId());

                if (resourceMeta != null) {
                    BeanUtils.copyProperties(resourceMeta, bidRewardDataVO);
                    bidRewardDataVO.setRound(currentRound);
                    bidRewardDataVO.setStartPrice(currentBidInfo.getStartPrice());
                    bidRewardDataVO.setRoundEndTime(currentBidInfo.getRoundEndTime());

                    // 获取用户可用里程数
                    bidRewardDataVO.setMyAvailableMileage(Integer.parseInt(userDataMap.getOrDefault(MILEAGE_AVAILABLE_KEY, "0")));

                    // 竞价
                    String bidRankKey = getZSetUserBidRankKey(activityId, currentRound);
                    Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(bidRankKey, 20);

                    // 计算最高价和最低价
                    if (!rankingMap.isEmpty()) {
                        List<Integer> prices = new ArrayList<>(rankingMap.values());
                        bidRewardDataVO.setHighestPrice(Collections.max(prices));
                        Map<String, Integer> rankingAscMap = activityCommonRedis.getCommonRangeRankingMap(bidRankKey, 1);
                        bidRewardDataVO.setLowestPrice(rankingAscMap.values().iterator().next());
                    }

                    // 构建竞价榜单
                    List<OtherSupportUserVO> bidUserList = new ArrayList<>();
                    int rank = 1;
                    for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                        String bidUid = entry.getKey();
                        Integer bidPrice = entry.getValue();
                        ActorData actorData = actorDao.getActorDataFromCache(bidUid);

                        OtherSupportUserVO bidUser = new OtherSupportUserVO();
                        bidUser.setUid(bidUid);
                        bidUser.setName(actorData.getName());
                        bidUser.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                        bidUser.setScore(bidPrice);
                        bidUserList.add(bidUser);
                        if (bidUid.equals(uid)) {
                            bidRewardDataVO.setMyBidPrice(bidPrice);
                        }
                        rank++;
                    }
                    if (bidRewardDataVO.getMyBidPrice() == 0) {
                        bidRewardDataVO.setMyBidPrice(activityCommonRedis.getCommonZSetRankingScore(bidRankKey, uid));
                    }

                    bidRewardDataVO.setBidUserList(bidUserList);

                    // 获取后3轮拍品预告
                    List<HappyTripVO.BidRewardDataVO> nextThreeRoundList = new ArrayList<>();
                    List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = resourceKeyConfigData.getResourceMetaList();
                    for (int i = 1; i <= 3 ; i++) {
                        int nextRoundIndex = (currentRound + i - 1) % resourceMetaList.size();
                        ResourceKeyConfigData.ResourceMeta nextMeta = resourceMetaList.get(nextRoundIndex);
                        HappyTripVO.BidRewardDataVO nextRoundVO = new HappyTripVO.BidRewardDataVO();
                        BeanUtils.copyProperties(nextMeta, nextRoundVO);

                        // 设置起始价
                        nextRoundVO.setStartPrice(START_PRICE_ORDER_LIST.get(nextRoundIndex));
                        nextRoundVO.setRound(currentRound + i);
                        nextRoundVO.setRemindStatus(activityCommonRedis.isCommonSetData(getSetUserReminderKey(activityId, currentRound + i), uid));
                        nextThreeRoundList.add(nextRoundVO);
                    }
                    bidRewardDataVO.setNextThreeRoundList(nextThreeRoundList);


                    // 获取我的上一轮中奖信息
                    if (currentRound > 1) {
                        int lastRound = currentRound - 1;
                        String lastRoundResultStr = activityCommonRedis.getCommonHashStrValue(getHashRoundActivityId(activityId), String.valueOf(lastRound));
                        if (!StringUtils.isEmpty(lastRoundResultStr)) {
                            HappyTripVO.BidInfoResult lastRoundResult = JSON.parseObject(lastRoundResultStr, HappyTripVO.BidInfoResult.class);
                            String roundWinKey = String.format(LAST_ROUND_WIN_INFO_RETURNED_KEY, lastRound);
                            int lastRoundWinInfoReturned = Integer.valueOf(userDataMap.getOrDefault(roundWinKey, "0"));
                            if (lastRoundResult.getUidList().contains(uid) && lastRoundWinInfoReturned != 1) {
                                HappyTripVO.BidRewardDataVO lastRoundWinInfo = new HappyTripVO.BidRewardDataVO();
                                BeanUtils.copyProperties(resourceMeta, lastRoundWinInfo);
                                lastRoundWinInfo.setMyBidNum(lastRoundResult.getNum());
                                bidRewardDataVO.setMyLastRoundWinInfo(lastRoundWinInfo);
                                activityCommonRedis.setCommonHashData(hashActivityId, roundWinKey, "1");
                            }
                        }
                    }
                }
            }
        }

        // 构建个人消耗里程数榜单
        String consumeRankKey = getZSetUserConsumeRankKey(activityId);
        Map<String, Integer> commonRankingMap = activityCommonRedis.getCommonRankingMap(consumeRankKey, 10);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : commonRankingMap.entrySet()) {
            String bidUid = entry.getKey();
            Integer bidPrice = entry.getValue();
            ActorData actorData = actorDao.getActorDataFromCache(bidUid);
            // 构建竞拍排行榜
            HappyTripVO.BidRankingListVO rankVO = new HappyTripVO.BidRankingListVO();
            rankVO.setUid(bidUid);
            rankVO.setName(actorData.getName());
            rankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            rankVO.setScore(bidPrice);
            rankVO.setRank(rank);
            rankVO.setVipLevel(vipInfoDao.getIntVipLevelFromCache(bidUid));
            rankVO.setBadgeList(badgeDao.getBadgeList(bidUid));

            String bHashActivityId = getHashActivityId(activityId, bidUid);
            rankVO.setBidNum(activityCommonRedis.getCommonHashValue(bHashActivityId, BID_TOTAL_NUM_KEY));
            rankVO.setHonorTitleIconList(getHonorTitleIconList(bidUid));
            bidRankingList.add(rankVO);
            // 设置我的排名信息
            if (bidUid.equals(uid)) {
                BeanUtils.copyProperties(rankVO, myBidRank);
            }
            rank++;
        }

        if (myBidRank.getRank() == null) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            myBidRank.setName(actorData.getName());
            myBidRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myBidRank.setScore(activityCommonRedis.getCommonZSetRankingScore(consumeRankKey, uid));
            myBidRank.setRank(activityCommonRedis.getCommonZSetRank(consumeRankKey, uid));
            myBidRank.setVipLevel(vipInfoDao.getIntVipLevelFromCache(uid));
            myBidRank.setBadgeList(badgeDao.getBadgeList(uid));
            myBidRank.setBidNum(Integer.valueOf(userDataMap.getOrDefault(BID_TOTAL_NUM_KEY, "0")));
            myBidRank.setHonorTitleIconList(getHonorTitleIconList(uid));
        }


        HappyTripVO vo = new HappyTripVO();
        vo.setBidRewardDataVO(bidRewardDataVO);
        vo.setBidRankingList(bidRankingList);
        vo.setMyBidRank(myBidRank);
        return vo;
    }

    private List<String> getHonorTitleIconList(String uid) {
        List<UserResourceData> honorTitleList = userResourceDao.getUserHonorTitleList(uid);
        if (CollectionUtils.isEmpty(honorTitleList)) {
            return Collections.emptyList();
        }
        return honorTitleList.stream().map(item -> {
            ResourceConfigData resourceData = resourceConfigDao.getResourceDataFromCache(item.getResourceId());
            return resourceData != null ? resourceData.getIconAr() : "";
        }).collect(Collectors.toList());
    }

    /**
     * 结算竞拍-每小时一次
     */
    public void bidSettleAuction() {
        int currentTime = DateHelper.getNowSeconds();
        String activityId = ACTIVITY_ID;
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(activityId);
        if (activityData == null) {
            return;
        }
        int startTime = activityData.getStartTime();
        int endTime = activityData.getEndTime();

        if (!(currentTime >= startTime && currentTime < endTime + TimeUnit.MINUTES.toSeconds(10))) {
            return;
        }

        ResourceKeyConfigData resourceKeyConfigData = this.getResourceKeyConfig();
        if (resourceKeyConfigData == null) {
            return;
        }
        // 设置结算标志
        activityCommonRedis.setCommonStrScore(getStrBidSettleAuction(activityId), 1);
        // 获取当前竞拍信息
        String bidInfoStr = activityCommonRedis.getCommonStrValue(getStrNowBidInfo(activityId));
        if (ObjectUtils.isEmpty(bidInfoStr)) {
            // 初始化第一轮竞拍
            initFirstRoundBid(activityId, resourceKeyConfigData, currentTime);
            return;
        }

        HappyTripVO.BidInfo currentBidInfo = JSON.parseObject(bidInfoStr, HappyTripVO.BidInfo.class);
        int currentRound = currentBidInfo.getRound();
        String currentMeteId = currentBidInfo.getMeteId();
        int currentNum = currentBidInfo.getNum();
        int startPrice = currentBidInfo.getStartPrice();


        Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList()
                .stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
        ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(currentMeteId);
        if (resourceMeta == null) {
            return;
        }

        // 获取当前轮次竞拍排行榜
        String bidRankKey = getZSetUserBidRankKey(activityId, currentRound);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(bidRankKey, 5000);

        List<String> winnerUidList = new ArrayList<>();

        // 处理中奖用户
        int lowPrice = startPrice;
        int num = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String bidUid = entry.getKey();
            int bidPrice = entry.getValue();
            if (num <= currentNum) {
                lowPrice = bidPrice;
                winnerUidList.add(bidUid);

                ActorData actorData = actorDao.getActorDataFromCache(bidUid);
                String hashActivityId = getHashActivityId(activityId, bidUid);

                // 扣除实际里程数
                activityCommonRedis.incCommonHashNum(hashActivityId, MILEAGE_AVAILABLE_KEY, -bidPrice);
                doReportSpecialItemsEvent(activityId, bidUid, 2, bidPrice, 2);

                // 增加消耗里程数
                activityCommonRedis.incCommonHashNum(hashActivityId, CONSUME_MILEAGE_KEY, bidPrice);
                activityCommonRedis.incrCommonZSetRankingScoreSimple(getZSetUserConsumeRankKey(activityId), bidUid, bidPrice);

                // 增加竞拍拍中次数
                activityCommonRedis.incCommonHashNum(hashActivityId, BID_TOTAL_NUM_KEY, 1);

                // 发放奖励
                resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, "Trip-bid reward", "Trip-bid reward", "Trip-bid reward", "", "", 0);

                // 记录个人历史-中奖
                HappyTripVO.RecordDataVO recordData = new HappyTripVO.RecordDataVO();
                BeanUtils.copyProperties(resourceMeta, recordData);
                recordData.setRound(currentRound);
                recordData.setResult(1); // 中奖
                recordData.setBidPrice(bidPrice);
                recordData.setCtime(currentTime);

                String jsonRecord = JSON.toJSONString(recordData);
                activityCommonRedis.leftPushAllCommonList(getListHistoryBidKey(activityId, bidUid), Collections.singletonList(jsonRecord), 1000);
                doDrawPrizeRecordEvent(bidUid, bidPrice, 1, resourceMeta);

                logger.info("bidSettleAuction winner uid:{} round:{} bidPrice:{}", bidUid, currentRound, bidPrice);

            } else {
                // 记录个人历史-未中奖
                HappyTripVO.RecordDataVO recordData = new HappyTripVO.RecordDataVO();
                BeanUtils.copyProperties(resourceMeta, recordData);
                recordData.setRound(currentRound);
                recordData.setResult(0); // 未中奖
                recordData.setBidPrice(bidPrice);
                recordData.setCtime(currentTime);
                String jsonRecord = JSON.toJSONString(recordData);
                activityCommonRedis.leftPushAllCommonList(getListHistoryBidKey(activityId, bidUid), Collections.singletonList(jsonRecord), 1000);
            }
            num++;
        }

        // 记录开奖记录
        HappyTripVO.RecordDataVO recordData = new HappyTripVO.RecordDataVO();
        BeanUtils.copyProperties(resourceMeta, recordData);
        recordData.setRound(currentRound);
        recordData.setBidPrice(lowPrice); // 最低价
        recordData.setCtime(currentTime);
        String jsonRecord = JSON.toJSONString(recordData);
        activityCommonRedis.addCommonListRecord(getListHistoryRolley(activityId), jsonRecord);

        // 记录本轮结果到hash
        HappyTripVO.BidInfoResult bidResult = new HappyTripVO.BidInfoResult();
        BeanUtils.copyProperties(currentBidInfo, bidResult);
        bidResult.setUidList(winnerUidList);
        String resultJson = JSON.toJSONString(bidResult);
        activityCommonRedis.setCommonHashData(getHashRoundActivityId(activityId), String.valueOf(currentRound), resultJson);

        // 准备下一轮竞拍
        HappyTripVO.BidInfo nextBidInfo = setupNextRoundBid(activityId, resourceKeyConfigData, currentRound, currentTime);
        // 结算完成
        activityCommonRedis.setCommonStrScore(getStrBidSettleAuction(activityId), 0);

        // 通知设置下一轮开始提醒的用户
        if (nextBidInfo != null) {
            noticeReminderUser(activityId, nextBidInfo);
        }
        logger.info("bidSettleAuction completed round:{} winners:{}", currentRound, winnerUidList.size());
    }

    /**
     * 初始化第一轮竞拍
     */
    private void initFirstRoundBid(String activityId, ResourceKeyConfigData resourceKeyConfigData, int currentTime) {
        List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = resourceKeyConfigData.getResourceMetaList();
        if (resourceMetaList.isEmpty()) {
            return;
        }

        // 第一轮使用第一个资源
        ResourceKeyConfigData.ResourceMeta firstMeta = resourceMetaList.get(0);

        HappyTripVO.BidInfo bidInfo = new HappyTripVO.BidInfo();
        bidInfo.setRound(1);
        bidInfo.setMeteId(firstMeta.getMetaId());
        try {
            bidInfo.setNum(Integer.parseInt(firstMeta.getRateNumber()));// 使用rateNumber作为数量
        } catch (NumberFormatException e) {
            logger.error("initFirstRoundBid error:{}", e.getMessage(), e);
            bidInfo.setNum(10);
        }
        bidInfo.setStartPrice(START_PRICE_ORDER_LIST.get(0)); // 第一个起始价
        bidInfo.setRoundEndTime(currentTime + 3600); // 1小时后结束

        String bidInfoJson = JSON.toJSONString(bidInfo);
        activityCommonRedis.setCommonStrData(getStrNowBidInfo(activityId), bidInfoJson);

        logger.info("initFirstRoundBid activityId:{} meteId:{} startPrice:{}", activityId, firstMeta.getMetaId(), bidInfo.getStartPrice());
    }

    /**
     * 设置下一轮竞拍
     */
    private HappyTripVO.BidInfo setupNextRoundBid(String activityId, ResourceKeyConfigData resourceKeyConfigData, int currentRound, int currentTime) {
        List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = resourceKeyConfigData.getResourceMetaList();
        if (resourceMetaList.isEmpty()) {
            return null;
        }

        // 按资源配置顺序轮询
        int nextRoundIndex = currentRound % resourceMetaList.size();
        ResourceKeyConfigData.ResourceMeta nextMeta = resourceMetaList.get(nextRoundIndex);

        // 计算下一轮起始价，按START_PRICE_ORDER_LIST依次取得
        int priceIndex = Math.min(currentRound, START_PRICE_ORDER_LIST.size() - 1);
        int nextStartPrice = START_PRICE_ORDER_LIST.get(priceIndex);

        HappyTripVO.BidInfo nextBidInfo = new HappyTripVO.BidInfo();
        nextBidInfo.setRound(currentRound + 1);
        nextBidInfo.setMeteId(nextMeta.getMetaId());
        try {
            nextBidInfo.setNum(Integer.parseInt(nextMeta.getRateNumber()));// 使用rateNumber作为数量
        } catch (NumberFormatException e) {
            logger.error("setupNextRoundBid error:{}", e.getMessage(), e);
            nextBidInfo.setNum(10);
        }
        nextBidInfo.setStartPrice(nextStartPrice);
        nextBidInfo.setRoundEndTime(currentTime + 3600); // 1小时后结束

        String bidInfoJson = JSON.toJSONString(nextBidInfo);
        activityCommonRedis.setCommonStrData(getStrNowBidInfo(activityId), bidInfoJson);

        logger.info("setupNextRoundBid round:{} meteId:{} startPrice:{}", nextBidInfo.getRound(), nextMeta.getMetaId(), nextStartPrice);
        return nextBidInfo;
    }

    public void setRemind(String activityId, String uid, int roundNum, int status) {
        checkActivityTime(activityId);
        if (status == 0) {
            activityCommonRedis.removeCommonSetData(getSetUserReminderKey(activityId, roundNum), uid);
        } else {
            activityCommonRedis.addCommonSetData(getSetUserReminderKey(activityId, roundNum), uid);
        }
    }

    private void noticeReminderUser(String activityId, HappyTripVO.BidInfo bidInfo) {
        Set<String> uidSet = activityCommonRedis.getCommonSetMember(getSetUserReminderKey(activityId, bidInfo.getRound()));
        String bodyEn = "Starting price for this round: %s miles, limited to %s, bid now.";
        String bodyAr = "السعر الابتدائي لهذه الجولة: %s ميل، بحد %s، قم بالمزايدة الآن.";
        for (String uid : uidSet) {
            sendOfficialMsg(uid, "", "Your reserved reward is now being auctioned", "مكافأتك المحجوزة يتم بيعها بالمزاد"
                    , String.format(bodyEn, bidInfo.getStartPrice(), bidInfo.getNum()), String.format(bodyAr, bidInfo.getStartPrice(), bidInfo.getNum())
                    , ACTIVITY_URL);
        }
        logger.info("noticeReminderUser round:{} uidSet:{}", bidInfo.getRound(), uidSet.size());
    }


    private void sendOfficialMsg(String uid, String picture, String titleEn, String titleAr, String bodyEn, String bodyAr, String url
    ) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("sendOfficialMsg actor not found for uid: {}", uid);
            return;
        }
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleAr : titleEn;
        String body = slang == SLangType.ARABIC ? bodyAr : bodyEn;
        String actText = slang == SLangType.ARABIC ? "شاهد" : "View";
        commonOfficialMsg(uid, picture, 0, 0, actText, title, body, url, "");
    }

    /**
     * 增加团队分数
     */
    private void incTeamTrainScore(String activityId, String teamId, int value) {
        synchronized (stringPool.intern("incTeamTrainScore" + teamId)) {
            String teamRankKey = getZSetTeamRankKey(activityId);
            int currentNum = activityCommonRedis.getCommonZSetRankingScore(teamRankKey, teamId);
            while (value > 0) {
                List<Integer> tempLevelNumList = new ArrayList<>(TEAM_SCORE_LEVEL_LIST);
                int currentLevelIndex = 0;
                if (tempLevelNumList.contains(currentNum)) {
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum);
                } else {
                    tempLevelNumList.add(currentNum);
                    tempLevelNumList.sort(Integer::compare);
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
                }

                int upLevelIndex = currentLevelIndex + 1;
                if (upLevelIndex >= TEAM_SCORE_LEVEL_LIST.size()) {
                    activityCommonRedis.incrCommonZSetRankingScore(teamRankKey, teamId, value);
                    value = 0;
                } else {
                    int upLevelNum = TEAM_SCORE_LEVEL_LIST.get(upLevelIndex);     // 下一级的数量
                    int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
                    if (value >= needUpNum) {                                      // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
                        currentNum = currentNum + needUpNum;
                        value = value - needUpNum;
                        activityCommonRedis.incrCommonZSetRankingScore(teamRankKey, teamId, needUpNum);
                        String resKey = TEAM_LEVEL_KEY_LIST.get(upLevelIndex);
                        List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), MAX_TEAM_SIZE);
                        for (String memberUid : teamMemberUidList) {
                            resourceKeyHandlerService.sendResourceData(memberUid, resKey, "Trip-team task reward", "Trip-team task reward");
                        }
                    } else {
                        activityCommonRedis.incrCommonZSetRankingScore(teamRankKey, teamId, value);
                        value = 0;
                    }
                }
            }
        }
    }


    /**
     * 获取抽奖配置
     */
    private ResourceKeyConfigData getResourceKeyConfig() {
        return resourceKeyHandlerService.getConfigData(HAPPY_TRIP_BID_KEY);

    }

    /**
     * 历史记录
     * type 0:我的投注记录 1:开奖记录
     */
    public HappyTripVO happyTripRecord(String activityId, String uid, int page, int type) {
        HappyTripVO vo = new HappyTripVO();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;

        List<String> pageRecordList = type == 0 ? activityCommonRedis.getCommonListPageRecord(getListHistoryBidKey(activityId, uid), start, end) :
                activityCommonRedis.getCommonListPageRecord(getListHistoryRolley(activityId), start, end);
        List<HappyTripVO.RecordDataVO> recordList = new ArrayList<>();
        for (String record : pageRecordList) {
            HappyTripVO.RecordDataVO rollRecordData = JSONObject.parseObject(record, HappyTripVO.RecordDataVO.class);
            recordList.add(rollRecordData);
        }
        vo.setRecordList(recordList);
        if (pageRecordList.size() < RECORD_PAGE_SIZE) {
            vo.setNextUrl(0);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }


    /**
     * 加入团队
     */
    public void happyTripJoinTeam(String activityId, String uid, String captainUid) {
        checkActivityTime(activityId);
        synchronized (stringPool.intern("happyTripJoinTeam" + captainUid)) {
            if (ObjectUtils.isEmpty(captainUid)) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            if (captainUid.equals(uid)) {
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_LIKE.getCode(), "لا تستطيع التعاون مع نفسك");
            }

            ActorData captainActorData = actorDao.getActorDataFromCache(captainUid);
            // 队长不存在
            if (captainActorData == null) {
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_NOT_DIAMONDS.getCode(), "المستخدم الكابتن الذي تقدمت بطلب للانضمام إليه غير موجود");
            }

            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
            String teamId = userDataMap.get(TEAM_ID_KEY);
            int trainNum = Integer.parseInt(userDataMap.getOrDefault(TOTAL_MILEAGE_KEY, "0"));
            // 判断该用户是否已加入队伍
            if (!ObjectUtils.isEmpty(teamId)) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "لقد انضممت إلى فريق آخر");
            }

            String captainHashActivityId = getHashActivityId(activityId, captainUid);
            Map<String, String> captainDataMap = activityCommonRedis.getCommonHashAllMapStr(captainHashActivityId);
            String captainTeamId = captainDataMap.get(TEAM_ID_KEY);
            // 队长没有团队id, 则加入
            if (ObjectUtils.isEmpty(captainTeamId)) {
                captainTeamId = new ObjectId().toString();
                List<String> teamMemberList = new ArrayList<>();
                teamMemberList.add(captainUid);
                teamMemberList.add(uid);

                activityCommonRedis.setCommonHashData(captainHashActivityId, TEAM_ID_KEY, captainTeamId);
                activityCommonRedis.setCommonHashData(hashActivityId, TEAM_ID_KEY, captainTeamId);
                activityCommonRedis.rightPushAllCommonList(getListTeamMemberKey(activityId, captainTeamId), teamMemberList);
                // 处理团队分数及奖励
                int captainTrainNum = Integer.parseInt(captainDataMap.getOrDefault(TOTAL_MILEAGE_KEY, "0"));
                int totalScore = trainNum + captainTrainNum;
                this.incTeamTrainScore(activityId, captainTeamId, totalScore);
                doTeamRecordEvent(activityId, uid, captainUid, captainActorData.getName());
                doTeamRecordEvent(activityId, captainUid, captainUid, captainActorData.getName());
                logger.info("happyTripJoinTeam1 captainTeamId:{}, captainUid:{}, uid:{}, totalScore:{}", captainTeamId, captainUid, uid, totalScore);
            } else {
                String teamMemberKey = getListTeamMemberKey(activityId, captainTeamId);
                List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(teamMemberKey, MAX_TEAM_SIZE);
                if (CollectionUtils.isEmpty(teamMemberUidList)) {
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "الفريق الذي طلبت الانضمام إليه غير موجود");
                }
                if (teamMemberUidList.size() >= MAX_TEAM_SIZE) {
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "الفريق الذي تتقدم بطلب الانضمام إليه ممتلئ");
                }
                String originCaptainUid = teamMemberUidList.get(0);
                teamMemberUidList.add(uid);
                activityCommonRedis.setCommonHashData(hashActivityId, TEAM_ID_KEY, captainTeamId);
                activityCommonRedis.addRightCommonListData(teamMemberKey, uid);
                this.incTeamTrainScore(activityId, captainTeamId, trainNum);
                doTeamRecordEvent(activityId, uid, originCaptainUid, captainActorData.getName());
                logger.info("happyTripJoinTeam2 captainTeamId:{}, captainUid:{}, uid:{}", captainTeamId, originCaptainUid, uid);
                if (teamMemberUidList.size() == MAX_TEAM_SIZE) {
                    for (String memberUid : teamMemberUidList) {
                        resourceKeyHandlerService.sendResourceData(memberUid, TRIP_TEAM_SUCCESS_KEY, "Trip-team reward", "Trip-team reward");
                    }
                }
            }

            // HappyTripVO vo = new HappyTripVO();
            // vo.setMileageTotalNum(trainNum);
            // return vo;
        }


    }

    /**
     * 下发团队奖励与投注消耗里程奖励
     */
    public void distributionTotalRanking(String activityId) {
        try {
            // 下发团队奖励top10
            Map<String, Integer> teamRankMap = activityCommonRedis.getCommonRankingMap(getZSetTeamRankKey(activityId), 10);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : teamRankMap.entrySet()) {
                String teamId = entry.getKey();
                List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), MAX_TEAM_SIZE);
                String resourceKey = TRIP_TOP_KEY_LIST.get(rank - 1);
                for (String rankUid : teamMemberUidList) {
                    resourceKeyHandlerService.sendResourceData(rankUid, resourceKey, "Trip-team top reward", "Trip-team top reward");
                }
                rank += 1;
            }

            // 下发投注消耗里程奖励
            Map<String, Integer> consumeRankMap = activityCommonRedis.getCommonRankingMap(getZSetUserConsumeRankKey(activityId), 3);
            for (Map.Entry<String, Integer> entry : consumeRankMap.entrySet()) {
                String uid = entry.getKey();
                resourceKeyHandlerService.sendResourceData(uid, TRIP_CONSUME_TOP_KEY, "Trip-bid top reward", "Trip-bid top reward");
            }


        } catch (Exception e) {
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }

    /**
     * 私信邀请好友
     */
    public void happyTripMsgShare(ShareActivityDTO dto) {
        if (StringUtils.isEmpty(dto.getActivity_id()) || StringUtils.isEmpty(dto.getAid())) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        checkActivityTime(dto.getActivity_id());
        String uid = dto.getUid(); // 邀请者 ，需要加入的队伍
        String activityId = dto.getActivity_id();
        String aid = dto.getAid(); // 被邀请者
        if (uid.equals(aid)) {
            logger.info("happyTripMsgShare uid equals aid activityId:{}", activityId);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "لا يمكنك دعوة نفسك");// 不可以邀请自己
        }

        if (!friendsDao.isFriend(uid, aid)) {
            logger.info("happyTripMsgShare not friend activityId:{}", activityId);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "أضفه كصديق أولاً ثم قم بدعوته");
        }

        String hashActivityId = getHashActivityId(activityId, uid);
        String teamId = activityCommonRedis.getCommonHashStrValue(hashActivityId, TEAM_ID_KEY);

        if (!ObjectUtils.isEmpty(teamId)) {
            String teamMemberKey = getListTeamMemberKey(activityId, teamId);
            List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(teamMemberKey, 3);
            if (teamMemberUidList.contains(aid)) {
                logger.info("happyTripMsgShare teamMemberUidList.contains(aid) activityId:{} aid:{}", activityId, aid);
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "أنتم بالفعل زملاء في الفريق");
            }

            if (teamMemberUidList.size() >= MAX_TEAM_SIZE) {
                logger.info("happyTripMsgShare teamMemberUidList.size() >= 3 activityId:{}", activityId);
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "عضو فريق الصديق ممتلئ");
            }
        }

        String hashAidActivityId = getHashActivityId(activityId, aid);
        String teamAidId = activityCommonRedis.getCommonHashStrValue(hashAidActivityId, TEAM_ID_KEY);
        if (!ObjectUtils.isEmpty(teamAidId)) {
            logger.info("happyTripMsgShare teamAidId is not empty activityId:{}", activityId);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "لقد انضم هذا الصديق الي فريق آخر");
        }

        String shareLimitKey = getShareLimitKey(activityId, uid, aid, DateHelper.ARABIAN.formatDateInDay());
        String shareLimit = activityCommonRedis.getCommonStrValue(shareLimitKey);
        if (!StringUtils.isEmpty(shareLimit)) {
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE.getCode(), "لقد قمت بدعوته/ها اليوم , يرجي المحاولة غدا.");
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("activity_id", dto.getActivity_id());
        jsonObject.put("activity_name", dto.getActivity_name());
        jsonObject.put("activity_desc", dto.getActivity_desc());
        jsonObject.put("activity_icon", dto.getActivity_icon());
        jsonObject.put("activity_banner", dto.getActivity_banner());
        jsonObject.put("activity_url", dto.getActivity_url());
        sendActivityShareMsg(dto.getUid(), dto.getAid(), jsonObject);
        activityCommonRedis.setCommonStrScore(shareLimitKey, 1);

    }

    /**
     *添加到推荐列表
     * @param activityId
     * @param uid
     * @param state 1:加入 2:移除
     */
    public void addToRecommendList(String activityId, String uid,int state) {
        checkActivityTime(activityId);
        String findListKey = getZetFriendFindKey(activityId);
        int oldTime = activityCommonRedis.getCommonZSetRankingScore(findListKey, uid);
        if (oldTime != 0) {
            int targetNum = 0;
            if (state == 1 && oldTime < BASE_OPEN_SHOW) {
                targetNum = BASE_OPEN_SHOW + oldTime;
            } else if (state == 2 && oldTime > BASE_OPEN_SHOW) {
                targetNum = oldTime - BASE_OPEN_SHOW;
            }
            if (targetNum != 0) {
                activityCommonRedis.addCommonZSetRankingScore(findListKey, uid, targetNum);
            }
        } else {
            int nowNum = (int) activityCommonRedis.incCommonStrScore(getStrFriendFindIncKey(activityId), 1);
            int targetNum = BASE_OPEN_SHOW + nowNum;
            activityCommonRedis.addCommonZSetRankingScore(findListKey, uid, targetNum);
        }

    }

    public PageVO<OtherSupportUserVO> recommendUserList(String activityId, String uid, int page) {
        List<OtherSupportUserVO> recommendUserList = new ArrayList<>();
        page = page <= 0 ? 1 : page;
        int pageSize = 4;
        int start = (page - 1) * pageSize;
        String findListKey = getZetFriendFindKey(activityId);
        Map<String, Integer> rankingMap = activityCommonRedis.getOtherRankingMapByScoreAPage(findListKey, BASE_OPEN_SHOW,
                BASE_OPEN_SHOW * 2, start, pageSize);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String findUid = entry.getKey();
            if (uid.equals(findUid)) {
                continue;
            }
            ActorData actorData = actorDao.getActorDataFromCache(findUid);
            if (actorData == null) {
                continue;
            }
            OtherSupportUserVO vo = new OtherSupportUserVO();
            vo.setUid(findUid);
            vo.setName(actorData.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            recommendUserList.add(vo);
        }
        PageVO<OtherSupportUserVO> pageVO = new PageVO<>();
        pageVO.setList(recommendUserList);
        pageVO.setNextUrl(String.valueOf(rankingMap.size() == pageSize ? page + 1 : -1));
        return pageVO;
    }

    public OtherSupportUserVO searchUser(String activityId, String uid, String keyword) {
        OtherSupportUserVO vo = new OtherSupportUserVO();
        if (StringUtils.isEmpty(keyword)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorByStrRid(keyword);
        if (actorData == null) {
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_FIND_EXIST);
        }
        vo.setUid(actorData.getUid());
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        return vo;
    }

    public void happyTripAddFriendApply(String activityId, String uid, String aid, String msg) {
        // 调用 commonAddFriendApply
    }

    /**
     * 上报里程数量
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, int action, int num, int source) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_name("Happy Anniversary Trip");
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        eventReport.track(new EventDTO(event));
    }

    /**
     * 上报中奖记录
     */
    private void doDrawPrizeRecordEvent(String uid, int costNum, int amount, ResourceKeyConfigData.ResourceMeta resourceMeta) {
        DrawPrizeRecordEvent event = new DrawPrizeRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence("Happy Anniversary Trip");
        event.setSence_detail(1);
        event.setTicket_type(0);
        event.setCost_ticket(costNum);
        event.setDraw_nums(amount);
        event.setDraw_success_nums(amount);
        event.setDraw_result(JSONObject.toJSONString(resourceMeta));
        eventReport.track(new EventDTO(event));
    }


    /**
     * 上报组队情况
     */
    private void doTeamRecordEvent(String activityId, String uid, String captainUid, String teamName) {
        TeamRecordEvent event = new TeamRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name("Happy Anniversary Trip");
        event.setActive_id(activityId);
        event.setTeam_captain_uid(captainUid);
        event.setTeam_name(teamName);
        eventReport.track(new EventDTO(event));
    }


}
