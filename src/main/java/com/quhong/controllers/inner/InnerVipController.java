package com.quhong.controllers.inner;

import com.alibaba.fastjson.JSON;
import com.quhong.data.dto.VipCardActivateDTO;
import com.quhong.data.vo.VipV2BuyVO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.VipV2BuyDTO;
import com.quhong.dto.VipV2ChargeDTO;
import com.quhong.handler.BaseController;
import com.quhong.service.VipService;
import com.quhong.service.VipV2Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "inner/${baseUrl}/", produces = MediaType.APPLICATION_JSON_VALUE)
public class InnerVipController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(InnerVipController.class);

    @Resource
    private VipService vipService;
    @Resource
    private VipV2Service vipV2Service;

    /**
     * 旧版本admin赠送VIP接口
     */
    @RequestMapping("vipChargeOld")
    private HttpResult<?> vipChargeOld(@RequestBody VipV2ChargeDTO req) {
        logger.info("inner old vipCharge uid={} req={}", req.getUid(), JSON.toJSONString(req));
        vipService.vipCharge(req);
        return HttpResult.getOk();
    }

    /**
     * admin赠送VIP接口
     */
    @RequestMapping("vipCharge")
    private HttpResult<VipV2BuyVO> vipCharge(@RequestBody VipV2ChargeDTO req) {
        logger.info("inner vipCharge uid={} req={}", req.getUid(), JSON.toJSONString(req));
        return HttpResult.getOk(vipV2Service.vipCharge(req));
    }

    /**
     * 性别修改时, vip修改接口
     */
    @RequestMapping("vipChange")
    private HttpResult<VipV2BuyVO> vipChange(@RequestBody VipV2BuyDTO req) {
        logger.info("inner vipChange uid={} req={}", req.getUid(), JSON.toJSONString(req));
        return HttpResult.getOk(vipV2Service.vipChange(req));
    }


    /**
     * vip修改activateVipCard
     */
    @RequestMapping("activateVipCard")
    private HttpResult<VipV2BuyVO> activateVipCard(@RequestBody VipV2BuyDTO req) {
        logger.info("inner activateVipCard uid={} req={}", req.getUid(), JSON.toJSONString(req));
        VipCardActivateDTO dto = new VipCardActivateDTO();
        dto.setUid(req.getUid());
        dto.setId(req.getId());
        dto.setResourceId(req.getResourceId());
        return HttpResult.getOk(vipV2Service.activateVipCard(dto));
    }

    /**
     * vip修改接口
     */
    @RequestMapping("vipRemove")
    private HttpResult<?> vipRemove(@RequestBody VipV2BuyDTO req) {
        logger.info("inner vipRemove uid={} req={}", req.getUid(), JSON.toJSONString(req));
        vipV2Service.vipRemove(req);
        return HttpResult.getOk();
    }
}
